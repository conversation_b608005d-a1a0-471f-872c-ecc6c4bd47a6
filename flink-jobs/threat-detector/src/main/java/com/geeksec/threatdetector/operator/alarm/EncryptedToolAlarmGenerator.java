package com.geeksec.threatdetector.operator.alarm;

import com.geeksec.threatdetector.model.event.alarm.AlarmEvent;
import com.geeksec.threatdetector.model.event.protocol.SslEvent;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.streaming.api.datastream.DataStream;

import java.util.*;

/**
 * 加密工具告警生成器
 * 负责将加密工具检测事件转换为告警事件
 *
 * <AUTHOR>
 * @since 2023/01/01
 */
@Slf4j
public class EncryptedToolAlarmGenerator extends AbstractThreatAlarmGenerator<SslEvent> {
    
    private static final String ALERT_TYPE = "ENCRYPTED_TOOL_DETECTED";
    private static final String ALERT_NAME = "加密工具检测";
    private static final String ALERT_LEVEL = "HIGH";
    
    // 加密工具类型
    public enum EncryptedToolType {
        METASPLOIT("Metasploit框架"),
        COBALT_STRIKE("CobaltStrike框架"),
        RAT("远程访问木马"),
        C2_FRAMEWORK("命令与控制框架"),
        SSL_ATTACK_TOOL("SSL攻击工具"),
        UNKNOWN("未知加密工具");
        
        private final String description;
        
        EncryptedToolType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    private final EncryptedToolType toolType;
    private final String subType;
    
    /**
     * 构造函数
     *
     * @param toolType 加密工具类型
     * @param subType 子类型（具体工具名称）
     */
    public EncryptedToolAlarmGenerator(EncryptedToolType toolType, String subType) {
        this.toolType = toolType;
        this.subType = subType;
    }
    
    @Override
    protected boolean detectThreat(SslEvent event) {
        // 根据加密工具类型检查是否需要生成告警
        switch (toolType) {
            case METASPLOIT:
                return isMetasploit(event);
            case COBALT_STRIKE:
                return isCobaltStrike(event);
            case RAT:
                return isRat(event);
            case C2_FRAMEWORK:
                return isC2Framework(event);
            case SSL_ATTACK_TOOL:
                return isSslAttackTool(event);
            default:
                return false;
        }
    }
    
    @Override
    protected AlarmEvent convertToAlarmEvent(SslEvent event) {
        // 创建告警描述和解决方案
        String description = String.format("检测到%s通信行为，可能存在恶意攻击活动", 
                subType != null && !subType.isEmpty() ? subType : toolType.getDescription());
        
        String solution = "1. 立即隔离涉及的主机\n" +
                "2. 检查源IP和目标IP之间的SSL通信\n" +
                "3. 分析系统是否存在其他入侵迹象\n" +
                "4. 加强网络边界防护，限制非标准协议通信\n" +
                "5. 部署深度包检测设备，识别和阻断恶意工具通信";
        
        // 创建基础告警事件
        AlarmEvent alertEvent = createBaseAlertEvent(
                ALERT_TYPE + "_" + toolType.name(),
                ALERT_NAME + " - " + (subType != null && !subType.isEmpty() ? subType : toolType.getDescription()),
                ALERT_LEVEL,
                description,
                solution,
                event.getSourceIp(),
                event.getSourcePort(),
                event.getDestinationIp(),
                event.getDestinationPort()
        );
        
        // 添加详细信息
        Map<String, Object> details = alertEvent.getDetails();
        details.put("toolType", toolType.name());
        details.put("toolDescription", toolType.getDescription());
        details.put("subType", subType);
        details.put("protocol", event.getProtocol());
        details.put("sessionId", event.getSessionId());
        
        // 添加SSL特征信息
        details.put("sslVersion", event.getSslVersion());
        details.put("cipherSuite", event.getCipherSuite());
        details.put("clientFingerprint", event.getClientFingerprint());
        details.put("serverFingerprint", event.getServerFingerprint());
        details.put("clientHeartbeatSize", event.getClientHeartbeatSize());
        details.put("serverHeartbeatSize", event.getServerHeartbeatSize());
        
        // 添加证书信息
        Map<String, String> certificate = event.getCertificate();
        if (certificate != null && !certificate.isEmpty()) {
            details.put("certificate", certificate);
        }
        
        // 添加攻击家族信息
        List<Map<String, String>> attackFamily = new ArrayList<>();
        Map<String, String> family = new HashMap<>();
        family.put("key", toolType.getDescription());
        family.put("value", subType != null && !subType.isEmpty() ? subType : toolType.getDescription());
        attackFamily.add(family);
        details.put("attack_family", attackFamily);
        
        // 添加攻击目标信息
        List<Map<String, Object>> targets = new ArrayList<>();
        Map<String, Object> target = new HashMap<>();
        target.put("target_type", "网络服务器");
        target.put("target_ip", event.getDestinationIp());
        targets.add(target);
        details.put("targets", targets);
        
        return alertEvent;
    }
    
    /**
     * 检查是否为Metasploit
     */
    private boolean isMetasploit(SslEvent event) {
        // 检查指纹特征
        String clientFingerprint = event.getClientFingerprint();
        String serverFingerprint = event.getServerFingerprint();
        
        // Metasploit的客户端和服务器指纹特征
        boolean fingerprintMatch = "7497521005006602398".equals(clientFingerprint) || 
                                  "4066811212910162455".equals(serverFingerprint);
        
        // 检查证书特征
        Map<String, String> certificate = event.getCertificate();
        if (certificate != null && !certificate.isEmpty()) {
            String issuer = certificate.get("issuer");
            String subject = certificate.get("subject");
            
            // Metasploit通常使用自签名证书
            boolean certMatch = (issuer != null && subject != null && issuer.equals(subject)) ||
                               (issuer != null && issuer.contains("Metasploit")) ||
                               (subject != null && subject.contains("Metasploit"));
            
            return fingerprintMatch || certMatch;
        }
        
        return fingerprintMatch;
    }
    
    /**
     * 检查是否为CobaltStrike
     */
    private boolean isCobaltStrike(SslEvent event) {
        // 检查指纹特征
        String clientFingerprint = event.getClientFingerprint();
        String serverFingerprint = event.getServerFingerprint();
        
        // CobaltStrike的客户端和服务器指纹特征
        boolean fingerprintMatch = "1709099497236886578".equals(clientFingerprint) || 
                                  "5182306150814891391".equals(serverFingerprint);
        
        // 检查证书特征
        Map<String, String> certificate = event.getCertificate();
        if (certificate != null && !certificate.isEmpty()) {
            String issuer = certificate.get("issuer");
            String subject = certificate.get("subject");
            
            // CobaltStrike通常使用特定的证书特征
            boolean certMatch = (issuer != null && issuer.contains("Cobalt")) ||
                               (subject != null && subject.contains("Strike"));
            
            return fingerprintMatch || certMatch;
        }
        
        return fingerprintMatch;
    }
    
    /**
     * 检查是否为RAT
     */
    private boolean isRat(SslEvent event) {
        // 检查指纹特征
        String clientFingerprint = event.getClientFingerprint();
        String serverFingerprint = event.getServerFingerprint();
        
        // 常见RAT的指纹特征列表
        List<String> ratClientFingerprints = Arrays.asList(
                "3925667531385615969", // Gh0st RAT
                "8302301121792394103", // PoisonIvy
                "5789521357723991306"  // DarkComet
        );
        
        List<String> ratServerFingerprints = Arrays.asList(
                "6482336649921511552", // Gh0st RAT
                "4066811212910162455", // PoisonIvy
                "7497521005006602398"  // DarkComet
        );
        
        // 检查是否匹配已知RAT指纹
        if (clientFingerprint != null && ratClientFingerprints.contains(clientFingerprint)) {
            return true;
        }
        
        if (serverFingerprint != null && ratServerFingerprints.contains(serverFingerprint)) {
            return true;
        }
        
        // 检查证书特征
        Map<String, String> certificate = event.getCertificate();
        if (certificate != null && !certificate.isEmpty()) {
            String issuer = certificate.get("issuer");
            String subject = certificate.get("subject");
            
            // RAT通常使用自签名证书
            if (issuer != null && subject != null && issuer.equals(subject)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 检查是否为C2框架
     */
    private boolean isC2Framework(SslEvent event) {
        // 检查指纹特征
        String clientFingerprint = event.getClientFingerprint();
        String serverFingerprint = event.getServerFingerprint();
        
        // 常见C2框架的指纹特征列表
        List<String> c2ClientFingerprints = Arrays.asList(
                "4418072496022778490", // Empire
                "3703381180726290438", // Merlin
                "2957920661169905619"  // Covenant
        );
        
        List<String> c2ServerFingerprints = Arrays.asList(
                "8829655996777896182", // Empire
                "1142710092471348808", // Merlin
                "7497521005006602398"  // Covenant
        );
        
        // 检查是否匹配已知C2框架指纹
        if (clientFingerprint != null && c2ClientFingerprints.contains(clientFingerprint)) {
            return true;
        }
        
        if (serverFingerprint != null && c2ServerFingerprints.contains(serverFingerprint)) {
            return true;
        }
        
        // 检查证书特征
        Map<String, String> certificate = event.getCertificate();
        if (certificate != null && !certificate.isEmpty()) {
            String serialNumber = certificate.get("serialNumber");
            
            // Empire框架特征证书序列号
            if ("8ac729b92c4d6af64225faa43ebf9612238bc97".equals(serialNumber)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 检查是否为SSL攻击工具
     */
    private boolean isSslAttackTool(SslEvent event) {
        // 检查指纹特征
        String clientFingerprint = event.getClientFingerprint();
        
        // THC-SSL-DOS特征指纹
        if ("8033014089335809447".equals(clientFingerprint)) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 创建加密工具检测告警生成数据流
     */
    public static DataStream<AlarmEvent> generateEncryptedToolAlarms(
            DataStream<SslEvent> sslEvents, 
            EncryptedToolType toolType,
            String subType) {
        // TODO: EncryptedToolToAlarmFunction class is missing. Temporarily commenting out and returning null.
        // return sslEvents
        //         .flatMap(new EncryptedToolToAlarmFunction(toolType, subType))
        //         .name(toolType.getDescription() + (subType != null && !subType.isEmpty() ? " - " + subType : "") + "告警生成")
        //         .setParallelism(2);
        return null;
    }
}
