package com.geeksec.threatdetector.model.event;

import java.util.Map;

/**
 * 基础事件类
 */

public abstract class BaseEvent {

    public String getEventId() {
        return eventId;
    }

    public Map<String, Object> getMetadata() {
        return metadata;
    }
    private String eventId;
    private long timestamp;
    private String sourceIp;
    private int sourcePort;
    private String destinationIp;
    private int destinationPort;
    private String protocol;
    private Map<String, Object> metadata;
    
    public abstract String getEventType();
    
    // Explicit getters for compatibility
    public String getProtocol() {
        return protocol;
    }
    
    public String getSourceIp() {
        return sourceIp;
    }
    
    public int getSourcePort() {
        return sourcePort;
    }
    
    public String getDestinationIp() {
        return destinationIp;
    }
    
    public int getDestinationPort() {
        return destinationPort;
    }
    
    public long getTimestamp() {
        return timestamp;
    }
    
    // 显式添加 setter 方法以解决编译问题
    public void setEventId(String eventId) {
        this.eventId = eventId;
    }
    
    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }
    
    public void setSourceIp(String sourceIp) {
        this.sourceIp = sourceIp;
    }
    
    public void setSourcePort(int sourcePort) {
        this.sourcePort = sourcePort;
    }
    
    public void setDestinationIp(String destinationIp) {
        this.destinationIp = destinationIp;
    }
    
    public void setDestinationPort(int destinationPort) {
        this.destinationPort = destinationPort;
    }
    
    public void setProtocol(String protocol) {
        this.protocol = protocol;
    }
    
    public void setMetadata(Map<String, Object> metadata) {
        this.metadata = metadata;
    }
}
