package com.geeksec.threatdetector.model.protocol;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/9/9
 *
 */

@Data
public class SslSimpleInfo {
    /**
     * cSSLVersion
     */
    private String cSslVersion;

    /**
     * sSSLVersion
     */
    private String sSslVersion;

    /**
     * sCertHash
     */
    private List<String> sCertHash;

    /**
     * dCertHash
     */
    private List<String> dCertHash;

    /**
     * sSSLFinger
     */
    private String sSslFinger;

    /**
     * dSSLFinger
     */
    private String dSslFinger;

    public SslSimpleInfo(Map<String,Object> pbMap) {
        this.cSslVersion = pbMap.get("cSSLVersion").toString();
        this.sSslVersion = pbMap.get("sSSLVersion").toString();
        this.sCertHash = (List<String>) pbMap.get("sCertHash");
        this.dCertHash = (List<String>) pbMap.get("dCertHash");
        this.sSslFinger = (String) pbMap.get("sSSLFinger");
        this.dSslFinger = (String) pbMap.get("dSSLFinger");
    }
}
