package com.geeksec.threatdetector.io.serializer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.serialization.SerializationSchema;

import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * 告警日志值序列化器
 * <AUTHOR>
 * @Date 2024/11/27
 */
@Slf4j
public class AlarmLogValueSerializationSchema implements SerializationSchema<Map<String, Object>> {
    private static final long serialVersionUID = 1L;

    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public byte[] serialize(Map<String, Object> alarmLog) {
        try {
            return objectMapper.writeValueAsString(alarmLog).getBytes(StandardCharsets.UTF_8);
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize alarm log: {}", alarmLog, e);
            return new byte[0];
        }
    }
}
