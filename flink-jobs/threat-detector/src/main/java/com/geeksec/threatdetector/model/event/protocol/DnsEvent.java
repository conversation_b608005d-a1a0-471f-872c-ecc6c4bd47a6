package com.geeksec.threatdetector.model.event.protocol;

import java.util.List;
import java.util.Map;

/**
 * DNS事件模型
 *
 * <AUTHOR>
 */
public class DnsEvent extends ProtocolEvent {
    
    /**
     * 查询ID
     */
    private Integer queryId;
    
    /**
     * 查询名称
     */
    private String queryName;
    
    /**
     * 查询类型
     */
    private String queryType;
    
    /**
     * 查询类
     */
    private String queryClass;
    
    /**
     * 响应码
     */
    private Integer responseCode;
    
    /**
     * 是否递归
     */
    private Boolean recursive;
    
    /**
     * 是否截断
     */
    private Boolean truncated;
    
    /**
     * 是否权威回答
     */
    private Boolean authoritative;
    
    /**
     * 问题数
     */
    private Integer questionCount;
    
    /**
     * 回答数
     */
    private Integer answerCount;
    
    /**
     * 权威记录数
     */
    private Integer authorityCount;
    
    /**
     * 附加记录数
     */
    private Integer additionalCount;
    
    /**
     * 回答记录
     */
    private List<Map<String, String>> answers;
    
    /**
     * 权威记录
     */
    private List<Map<String, String>> authorities;
    
    /**
     * 附加记录
     */
    private List<Map<String, String>> additionals;

    public Integer getQueryId() {
        return queryId;
    }

    public void setQueryId(Integer queryId) {
        this.queryId = queryId;
    }

    public String getQueryName() {
        return queryName;
    }

    public void setQueryName(String queryName) {
        this.queryName = queryName;
    }

    public String getQueryType() {
        return queryType;
    }

    public void setQueryType(String queryType) {
        this.queryType = queryType;
    }

    public String getQueryClass() {
        return queryClass;
    }

    public void setQueryClass(String queryClass) {
        this.queryClass = queryClass;
    }

    public Integer getResponseCode() {
        return responseCode;
    }

    public void setResponseCode(Integer responseCode) {
        this.responseCode = responseCode;
    }

    public Boolean getRecursive() {
        return recursive;
    }

    public void setRecursive(Boolean recursive) {
        this.recursive = recursive;
    }

    public Boolean getTruncated() {
        return truncated;
    }

    public void setTruncated(Boolean truncated) {
        this.truncated = truncated;
    }

    public Boolean getAuthoritative() {
        return authoritative;
    }

    public void setAuthoritative(Boolean authoritative) {
        this.authoritative = authoritative;
    }

    public Integer getQuestionCount() {
        return questionCount;
    }

    public void setQuestionCount(Integer questionCount) {
        this.questionCount = questionCount;
    }

    public Integer getAnswerCount() {
        return answerCount;
    }

    public void setAnswerCount(Integer answerCount) {
        this.answerCount = answerCount;
    }

    public Integer getAuthorityCount() {
        return authorityCount;
    }

    public void setAuthorityCount(Integer authorityCount) {
        this.authorityCount = authorityCount;
    }

    public Integer getAdditionalCount() {
        return additionalCount;
    }

    public void setAdditionalCount(Integer additionalCount) {
        this.additionalCount = additionalCount;
    }

    public List<Map<String, String>> getAnswers() {
        return answers;
    }

    public void setAnswers(List<Map<String, String>> answers) {
        this.answers = answers;
    }

    public List<Map<String, String>> getAuthorities() {
        return authorities;
    }

    public void setAuthorities(List<Map<String, String>> authorities) {
        this.authorities = authorities;
    }

    public List<Map<String, String>> getAdditionals() {
        return additionals;
    }

    public void setAdditionals(List<Map<String, String>> additionals) {
        this.additionals = additionals;
    }


    public String getRcodeName() {
        Integer rcode = getResponseCode();
        if (rcode == null) {
            return "UNKNOWN_RCODE_NULL";
        }
        switch (rcode) {
            case 0: return "NOERROR";
            case 1: return "FORMERR";
            case 2: return "SERVFAIL";
            case 3: return "NXDOMAIN";
            case 4: return "NOTIMP";
            case 5: return "REFUSED";
            case 6: return "YXDOMAIN";
            case 7: return "YXRRSET";
            case 8: return "NXRRSET";
            case 9: return "NOTAUTH";
            case 10: return "NOTZONE";
            default:
                return "UNKNOWN_RCODE_" + rcode;
        }
    }

    @Override
    public String toString() {
        return "DnsEvent{" +
                "queryId=" + queryId +
                ", queryName='" + queryName + '\'' +
                ", queryType='" + queryType + '\'' +
                ", responseCode=" + responseCode +
                ", sourceIp='" + getSourceIp() + '\'' +
                ", destinationIp='" + getDestinationIp() + '\'' +
                '}';
    }
}
