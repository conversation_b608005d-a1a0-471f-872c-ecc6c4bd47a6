package com.geeksec.threatdetector.operator.analysis.encrypted;

import com.geeksec.threatdetector.model.event.alarm.AlarmEvent;
import com.geeksec.threatdetector.model.event.protocol.SslEvent;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 命令与控制(C2)框架检测函数
 *
 * <AUTHOR>
 */
/**
 * 命令与控制(C2)框架检测器
 * 用于检测各种C2框架的SSL/TLS流量特征
 *
 * <AUTHOR>
 * @since 2023/01/01
 */
public class C2FrameworkDetector extends RichFlatMapFunction<SslEvent, AlarmEvent> {
    
    private static final Logger logger = LoggerFactory.getLogger(C2FrameworkDetector.class);
    private static final String C2_ALERT_TYPE = "C2_FRAMEWORK_DETECTED";
    private static final String C2_ALERT_NAME = "命令与控制框架检测";
    private static final String C2_ALERT_LEVEL = "HIGH";
    
    // Empire框架特征证书序列号
    private static final String EMPIRE_SERIAL_NUMBER = "8ac729b92c4d6af64225faa43ebf9612238bc97";
    
    @Override
    public void flatMap(SslEvent sslEvent, Collector<AlarmEvent> out) throws Exception {
        try {
            // 检测Empire框架
            if (isEmpire(sslEvent)) {
                AlarmEvent alert = createAlertEvent(sslEvent, "EMPIRE", "Empire框架");
                out.collect(alert);
                logger.warn("检测到Empire框架: {}:{} -> {}:{}", 
                        sslEvent.getSourceIp(), sslEvent.getSourcePort(),
                        sslEvent.getDestinationIp(), sslEvent.getDestinationPort());
            }
            
            // 检测Merlin C2
            if (isMerlin(sslEvent)) {
                AlarmEvent alert = createAlertEvent(sslEvent, "MERLIN", "Merlin C2框架");
                out.collect(alert);
                logger.warn("检测到Merlin C2框架: {}:{} -> {}:{}", 
                        sslEvent.getSourceIp(), sslEvent.getSourcePort(),
                        sslEvent.getDestinationIp(), sslEvent.getDestinationPort());
            }
            
            // 检测PyFud
            if (isPyFud(sslEvent)) {
                AlarmEvent alert = createAlertEvent(sslEvent, "PYFUD", "PyFud框架");
                out.collect(alert);
                logger.warn("检测到PyFud框架: {}:{} -> {}:{}", 
                        sslEvent.getSourceIp(), sslEvent.getSourcePort(),
                        sslEvent.getDestinationIp(), sslEvent.getDestinationPort());
            }
            
        } catch (Exception e) {
            logger.error("C2框架检测错误: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 检测是否为Empire框架
     */
    private boolean isEmpire(SslEvent sslEvent) {
        Map<String, String> certificate = sslEvent.getCertificate();
        if (certificate == null) {
            return false;
        }
        
        // 检查证书序列号
        String serialNumber = certificate.get("serialNumber");
        if (!EMPIRE_SERIAL_NUMBER.equals(serialNumber)) {
            return false;
        }
        
        // 检查指纹特征
        String clientFingerprint = sslEvent.getClientFingerprint();
        String serverFingerprint = sslEvent.getServerFingerprint();
        
        return "4418072496022778490".equals(clientFingerprint) && "8829655996777896182".equals(serverFingerprint);
    }
    
    /**
     * 检测是否为Merlin C2
     */
    private boolean isMerlin(SslEvent sslEvent) {
        // 检查指纹特征
        String clientFingerprint = sslEvent.getClientFingerprint();
        String serverFingerprint = sslEvent.getServerFingerprint();
        
        boolean fingerMatch = "3703381180726290438".equals(clientFingerprint) && "1142710092471348808".equals(serverFingerprint);
        
        // 检查心跳包特征
        boolean heartbeatMatch = hasHeartbeatWithSize(sslEvent, 44) || hasHeartbeatWithSize(sslEvent, 54);
        
        return fingerMatch && heartbeatMatch;
    }
    
    /**
     * 检测是否为PyFud
     */
    private boolean isPyFud(SslEvent sslEvent) {
        // 检查负载特征
        String payload = sslEvent.getPayload();
        if (payload == null || payload.isEmpty()) {
            return false;
        }
        
        // 检查负载是否包含MAC地址和三个逗号分隔的字段
        String[] parts = payload.split(",");
        if (parts.length != 3) {
            return false;
        }
        
        // 检查负载是否包含源MAC地址
        String sourceMac = sslEvent.getSourceMac();
        return payload.contains(sourceMac);
    }
    
    /**
     * 检查是否存在指定大小的心跳包
     */
    private boolean hasHeartbeatWithSize(SslEvent sslEvent, int size) {
        return hasClientHeartbeatWithSize(sslEvent, size) || hasServerHeartbeatWithSize(sslEvent, size);
    }
    
    /**
     * 检查是否存在指定大小的客户端心跳包
     */
    private boolean hasClientHeartbeatWithSize(SslEvent sslEvent, int size) {
        Integer clientHeartbeatSize = sslEvent.getClientHeartbeatSize();
        return clientHeartbeatSize != null && clientHeartbeatSize == size;
    }
    
    /**
     * 检查是否存在指定大小的服务器心跳包
     */
    private boolean hasServerHeartbeatWithSize(SslEvent sslEvent, int size) {
        Integer serverHeartbeatSize = sslEvent.getServerHeartbeatSize();
        return serverHeartbeatSize != null && serverHeartbeatSize == size;
    }
    
    /**
     * 创建C2框架告警事件
     */
    private AlarmEvent createAlertEvent(SslEvent sslEvent, String subType, String subName) {
        AlarmEvent alert = new AlarmEvent();
        
        // 设置基本属性
        alert.setId(UUID.randomUUID().toString());
        alert.setType(C2_ALERT_TYPE + "_" + subType);
        alert.setName(C2_ALERT_NAME + " - " + subName);
        alert.setSeverity(C2_ALERT_LEVEL);
        alert.setDescription("检测到可能的" + subName + "命令与控制通信行为");
        alert.setSolution("1. 立即隔离涉及的主机\n2. 检查源IP和目标IP之间的SSL通信\n3. 分析系统是否存在其他入侵迹象");
        
        // 设置源IP和端口
        alert.setSourceIp(sslEvent.getSourceIp());
        alert.setSourcePort(sslEvent.getSourcePort());
        alert.setDestinationIp(sslEvent.getDestinationIp());
        alert.setDestinationPort(sslEvent.getDestinationPort());
        
        // 设置时间戳
        alert.setTimestamp(System.currentTimeMillis());
        
        // 设置详细信息
        Map<String, Object> details = new HashMap<>();
        details.put("c2Type", subType);
        details.put("sslVersion", sslEvent.getSslVersion());
        details.put("cipherSuite", sslEvent.getCipherSuite());
        details.put("certificate", sslEvent.getCertificate());
        details.put("clientFingerprint", sslEvent.getClientFingerprint());
        details.put("serverFingerprint", sslEvent.getServerFingerprint());
        details.put("clientHeartbeatSize", sslEvent.getClientHeartbeatSize());
        details.put("serverHeartbeatSize", sslEvent.getServerHeartbeatSize());
        details.put("payload", sslEvent.getPayload());
        alert.setDetails(details);
        
        return alert;
    }
    
    /**
     * 创建C2框架检测数据流
     */
    public static DataStream<AlarmEvent> detectC2Frameworks(DataStream<SslEvent> sslEvents) {
        return sslEvents
                .flatMap(new C2FrameworkDetector())
                .name("命令与控制框架检测")
                .setParallelism(2);
    }
}
