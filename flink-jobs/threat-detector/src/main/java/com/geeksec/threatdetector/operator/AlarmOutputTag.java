package com.geeksec.threatdetector.operator;

import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

/**
 * <AUTHOR>
 * @Date 2022/12/29
 */

public class AlarmOutputTag {
    public static final OutputTag<Row> ALARM_SIP_DIP_FINGER_ROW = new OutputTag<>("ALARM_SIP_DIP_FINGER_ROW", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_HTTP_WEB_LOGIN_INFO = new OutputTag<>("ALARM_HTTP_WEB_LOGIN_INFO", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_WEB_LOGIN_INFO = new OutputTag<>("ALARM_WEB_LOGIN_INFO", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_PORT_SCAN_ROW = new OutputTag<>("ALARM_PORT_SCAN_ROW", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_DNS_TUNNEL_ROW = new OutputTag<>("ALARM_DNS_TUNNEL_ROW", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_FINGER_LABEL_EDGE_ROW = new OutputTag<>("ALARM_FINGER_LABEL_EDGE_ROW", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_DNS_MINE_EDGE_ROW = new OutputTag<>("ALARM_DNS_MINE_EDGE_ROW", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_NEOREGEO_ROW = new OutputTag<>("ALARM_NEOREGEO_ROW", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_RDP_ROW = new OutputTag<>("ALARM_RDP_ROW", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_ORACLE_ROW = new OutputTag<>("ALARM_ORACLE_ROW", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_MYSQL_ROW = new OutputTag<>("ALARM_MYSQL_ROW", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_SMB_ROW = new OutputTag<>("ALARM_SMB_ROW", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_APP_SCAN_ROW = new OutputTag<>("ALARM_APP_SCAN_ROW", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_XRAY_ROW = new OutputTag<>("ALARM_XRAY_ROW", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_SUO5_ROW = new OutputTag<>("ALARM_SUO5_ROW", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_BEHINDER_ROW = new OutputTag<>("ALARM_BEHINDER_ROW", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_ANTSWORD_ROW = new OutputTag<>("ALARM_ANTSWORD_ROW", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_TUNNEL_ROW = new OutputTag<>("ALARM_TUNNEL_ROW", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_SRCPC2_ROW = new OutputTag<>("ALARM_SRCPC2_ROW", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_URCP_ROW = new OutputTag<>("ALARM_URCP_ROW", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_WEB_SHELL_ROW = new OutputTag<>("ALARM_WEB_SHELL_ROW", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_ENCRYPTED_TOOL_ROW = new OutputTag<>("ALARM_ENCRYPTED_TOOL_ROW", TypeInformation.of(Row.class));
}
