package com.geeksec.threatdetector.operator.label.connection;

import com.geeksec.common.utils.crypto.CryptoUtils;
import com.geeksec.threatdetector.common.enums.ThreatTypeEnum;
import com.geeksec.threatdetector.model.connection.ConnectBasicInfo;
import com.geeksec.threatdetector.model.nebula.BaseEdge;
import com.geeksec.threatdetector.model.protocol.SRCPInfo;
import de.malkusch.whoisServerList.publicSuffixList.PublicSuffixList;
import de.malkusch.whoisServerList.publicSuffixList.PublicSuffixListFactory;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.netty4.io.netty.util.internal.StringUtil;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 连接标签处理器，负责分析网络连接信息并生成相应的安全标签
 * 处理包括域名解析、连接标签生成、威胁检测等功能
 *
 * <AUTHOR>
 * @since 2023/01/01
 */
public class ConnectionLabeler extends RichFlatMapFunction<Map<String, Object>, Map<String, Object>> {

    private static final Logger log = LoggerFactory.getLogger(ConnectionLabeler.class);

    private static PublicSuffixListFactory factory = null;
    private static PublicSuffixList suffixList = null;
    private static final String SILLY_RAT_SIGNATURE = ")J@NcRfU";

    /**
     * 将十六进制字符串转换为字节数组
     */
    private static byte[] hexStringToByteArray(String hexString) {
        if (hexString == null || hexString.length() % 2 != 0) {
            throw new IllegalArgumentException("Invalid hex string");
        }
        byte[] bytes = new byte[hexString.length() / 2];
        for (int i = 0; i < hexString.length(); i += 2) {
            bytes[i / 2] = (byte) ((Character.digit(hexString.charAt(i), 16) << 4)
                    + Character.digit(hexString.charAt(i + 1), 16));
        }
        return bytes;
    }

    /**
     * 检查域名是否有效
     */
    private static boolean isValidDomain(String domain) {
        if (domain == null || domain.trim().isEmpty()) {
            return false;
        }
        // 简单的域名验证逻辑
        return domain.contains(".") && !domain.startsWith(".") && !domain.endsWith(".");
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        factory = new PublicSuffixListFactory();
        Properties properties = factory.getDefaults();
        properties.setProperty(PublicSuffixListFactory.PROPERTY_LIST_FILE, "/effective_tld_names.dat");
        suffixList = factory.build();
    }

    @Override
    public void flatMap(Map<String, Object> pbMap, Collector<Map<String, Object>> collector) throws Exception {

        // HTTP部分
        // ClientIp--->Domain Http访问 & ServerIp--->Doamin Http应用
        BaseEdge clientIpConnectDomain = getIpConnectDomain(pbMap, "client");
        BaseEdge serverIpConnectDomain = getIpConnectDomain(pbMap, "server");
        Row http_web_loginRow = getHttp_web_loginMap(pbMap);
        Row Port_Scan_Row = getPort_Scan_Row(pbMap);
        Row ToDeskRow = getToDeskRow(pbMap);
        Row ConnectInfo_Dns = get_ConnectInfo_Dns(pbMap);
        Row RDP_row = get_RDP_row(pbMap);
        Row Oracle_row = get_Oracle_row(pbMap);
        Row MYSQL_row = get_MYSQL_row(pbMap);
        Row SMB_row = get_SMB_row(pbMap);
        Row xRayFingerRow = getxRayFingerRow(pbMap);
        Row AntSword_php_infoRow = getAntSword_php_infoRow(pbMap);
        Row srcpInfoRow = getSRCPInfo(pbMap);
        Row urcpInfoRow = getURCPInfo(pbMap);

        HashMap<String, Object> resultMap = new HashMap<>();
        resultMap.put("clientIpConnectDomainEdge", clientIpConnectDomain);
        resultMap.put("serverIpConnectDomainEdge", serverIpConnectDomain);
        resultMap.put("http_web_login", http_web_loginRow);
        resultMap.put("Port_Scan_Row", Port_Scan_Row);
        resultMap.put("ToDeskRow", ToDeskRow);
        resultMap.put("ConnectInfo_Dns",ConnectInfo_Dns);
        if (RDP_row!=null){
            resultMap.put("RDP_row",RDP_row);
        }
        if (Oracle_row!=null){
            resultMap.put("Oracle_row",Oracle_row);
        }
        if (MYSQL_row!=null){
            resultMap.put("MYSQL_row",MYSQL_row);
        }
        if (SMB_row!=null){
            resultMap.put("SMB_row",SMB_row);
        }
        if (xRayFingerRow!=null){
            resultMap.put("xRayFingerRow",xRayFingerRow);
        }
        if (AntSword_php_infoRow!=null){
            resultMap.put("AntSword_php_infoRow",AntSword_php_infoRow);
        }
        if (srcpInfoRow!=null){
            resultMap.put("srcpInfo",srcpInfoRow);
        }
        if (urcpInfoRow!=null){
            resultMap.put("urcpInfo",urcpInfoRow);
        }

        collector.collect(resultMap);
    }

    // 获取ToDesk判断元数据
    private Row getToDeskRow(Map<String, Object> pbMap) {
        String AppName = (String) pbMap.get("AppProto");
        List<Object> ssl = (List<Object>) pbMap.get("SSL");
        boolean isToDesk = false;
        if (!ssl.isEmpty()){
            Map<String, Object> SSL = (Map<String, Object>) ssl.get(0);
            String chServerName = (String) SSL.getOrDefault("CH_ServerName", "");
            isToDesk = chServerName.equals("st.todesk.com");
        }
        boolean isSTUN = AppName.equals("STUN");
        Row resultRow = new Row(9);
        String SessionId = (String) pbMap.get("SessionId");
        resultRow.setField(0,"ToDeskRow");
        resultRow.setField(1,pbMap.get("sIp"));
        resultRow.setField(2,pbMap.get("dIp"));
        resultRow.setField(3,pbMap.get("CreateTime"));
        resultRow.setField(4,isToDesk);
        resultRow.setField(5,isSTUN);
        resultRow.setField(6,SessionId);
        resultRow.setField(7,pbMap.get("es_key"));
        resultRow.setField(8, null);

        return resultRow;
    }
    // 设置远程控制协议类型的时候，优先设置标签类型的，更精准，没有标签再设置AppName
    private Row getSRCPInfo(Map<String, Object> pbMap) {
        String AppName = (String) pbMap.get("AppProto");
        List<String> Labels = ((List<Integer>) pbMap.get("Labels")).stream().map(Object::toString).collect(Collectors.toList());
        List<String> RCPLabel = getSameLabel(SRCPInfo.RCPLabelKnowledge,Labels);
        if (!SRCPInfo.RCPProKnowledge.contains(AppName) && RCPLabel==null){
            return null;
        }

        SRCPInfo srcpInfo = new SRCPInfo();
        ConnectBasicInfo connectBasicInfo = new ConnectBasicInfo(pbMap);
        srcpInfo.setConnectBasicInfo(connectBasicInfo);
        if(SRCPInfo.RCPProKnowledge.contains(AppName)){
            srcpInfo.setRCPType(AppName);
        }else{
            srcpInfo.setRCPType(RCPLabel.get(0));
        }


        Row resultRow = new Row(2);
        resultRow.setField(0,"srcpInfoRow");
        resultRow.setField(1,srcpInfo);

        return resultRow;
    }
    // 未知远程控制协议识别（sillyrat）
    private Row getURCPInfo(Map<String, Object> pbMap) {
        SRCPInfo srcpInfo = new SRCPInfo();
        String AppName = (String) pbMap.get("AppProto");
        List<String> Labels = ((List<Integer>) pbMap.get("Labels")).stream().map(Object::toString).collect(Collectors.toList());
        List<String> RCPLabel = getSameLabel(SRCPInfo.RCPLabelKnowledge,Labels);
        if (RCPLabel == null && !SRCPInfo.RCPProKnowledge.contains(AppName)){
            ConnectBasicInfo connectBasicInfo = new ConnectBasicInfo(pbMap);
            connectBasicInfo.handleControl();
            boolean control = connectBasicInfo.isControl();
            boolean urcp = isURCP(connectBasicInfo.getServer4PayloadList());
            if (control && urcp){
                srcpInfo.setConnectBasicInfo(connectBasicInfo);
                srcpInfo.setRCPType("UnKnown");


                Row resultRow = new Row(2);
                resultRow.setField(0,"urcpInfoRow");
                resultRow.setField(1,srcpInfo);

                return resultRow;
            }
        }
        return null;
    }
    // 匹配sillyrat包特征
    boolean isURCP(List<String> server4PayloadList){
        if (!server4PayloadList.isEmpty()){
            for (String hexStr : server4PayloadList) {
                if (hexStr == null || hexStr.isEmpty()) {
                    return false; // 如果有空字符串，直接返回 false
                }
                String utf8Str = new String(hexStringToByteArray(hexStr), StandardCharsets.UTF_8);
                if (!utf8Str.endsWith(SILLY_RAT_SIGNATURE)) {
                    return false; // 如果不以指定后缀结尾，直接返回 false
                }
            }
            return true; // 所有元素都符合条件，返回 true
        }
        return false;
    }

    public static List<String> getSameLabel(List<String> list1, List<String> list2) {
        Set<String> set1 = new HashSet<>(list1);
        Set<String> set2 = new HashSet<>(list2);

        List<String> RCPLabel = set1.stream()
                .filter(set2::contains)
                .collect(Collectors.toList());

        return RCPLabel.isEmpty() ? null : RCPLabel;
    }

    /**
     * 生成IP与HTTP域名之间的边联系
     */
    private BaseEdge getIpConnectDomain(Map<String, Object> pbMap, String type) {
        List<HashMap<String, Object>> httpInfoList = (List<HashMap<String, Object>>) pbMap.get("HTTP");
        BaseEdge ipConnectDomainEdge = new BaseEdge();
        String httpDomain = "";
        if (httpInfoList.size() != 0) {
            for (HashMap<String, Object> httpMap : httpInfoList) {
                String response = (String) httpMap.get("Response");
                if (!StringUtil.isNullOrEmpty(response) && response.contains("200")) {
                    String httpDomainAddr = (String) httpMap.get("Host");
                    String serverIp = (String) pbMap.get("dIp");
                    if (isValidDomain(httpDomainAddr) && !httpDomainAddr.equals(serverIp)) {
                        if (type.equals("client")) {
                            ipConnectDomainEdge.setSrcId((String) pbMap.get("sIp"));
                        } else if (type.equals("server")) {
                            ipConnectDomainEdge.setSrcId((String) pbMap.get("dIp"));
                        }
                        if (httpDomainAddr.length() > 200){
                            ipConnectDomainEdge.setDstId(CryptoUtils.md5(httpDomainAddr));
                        }else{
                            ipConnectDomainEdge.setDstId(httpDomainAddr);
                        }
                        ipConnectDomainEdge.setFirstSeen((Integer) pbMap.get("StartTime"));
                        ipConnectDomainEdge.setLastSeen((Integer) pbMap.get("StartTime"));
                        ipConnectDomainEdge.setSessionCnt(0L);
                        httpDomain = httpDomainAddr;
                    } else {
                        return null;
                    }
                } else {
                    return null;
                }
            }
        } else {
            return null;
        }


        return ipConnectDomainEdge;
    }

    private Row getHttp_web_loginMap(Map<String, Object> pbMap){
        Row resultRow = new Row(9);
        int StartTime = (int) pbMap.get("StartTime");
        int EndTime = (int) pbMap.get("EndTime");
        String SessionId = (String) pbMap.get("SessionId");
        List<HashMap<String, Object>> httpInfoList = (List<HashMap<String, Object>>) pbMap.get("HTTP");
        int LoginCount = 0;
        int PostCount = 0;
        int GetCount = 0;
        if (httpInfoList.size() != 0){
            for (HashMap<String, Object> httpMap : httpInfoList){
                if (httpMap.get("Act")=="Post"){
                    PostCount+=1;
                }
                if (httpMap.get("Act")=="Get"){
                    GetCount+=1;
                }
                String UrlInfo = (String) httpMap.get("Url");
                if (UrlInfo.contains("Login")){
                    LoginCount+=1;
                }
            }
        }
        resultRow.setField(0,LoginCount);
        resultRow.setField(1,PostCount);
        resultRow.setField(2,GetCount);
        resultRow.setField(3,(EndTime-StartTime));
        resultRow.setField(4,SessionId);
        resultRow.setField(5,pbMap.get("sIp"));
        resultRow.setField(6,pbMap.get("dIp"));
        resultRow.setField(7,pbMap.get("es_key"));
        resultRow.setField(8, null);

        return resultRow;
    }

    private Row getPort_Scan_Row(Map<String,Object> pbMap){
        Row resultRow = new Row(8);
        String SessionId = (String) pbMap.get("SessionId");
        resultRow.setField(0,"Port_Scan_Row");
        resultRow.setField(1,pbMap.get("sIp"));
        resultRow.setField(2,pbMap.get("dIp"));
        resultRow.setField(3,pbMap.get("StartTime"));
        resultRow.setField(4,pbMap.get("dPort"));
        resultRow.setField(5,SessionId);
        resultRow.setField(6,pbMap.get("es_key"));
        resultRow.setField(7, null);
        return resultRow;
    }

    private Row get_ConnectInfo_Dns(Map<String,Object> pbMap){
        Row resultRow = new Row(6);
        List<HashMap<String, Object>> DNS_List = (List<HashMap<String, Object>>) pbMap.get("DNS");
        resultRow.setField(0,"ConnectInfo_Dns");
        resultRow.setField(1,pbMap.get("sIp"));
        resultRow.setField(2,DNS_List);
        resultRow.setField(3,pbMap.get("SessionId"));
        resultRow.setField(4,pbMap.get("es_key"));
        resultRow.setField(5, null);
        return resultRow;
    }

    private Row get_RDP_row(Map<String,Object> pbMap){
        //27064 rdp登录返回
        //27065 rdp登录失败
        String App_Name = (String) pbMap.get("AppProto");
        if (App_Name.equals("RDP")){
            List<String> Labels = ((List<Integer>) pbMap.get("Labels")).stream().map(Object::toString).collect(Collectors.toList());
            List<String> Labels_RDP = Arrays.asList(ThreatTypeEnum.RDP_LOGIN_REQ.getCode(), ThreatTypeEnum.RDP_LOGIN_RETURN.getCode());
            Map<String,Integer> label_count = new HashMap<>();
            if (!Labels.contains(ThreatTypeEnum.RDP_LOGIN_REQ.getCode()) && !Labels.contains(ThreatTypeEnum.RDP_LOGIN_RETURN.getCode())){
                return null;
            }else {
                for(String label : Labels_RDP){
                    if (Labels.contains(label)){
                        label_count.put(label,1);
                    }else {
                        label_count.put(label,0);
                    }
                }
            }
            List<String> dipSipList = Arrays.asList(pbMap.get("dIp").toString(),pbMap.get("sIp").toString());
            Row RDP_row = new Row(7);
            Collection<String> SessionId_list = new HashSet<>();
            SessionId_list.add((String) pbMap.get("SessionId"));
            RDP_row.setField(0,"RDP_row");
            RDP_row.setField(1,dipSipList);
            RDP_row.setField(2,label_count);
            RDP_row.setField(3,pbMap.get("StartTime"));
            RDP_row.setField(4,SessionId_list);
            RDP_row.setField(5,pbMap.get("es_key"));
            RDP_row.setField(6, null);
            return RDP_row;
        }else{
            return null;
        }
    }

    private Row get_SMB_row(Map<String,Object> pbMap){
        //27066 SMB登录请求
        //27067 SMB登录失败
        String App_Name = (String) pbMap.get("AppProto");
        if (App_Name.equals("SMB")){
            List<String> Labels = ((List<Integer>) pbMap.get("Labels")).stream().map(Object::toString).collect(Collectors.toList());
            List<String> Labels_SMB = Arrays.asList(ThreatTypeEnum.SMB_LOGIN_REQ.getCode(), ThreatTypeEnum.SMB_LOGIN_FAIL.getCode());
            Map<String,Integer> label_count = new HashMap<>();
            if (!Labels.contains(ThreatTypeEnum.SMB_LOGIN_REQ.getCode()) && !Labels.contains(ThreatTypeEnum.SMB_LOGIN_FAIL.getCode())){
                return null;
            }else {
                for(String label : Labels_SMB){
                    if (Labels.contains(label)){
                        label_count.put(label,1);
                    }else {
                        label_count.put(label,0);
                    }
                }
            }
            List<String> dipSipList = Arrays.asList(pbMap.get("dIp").toString(),pbMap.get("sIp").toString());
            Row SMB_row = new Row(7);
            Collection<String> SessionId_list = new HashSet<>();
            SessionId_list.add((String) pbMap.get("SessionId"));
            SMB_row.setField(0,"SMB_row");
            SMB_row.setField(1,dipSipList);
            SMB_row.setField(2,label_count);
            SMB_row.setField(3,pbMap.get("StartTime"));
            SMB_row.setField(4,SessionId_list);
            SMB_row.setField(5,pbMap.get("es_key"));
            SMB_row.setField(6, null);
            return SMB_row;
        }else {
            return null;
        }
    }

    private Row get_Oracle_row(Map<String,Object> pbMap){
        //27068 oracle连接请求
        //27069 oracle登陆失败
        String App_Name = (String) pbMap.get("AppProto");
        if (App_Name.equals("ORACLE_TNS")){
            List<String> Labels = ((List<Integer>) pbMap.get("Labels")).stream().map(Object::toString).collect(Collectors.toList());
            List<String> Labels_Oracle = Arrays.asList(ThreatTypeEnum.ORACLE_CONNECT_REQ.getCode(), ThreatTypeEnum.ORACLE_LOGIN_FAIL.getCode());
            Map<String,Integer> label_count = new HashMap<>();
            if (!Labels.contains(ThreatTypeEnum.ORACLE_CONNECT_REQ.getCode()) && !Labels.contains(ThreatTypeEnum.ORACLE_LOGIN_FAIL.getCode())){
                return null;
            }else {
                for(String label : Labels_Oracle){
                    if (Labels.contains(label)){
                        label_count.put(label,1);
                    }else {
                        label_count.put(label,0);
                    }
                }
            }
            List<String> dipSipList = Arrays.asList(pbMap.get("dIp").toString(),pbMap.get("sIp").toString());
            Row Oracle_row = new Row(7);
            Collection<String> SessionId_list = new HashSet<>();
            SessionId_list.add((String) pbMap.get("SessionId"));
            Oracle_row.setField(0,"Oracle_row");
            Oracle_row.setField(1,dipSipList);
            Oracle_row.setField(2,label_count);
            Oracle_row.setField(3,pbMap.get("StartTime"));
            Oracle_row.setField(4,SessionId_list);
            Oracle_row.setField(5,pbMap.get("es_key"));
            Oracle_row.setField(6, null);
            return Oracle_row;
        }else {
            return null;
        }
    }
    private Row get_MYSQL_row(Map<String,Object> pbMap){
        //27071 MySQL登录失败
        String App_Name = (String) pbMap.get("AppProto");
        if (App_Name.equals("MYSQL")){
            List<String> Labels = ((List<Integer>) pbMap.get("Labels")).stream().map(Object::toString).collect(Collectors.toList());
            List<String> Labels_MYSQL = Arrays.asList(ThreatTypeEnum.MYSQL_LOGIN_FAIL.getCode());
            Map<String,Integer> label_count = new HashMap<>();
            if (!Labels.contains(ThreatTypeEnum.MYSQL_LOGIN_FAIL.getCode())){
                return null;
            }else {
                for(String label : Labels_MYSQL){
                    if (Labels.contains(label)){
                        label_count.put(label,1);
                    }else {
                        label_count.put(label,0);
                    }
                }
            }
            List<String> dipSipList = Arrays.asList(pbMap.get("dIp").toString(),pbMap.get("sIp").toString());
            Row MYSQL_row = new Row(7);
            Collection<String> SessionId_list = new HashSet<>();
            SessionId_list.add((String) pbMap.get("SessionId"));
            MYSQL_row.setField(0,"MYSQL_row");
            MYSQL_row.setField(1,dipSipList);
            MYSQL_row.setField(2,label_count);
            MYSQL_row.setField(3,pbMap.get("StartTime"));
            MYSQL_row.setField(4,SessionId_list);
            MYSQL_row.setField(5,pbMap.get("es_key"));
            MYSQL_row.setField(6, null);
            return MYSQL_row;
        }else {
            return null;
        }
    }

    public Row getxRayFingerRow(Map<String, Object> pbMap){
        String APP_name = (String) pbMap.get("AppProto");
        if (APP_name.equals("SSL")){
            String fingerID = (String) pbMap.get("sSSLFinger");
            //匹配xray的fingerID，是的话就进行窗口统计
            if (!fingerID.equals("120848568532455093")){
                return null;
            }
            List<String> dipSipList = Arrays.asList(pbMap.get("dIp").toString(),pbMap.get("sIp").toString());
            Row dipdSSLFingerSipRow = new Row(7);
            List<String> SessionId_list = new ArrayList<>();
            SessionId_list.add((String) pbMap.get("SessionId"));
            dipdSSLFingerSipRow.setField(0,"xRay_Finger_Row");
            dipdSSLFingerSipRow.setField(1,dipSipList);
            dipdSSLFingerSipRow.setField(2,fingerID);
            dipdSSLFingerSipRow.setField(3,pbMap.get("StartTime"));
            dipdSSLFingerSipRow.setField(4,SessionId_list);
            dipdSSLFingerSipRow.setField(5,pbMap.get("es_key"));
            dipdSSLFingerSipRow.setField(6, null);
            return dipdSSLFingerSipRow;
        }else {
            return null;
        }
    }

    //蚁剑
    //45,27078,威胁,0,session,antSword_PHP自定义编码请求,antSword_PHP自定义编码请求
    //45,27079,威胁,0,session,antSword_PHP自定义编码响应,antSword_PHP自定义编码响应
    public Row getAntSword_php_infoRow(Map<String, Object> pbMap){
        String APP_name = (String) pbMap.get("AppProto");
        if (APP_name.equals("HTTP")){
            List<String> Labels = ((List<Integer>) pbMap.get("Labels")).stream().map(Object::toString).collect(Collectors.toList());
            if(Labels.contains(ThreatTypeEnum.ANT_SWORD_PHP_REQ.getCode()) && Labels.contains(ThreatTypeEnum.ANT_SWORD_PHP_RES.getCode())){
                Row resultRow = new Row(8);
                resultRow.setField(0,"antSword_php_info");
                resultRow.setField(1,pbMap.get("sIp").toString());
                resultRow.setField(2,pbMap.get("dIp").toString());
                resultRow.setField(3,pbMap.get("StartTime"));
                resultRow.setField(4,Arrays.asList(ThreatTypeEnum.ANT_SWORD_PHP_REQ.getCode(), ThreatTypeEnum.ANT_SWORD_PHP_RES.getCode()));
                resultRow.setField(5,pbMap.get("SessionId"));
                resultRow.setField(6,pbMap.get("es_key"));
                resultRow.setField(7, null);
                return resultRow;
            }else {
                return null;
            }
        }else {
            return null;
        }
    }
}
