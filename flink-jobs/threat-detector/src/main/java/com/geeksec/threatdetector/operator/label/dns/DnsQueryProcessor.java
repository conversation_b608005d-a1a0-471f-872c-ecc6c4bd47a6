package com.geeksec.threatdetector.operator.label.dns;

import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.netty4.io.netty.util.internal.StringUtil;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * DNS查询处理器
 * 负责处理DNS查询信息，包括查询类型映射和DNS服务器白名单管理
 *
 * <AUTHOR>
 */
public class DnsQueryProcessor extends RichFlatMapFunction<Map<String, Object>, Map<String, Object>> {

    private static final Logger log = LoggerFactory.getLogger(DnsQueryProcessor.class);

    private static final long serialVersionUID = 1L;
    public static Map<String,String> QUERY_TYPE_MAP = new HashMap<>();
    public static List<String> BenignDNSServer_Map = new ArrayList<>();

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        log.info("Initializing DnsQueryProcessor with simplified implementation");

        // 初始化查询类型映射
        QUERY_TYPE_MAP = new HashMap<>();
        QUERY_TYPE_MAP.put("1", "A");
        QUERY_TYPE_MAP.put("2", "NS");
        QUERY_TYPE_MAP.put("5", "CNAME");
        QUERY_TYPE_MAP.put("6", "SOA");
        QUERY_TYPE_MAP.put("12", "PTR");
        QUERY_TYPE_MAP.put("15", "MX");
        QUERY_TYPE_MAP.put("16", "TXT");
        QUERY_TYPE_MAP.put("28", "AAAA");
        QUERY_TYPE_MAP.put("33", "SRV");
        QUERY_TYPE_MAP.put("65", "HTTPS");

        try {
            // 尝试从资源文件加载更多配置
            InputStream queryTypeMapStream = this.getClass().getClassLoader().getResourceAsStream("query_type.csv");
            if (queryTypeMapStream != null) {
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(queryTypeMapStream))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        String[] parts = line.split(",");
                        if (parts.length == 2) {
                            QUERY_TYPE_MAP.put(parts[0].trim(), parts[1].trim());
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn("Could not load query_type.csv from resources, using default mappings", e);
        }

        // 加载良性DNS服务器列表
        try {
            InputStream benignDnsStream = this.getClass().getClassLoader().getResourceAsStream("BenignDNSServer.csv");
            if (benignDnsStream != null) {
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(benignDnsStream))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        BenignDNSServer_Map.add(line.trim());
                    }
                    log.info("Loaded {} benign DNS servers from BenignDNSServer.csv", BenignDNSServer_Map.size());
                }
            } else {
                log.warn("BenignDNSServer.csv not found in resources.");
            }
        } catch (Exception e) {
            log.error("Error loading BenignDNSServer.csv: {}", e.getMessage(), e);
        }
    }

    @Override
    public void flatMap(Map<String, Object> value, Collector<Map<String, Object>> out) throws Exception {
        try {
            if (value == null) {
                return;
            }
            
            // 只处理DNS协议数据
            if (value.get("protocol") == null || !value.get("protocol").equals("dns")) {
                return;
            }
            
            // 必须有查询内容
            if (value.get("query") == null) {
                return;
            }
            
            // 增强DNS数据
            Map<String, Object> enhancedData = new HashMap<>(value);
            
            // 添加查询类型的文本表示
            if (value.get("qtype") != null && value.get("qtype") instanceof Integer) {
                Integer qtype = (Integer) value.get("qtype");
                String qtypeStr = QUERY_TYPE_MAP.getOrDefault(qtype.toString(), "UNKNOWN");
                enhancedData.put("qtype_str", qtypeStr);
            }
            
            // 检查是否有源IP和目标IP
            String srcIp = (String) value.get("src_ip");
            String dstIp = (String) value.get("dst_ip");
            String query = (String) value.get("query");
            
            if (!StringUtil.isNullOrEmpty(srcIp) && !StringUtil.isNullOrEmpty(dstIp) && !StringUtil.isNullOrEmpty(query)) {
                // 添加域名长度作为特征
                enhancedData.put("query_length", query.length());
                
                // 添加域名中的点数量作为特征
                int dotCount = 0;
                for (char c : query.toCharArray()) {
                    if (c == '.') dotCount++;
                }
                enhancedData.put("dot_count", dotCount);
                
                // 添加简单的DNS请求分类
                Object answer = value.get("answer");
                if (answer == null) {
                    enhancedData.put("response_type", "no_answer");
                } else if (answer instanceof String && !((String) answer).isEmpty()) {
                    enhancedData.put("response_type", "has_answer");
                } else {
                    enhancedData.put("response_type", "unknown");
                }
                
                // 输出增强后的数据
                out.collect(enhancedData);
            } else {
                // 如果缺少必要字段，仍然输出原始数据
                out.collect(value);
            }
        } catch (Exception e) {
            log.error("Error in DNS flatMap processing: {}", e.getMessage(), e);
            // 发生错误时仍然输出原始数据
            out.collect(value);
        }
    }
}
