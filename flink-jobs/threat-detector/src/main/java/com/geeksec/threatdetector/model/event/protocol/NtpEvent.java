package com.geeksec.threatdetector.model.event.protocol;

/**
 * NTP事件模型
 *
 * <AUTHOR>
 */
public class NtpEvent extends ProtocolEvent {
    
    /**
     * NTP版本
     */
    private Integer version;
    
    /**
     * 模式
     */
    private Integer mode;
    
    /**
     * 层级
     */
    private Integer stratum;
    
    /**
     * 轮询间隔
     */
    private Integer poll;
    
    /**
     * 精度
     */
    private Integer precision;
    
    /**
     * 根延迟
     */
    private Double rootDelay;
    
    /**
     * 根分散
     */
    private Double rootDispersion;
    
    /**
     * 参考标识符
     */
    private String referenceId;
    
    /**
     * 参考时间戳
     */
    private Long referenceTimestamp;
    
    /**
     * 原始时间戳
     */
    private Long originateTimestamp;
    
    /**
     * 接收时间戳
     */
    private Long receiveTimestamp;
    
    /**
     * 发送时间戳
     */
    private Long transmitTimestamp;
    
    /**
     * 扩展字段
     */
    private String extension;

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Integer getMode() {
        return mode;
    }

    public void setMode(Integer mode) {
        this.mode = mode;
    }

    public Integer getStratum() {
        return stratum;
    }

    public void setStratum(Integer stratum) {
        this.stratum = stratum;
    }

    public Integer getPoll() {
        return poll;
    }

    public void setPoll(Integer poll) {
        this.poll = poll;
    }
    
    /**
     * 获取轮询间隔（与poll相同）
     * 
     * @return 轮询间隔
     */
    public Integer getPollInterval() {
        return poll;
    }

    public Integer getPrecision() {
        return precision;
    }

    public void setPrecision(Integer precision) {
        this.precision = precision;
    }

    public Double getRootDelay() {
        return rootDelay;
    }

    public void setRootDelay(Double rootDelay) {
        this.rootDelay = rootDelay;
    }

    public Double getRootDispersion() {
        return rootDispersion;
    }

    public void setRootDispersion(Double rootDispersion) {
        this.rootDispersion = rootDispersion;
    }

    public String getReferenceId() {
        return referenceId;
    }

    public void setReferenceId(String referenceId) {
        this.referenceId = referenceId;
    }

    public Long getReferenceTimestamp() {
        return referenceTimestamp;
    }

    public void setReferenceTimestamp(Long referenceTimestamp) {
        this.referenceTimestamp = referenceTimestamp;
    }

    public Long getOriginateTimestamp() {
        return originateTimestamp;
    }

    public void setOriginateTimestamp(Long originateTimestamp) {
        this.originateTimestamp = originateTimestamp;
    }

    public Long getReceiveTimestamp() {
        return receiveTimestamp;
    }

    public void setReceiveTimestamp(Long receiveTimestamp) {
        this.receiveTimestamp = receiveTimestamp;
    }

    public Long getTransmitTimestamp() {
        return transmitTimestamp;
    }

    public void setTransmitTimestamp(Long transmitTimestamp) {
        this.transmitTimestamp = transmitTimestamp;
    }

    public String getExtension() {
        return extension;
    }

    public void setExtension(String extension) {
        this.extension = extension;
    }

    /**
     * 获取负载数据的字节数组
     * 
     * @return 负载数据的字节数组
     */
    public byte[] getPayloadBytes() {
        if (getPayload() == null) {
            return new byte[0];
        }
        return getPayload().getBytes();
    }
    
    @Override
    public String toString() {
        return "NtpEvent{" +
                "version=" + version +
                ", mode=" + mode +
                ", stratum=" + stratum +
                ", poll=" + poll +
                ", sourceIp='" + getSourceIp() + '\'' +
                ", destinationIp='" + getDestinationIp() + '\'' +
                '}';
    }
}
