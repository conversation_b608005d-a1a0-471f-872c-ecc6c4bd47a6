package com.geeksec.threatdetector.operator;

import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

/**
 * <AUTHOR>
 * @Date 2022/12/29
 */

public class NebulaEdgeOutputTag {
    public static final OutputTag<Row> NEBULA_SIP_DIP_FINGER_ROW = new OutputTag<>("NEBULA_SIP_DIP_FINGER_ROW", TypeInformation.of(Row.class));
    public static final OutputTag<Row> NEBULA_WEB_LOGIN_INFO = new OutputTag<>("NEBULA_WEB_LOGIN_INFO", TypeInformation.of(Row.class));
    public static final OutputTag<Row> NEBULA_PORT_SCAN_ROW = new OutputTag<>("NEBULA_PORT_SCAN_ROW", TypeInformation.of(Row.class));
    public static final OutputTag<Row> NEBULA_DNS_TUNNEL_ROW = new OutputTag<>("NEBULA_DNS_TUNNEL_ROW", TypeInformation.of(Row.class));
    public static final OutputTag<Row> NEBULA_DNS_SERVER_ROW = new OutputTag<>("NEBULA_DNS_SERVER_ROW", TypeInformation.of(Row.class));
    public static final OutputTag<Row> NEBULA_DNS_LE_SERVER_ROW = new OutputTag<>("NEBULA_DNS_LE_SERVER_ROW", TypeInformation.of(Row.class));
    public static final OutputTag<Row> NEBULA_FINGER_LABEL_EDGE_ROW = new OutputTag<>("NEBULA_FINGER_LABEL_EDGE_ROW", TypeInformation.of(Row.class));
    public static final OutputTag<Row> NEBULA_DNS_MINE_EDGE_ROW = new OutputTag<>("NEBULA_DNS_MINE_EDGE_ROW", TypeInformation.of(Row.class));
    public static final OutputTag<Row> NEBULA_APP_SCAN_EDGE_ROW = new OutputTag<>("NEBULA_APP_SCAN_EDGE_ROW", TypeInformation.of(Row.class));
    public static final OutputTag<Row> NEBULA_XRAY_EDGE_ROW = new OutputTag<>("NEBULA_XRAY_EDGE_ROW", TypeInformation.of(Row.class));
    public static final OutputTag<Row> NEBULA_PIVOT_EDGE_ROW = new OutputTag<>("NEBULA_PIVOT_EDGE_ROW", TypeInformation.of(Row.class));
    public static final OutputTag<Row> NEBULA_RCP_ATTACK_EDGE_ROW = new OutputTag<>("NEBULA_RCP_ATTACK_EDGE_ROW", TypeInformation.of(Row.class));
    public static final OutputTag<Row> NEBULA_WEB_SHELL_ROW = new OutputTag<>("NEBULA_WEB_SHELL_ROW", TypeInformation.of(Row.class));
    public static final OutputTag<Row> NEBULA_TUNNEL_ROW = new OutputTag<>("NEBULA_TUNNEL_ROW", TypeInformation.of(Row.class));
    public static final OutputTag<Row> NEBULA_SRCP_ROW = new OutputTag<>("NEBULA_SRCP_ROW", TypeInformation.of(Row.class));
    public static final OutputTag<Row> NEBULA_ENCRYPTED_APT_ROW = new OutputTag<>("NEBULA_ENCRYPTED_APT_ROW", TypeInformation.of(Row.class));
    public static final OutputTag<Row> NEBULA_URCP_ROW = new OutputTag<>("NEBULA_URCP_ROW", TypeInformation.of(Row.class));

}
