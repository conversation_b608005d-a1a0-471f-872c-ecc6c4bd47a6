package com.geeksec.threatdetector.io.sink;


import com.alibaba.fastjson.JSONObject;
import com.geeksec.common.config.ConfigurationManager;
import com.geeksec.threatdetector.common.constant.ConfigConstants;
import com.geeksec.threatdetector.model.config.KafkaConfig;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.api.common.state.BroadcastState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.connector.base.DeliveryGuarantee;
import org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.connector.kafka.sink.TopicSelector;
import org.apache.flink.streaming.api.datastream.BroadcastStream;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.functions.co.BroadcastProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.kafka.clients.producer.ProducerConfig;

import java.util.Map;
import java.util.Properties;

/**
 * Kafka告警输出接收器
 * 负责将告警数据输出到Kafka消息队列
 *
 * <AUTHOR>
 * @since 2023/6/5
 */
public final class KafkaAlarmSink {

    private static final Properties kafkaSinkProperties = new Properties();

    // 获取当前环境配置
    private static final ParameterTool CONFIG = ConfigurationManager.getConfig();
    public static String OUTPUT_TOPIC = "";
    public static String OUTPUT_BOOTSTRAP_SERVERS = CONFIG.get(ConfigConstants.KAFKA_BOOTSTRAP_SERVERS);

    // 临时定义常量和描述符
    private static final int PARALLELISM_2 = 2;
    private static final MapStateDescriptor<String, KafkaConfig> subscribeKafkaDescriptor =
        new MapStateDescriptor<>("kafka-config", String.class, KafkaConfig.class);

    private KafkaAlarmSink() {
        // 防止实例化
    }


    public static void AlarmKafkaSink(DataStream<JSONObject> alarmJsonStream, BroadcastStream<KafkaConfig> subscribeUnionStream, Map<String, Object> kafka_info) throws Exception {
        SingleOutputStreamOperator<JSONObject> alarmJsonScirbeStream = alarmJsonStream.connect(subscribeUnionStream).process(new BroadcastProcessFunction<JSONObject, KafkaConfig, JSONObject>() {

            @Override
            public void open(Configuration parameters) throws Exception {
                super.open(parameters);
            }

            @Override
            public void processElement(JSONObject jsonObject, BroadcastProcessFunction<JSONObject, KafkaConfig, JSONObject>.ReadOnlyContext readOnlyContext, Collector<JSONObject> collector) throws Exception {
                if (jsonObject != null) {
                    // 将Kafka外发配置通过广播流的方式添加到告警流的字段中
                    KafkaConfig config = (KafkaConfig) readOnlyContext.getBroadcastState(subscribeKafkaDescriptor).get("config-key");
                        if (ObjectUtils.allNotNull(config)) {
                            jsonObject.put("broker", config.getIp()+":"+config.getPort());
                            jsonObject.put("topic", config.getTopic());
                            OUTPUT_BOOTSTRAP_SERVERS = config.getIp()+":"+config.getPort();
                            collector.collect(jsonObject);
                        } else {
                            jsonObject.put("broker", CONFIG.get(ConfigConstants.KAFKA_BOOTSTRAP_SERVERS));
                            jsonObject.put("topic", CONFIG.get(ConfigConstants.KAFKA_OUTPUT_TOPIC));
                            collector.collect(jsonObject);
                    }
                }
            }

            @Override
            public void processBroadcastElement(KafkaConfig value, BroadcastProcessFunction<JSONObject, KafkaConfig, JSONObject>.Context context, Collector<JSONObject> collector) throws Exception {
                BroadcastState<String, KafkaConfig> state = context.getBroadcastState(subscribeKafkaDescriptor);
                state.put("config-key", value);
            }
        });
        kafkaSinkProperties.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, OUTPUT_BOOTSTRAP_SERVERS);
        kafkaSinkProperties.put(ProducerConfig.TRANSACTION_TIMEOUT_CONFIG, "5000");
        KafkaSink<String> kafkaSink = KafkaSink.<String>builder()
                .setKafkaProducerConfig(kafkaSinkProperties)
                .setBootstrapServers(OUTPUT_BOOTSTRAP_SERVERS)
                .setRecordSerializer(KafkaRecordSerializationSchema.builder()
                        // 根据topicselector读取topic配置动态改变外发的topic，Kafka服务器地址暂不能动态改变
                        .setTopicSelector(new TopicSelector<String>() {
                            @Override
                            public String apply(String s) {
                                JSONObject jsonObject = JSONObject.parseObject(s);
                                return jsonObject.getString("topic");
                            }
                        })
                        .setValueSerializationSchema(new SimpleStringSchema())
                        .build())
                .setDeliverGuarantee(DeliveryGuarantee.AT_LEAST_ONCE)
                .build();
        DataStream<String> alarmJsonScirbeStr = alarmJsonScirbeStream.map(new MapFunction<JSONObject, String>() {
            @Override
            public String map(JSONObject jsonObject) throws Exception {
                return jsonObject.toString();
            }
        });
        alarmJsonScirbeStr.sinkTo(kafkaSink).name("kafka 外发告警日志").setParallelism(PARALLELISM_2);
    }


}
