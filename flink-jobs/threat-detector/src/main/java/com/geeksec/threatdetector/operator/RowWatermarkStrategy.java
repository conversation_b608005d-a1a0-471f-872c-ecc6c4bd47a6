package com.geeksec.threatdetector.operator;

import org.apache.flink.api.common.eventtime.*;

/**
 * A {@link WatermarkStrategy} for generating watermarks for {@link org.apache.flink.types.Row} typed streams.
 * It uses {@link EventTimeTimestampAssigner} and {@link EventTimeRowWatermarkGenerator}.
 *
 * <AUTHOR>
 * @Date 2022/12/8
 */
public class RowWatermarkStrategy implements WatermarkStrategy {

    private long outOfOrdernessDelay;
    private int eventTimeTimestampPosition;

    @Override
    public TimestampAssigner<org.apache.flink.types.Row> createTimestampAssigner(TimestampAssignerSupplier.Context context) {
        EventTimeTimestampAssigner timestampAssigner = new EventTimeTimestampAssigner();
        timestampAssigner.setTimeStampPosition(this.eventTimeTimestampPosition);
        return timestampAssigner;
    }
    @Override
    public WatermarkGenerator<org.apache.flink.types.Row> createWatermarkGenerator(WatermarkGeneratorSupplier.Context context) {
        EventTimeRowWatermarkGenerator watermarkGenerator = new EventTimeRowWatermarkGenerator();
        watermarkGenerator.setOutOfOrdernessDelay(this.outOfOrdernessDelay);
        return watermarkGenerator;
    }

    public void setOutOfOrdernessDelay(long outOfOrdernessDelay) {
        this.outOfOrdernessDelay = outOfOrdernessDelay;
    }
    public void setEventTimeTimestampPosition(int eventTimeTimestampPosition) {
        this.eventTimeTimestampPosition = eventTimeTimestampPosition;
    }
}
