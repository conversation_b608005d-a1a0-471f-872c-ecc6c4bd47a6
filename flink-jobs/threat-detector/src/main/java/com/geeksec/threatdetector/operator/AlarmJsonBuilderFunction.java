package com.geeksec.threatdetector.operator;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.common.utils.redis.RedisUtils;
import com.geeksec.threatdetector.function.alarm.builder.AlarmBuilders;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

/**
 * Converts {@link Row} objects containing labeled alarm data into JSON format.
 * This function is designed to be used as a sink or an intermediate step in a Flink pipeline,
 * preparing data for output or further processing. It may interact with external systems like Redis
 * to enrich or retrieve additional data needed for JSON construction.
 *
 * <AUTHOR>
 */
@Slf4j
public class AlarmJsonBuilderFunction extends RichFlatMapFunction<Row, JSONObject> {
    private static transient JedisPool jedisPool = null;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        // redis池 初始化.
        jedisPool = RedisUtils.initJedisPool();
        logger.info("生成jedisPool成功! {}", jedisPool.getNumIdle(), jedisPool.hashCode());
    }

    @Override
    public void close() throws Exception {
        super.close();
        if (!jedisPool.isClosed()) {
            jedisPool.close();
        }
    }

    @Override
    public void flatMap(Row LabelAlarmRow, Collector<JSONObject> collector) throws Exception {
        String Alarm_type = LabelAlarmRow.getFieldAs(0);
        JSONObject send_json;

        Jedis jedis = null;
        try {
            jedis = RedisUtils.getJedis();
            // 从db7获取pcap文件路径
            jedis.select(7);
            switch (Alarm_type) {
                case "挖矿病毒":
                    send_json = AlarmBuilders.getMineAlarmBuilder().getAlarmJson(LabelAlarmRow, jedis);
                    collector.collect(send_json);
                    break;
                case "尝试挖矿连接":
                    send_json = AlarmBuilders.getDnsMineAlarmBuilder().getAlarmJson(LabelAlarmRow, jedis);
                    collector.collect(send_json);
                    break;
                case "指纹探测":
                    send_json = AlarmBuilders.getFingerprintDetectAlarmBuilder().getAlarmJson(LabelAlarmRow, jedis);
                    collector.collect(send_json);
                    break;
                case "扫描探测":
                    send_json = AlarmBuilders.getScanActivityAlarmBuilder().getAlarmJson(LabelAlarmRow, jedis);
                    collector.collect(send_json);
                    break;
                case "随机指纹探测":
                    send_json = AlarmBuilders.getFingerprintRandomAlarmBuilder().getAlarmJson(LabelAlarmRow, jedis);
                    collector.collect(send_json);
                    break;
                case "dns隧道检测":
                    send_json = AlarmBuilders.getDnsTunnelAlarmBuilder().getAlarmJson(LabelAlarmRow, jedis);
                    collector.collect(send_json);
                    break;
                case "通用隧道检测":
                    send_json = AlarmBuilders.getBasicTunnelAlarmBuilder().getAlarmJson(LabelAlarmRow, jedis);
                    collector.collect(send_json);
                    break;
                case "加密隧道检测":
                    send_json = AlarmBuilders.getEncryptedTunnelAlarmBuilder().getAlarmJson(LabelAlarmRow, jedis);
                    collector.collect(send_json);
                    break;
                case "加密隧道攻击检测":
                    send_json = AlarmBuilders.getEncryptedTunnelAttackAlarmBuilder().getAlarmJson(LabelAlarmRow, jedis);
                    collector.collect(send_json);
                    break;
                case "webshell检测":
                    send_json = AlarmBuilders.getHackToolAlarmBuilder().getAlarmJson(LabelAlarmRow, jedis);
                    collector.collect(send_json);
                    break;
                case "失陷主机检测":
                    send_json = AlarmBuilders.getHackToolAlarmBuilder().getAlarmJson(LabelAlarmRow, jedis);
                    collector.collect(send_json);
                    break;
                case "恶意软件活动":
                    send_json = AlarmBuilders.getHackToolAlarmBuilder().getAlarmJson(LabelAlarmRow, jedis); // Using HackToolAlarm as fallback
                    collector.collect(send_json);
                    break;
                case "未知远程控制协议":
                    send_json = AlarmBuilders.getHackToolAlarmBuilder().getAlarmJson(LabelAlarmRow, jedis); // Using HackToolAlarm as fallback
                    collector.collect(send_json);
                    break;
                case "webShell攻击检测":
                    // webShell 相关的告警产出
                    send_json = AlarmBuilders.getHackToolAlarmBuilder().getAlarmJson(LabelAlarmRow, jedis); // Using HackToolAlarm as fallback
                    collector.collect(send_json);
                    break;
                case "特定协议攻击工具":
                    // 加密流量检测 相关的告警产出
                    send_json = AlarmBuilders.getHackToolAlarmBuilder().getAlarmJson(LabelAlarmRow, jedis); // Using HackToolAlarm as fallback
                    collector.collect(send_json);
                    break;
                default:
                    logger.error("黑客工具出错");
                    break;
            }
        } catch (Exception e) {
            logger.info("获取最近短时告警统计数据失败， {}", e);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
    }
}
