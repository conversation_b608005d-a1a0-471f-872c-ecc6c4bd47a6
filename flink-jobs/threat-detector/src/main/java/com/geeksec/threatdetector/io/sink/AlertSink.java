package com.geeksec.threatdetector.io.sink;

import com.geeksec.common.config.ConfigurationManager;
import com.geeksec.threatdetector.function.alarm.AlarmDeduplicationProcessor;
import com.geeksec.threatdetector.model.event.alarm.AlarmEvent;
import com.geeksec.threatdetector.sink.es.ElasticsearchAlertSink;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.functions.sink.SinkFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 告警接收器工厂类
 * 用于创建各种告警接收器
 *
 * <AUTHOR>
 */
public class AlertSink {

    private static final Logger logger = LoggerFactory.getLogger(AlertSink.class);

    /**
     * 添加告警接收器
     *
     * @param alerts 告警事件数据流
     */
    public static void addAlertSink(DataStream<AlarmEvent> alerts) {
        // 获取配置信息
        var config = ConfigurationManager.getConfig();
        String redisHost = config.get("redis.host", "localhost");
        int redisPort = config.getInt("redis.port", 6379);
        String redisPassword = config.get("redis.password", "");
        int deduplicationWindowSeconds = config.getInt("alert.deduplication.window.seconds", 3600); // 默认1小时

        // 是否启用告警去重
        boolean enableDeduplication = config.getBoolean("alert.deduplication.enabled", true);

        // 是否启用Elasticsearch输出
        boolean enableElasticsearch = config.getBoolean("alert.elasticsearch.enabled", true);

        // 是否启用日志输出
        boolean enableLogSink = config.getBoolean("alert.log.enabled", true);

        DataStream<AlarmEvent> processedAlerts = alerts;

        // 添加告警去重处理
        if (enableDeduplication) {
            logger.info("启用告警去重处理，去重窗口: {}秒", deduplicationWindowSeconds);

            AlarmDeduplicationProcessor deduplicationProcessor = new AlarmDeduplicationProcessor(redisHost, redisPort,
                    redisPassword, deduplicationWindowSeconds);

            SingleOutputStreamOperator<AlarmEvent> dedupedAlerts = alerts.process(deduplicationProcessor);

            // 获取重复告警的侧输出流并记录日志
            DataStream<AlarmEvent> duplicateAlerts = dedupedAlerts
                    .getSideOutput(AlarmDeduplicationProcessor.DUPLICATE_ALERTS);

            if (enableLogSink) {
                duplicateAlerts.addSink(new DuplicateAlertLogSink());
            }

            processedAlerts = dedupedAlerts;
        }

        // 添加Elasticsearch输出
        if (enableElasticsearch) {
            logger.info("启用Elasticsearch告警输出");
            String[] esHosts = config.get("elasticsearch.hosts", "localhost:9200").split(",");
            String indexPrefix = config.get("elasticsearch.index.prefix", "threat-alerts");
            int bulkSize = config.getInt("elasticsearch.bulk.size", 1000);
            int bulkFlushInterval = config.getInt("elasticsearch.bulk.flush.interval", 5000);

            processedAlerts.sinkTo(ElasticsearchAlertSink.createElasticsearchSink(
                    esHosts, indexPrefix, bulkSize, bulkFlushInterval));
        }

        // 添加日志输出
        if (enableLogSink) {
            logger.info("启用告警日志输出");
            processedAlerts.addSink(new AlertLogSink());
        }
    }

    /**
     * 告警日志输出接收器
     * 将告警事件输出到日志
     */
    private static class AlertLogSink implements SinkFunction<AlarmEvent> {

        private static final long serialVersionUID = 1L;

        @Override
        public void invoke(AlarmEvent value, Context context) {
            logger.warn("告警事件: 类型={}, 名称={}, 级别={}, 源IP={}, 目标IP={}",
                    value.getType(),
                    value.getName(),
                    value.getSeverity(),
                    value.getSourceIp(),
                    value.getDestinationIp());
        }
    }

    /**
     * 重复告警日志输出接收器
     * 将重复的告警事件输出到日志
     */
    private static class DuplicateAlertLogSink implements SinkFunction<AlarmEvent> {

        private static final long serialVersionUID = 1L;

        @Override
        public void invoke(AlarmEvent value, Context context) {
            logger.debug("重复告警事件: 类型={}, 名称={}, 级别={}, 源IP={}, 目标IP={}",
                    value.getType(),
                    value.getName(),
                    value.getSeverity(),
                    value.getSourceIp(),
                    value.getDestinationIp());
        }
    }
}
