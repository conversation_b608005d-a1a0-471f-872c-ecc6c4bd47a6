package com.geeksec.threatdetector.infra.elasticsearch;

import com.geeksec.common.config.ConfigurationManager;
import com.geeksec.threatdetector.common.constant.ConfigConstants;
import org.apache.commons.pool.BasePoolableObjectFactory;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.http.HttpHost;
import org.apache.http.client.config.RequestConfig;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;

/**
 * <AUTHOR>
 * @Date 2022/10/21
 */

public class ESPoolFactory extends BasePoolableObjectFactory<RestHighLevelClient> {

    private static final ParameterTool CONFIG = ConfigurationManager.getConfig();
    public static String esHost = CONFIG.get(ConfigConstants.ELASTICSEARCH_HOST, "localhost");
    public static int esPort = CONFIG.getInt(ConfigConstants.ELASTICSEARCH_PORT, 9200);

    @Override
    public RestHighLevelClient makeObject() {
        RestHighLevelClient client = null;
        try {
            HttpHost httpHosts = new HttpHost(esHost, esPort, "http");
            RestClientBuilder builder = RestClient.builder(httpHosts)
                    .setRequestConfigCallback(new RestClientBuilder.RequestConfigCallback() {
                        @Override
                        public RequestConfig.Builder customizeRequestConfig(
                                RequestConfig.Builder requestConfigBuilder) {
                            requestConfigBuilder.setConnectTimeout(90000000);// 连接超时,防止长时间未操作断开es连接
                            requestConfigBuilder.setSocketTimeout(90000000);
                            requestConfigBuilder.setConnectionRequestTimeout(1000000);
                            return requestConfigBuilder;
                        }
                    });
            client = new RestHighLevelClient(builder);

        } catch (Exception e) {
            e.printStackTrace();
        }
        // logger.info("对象被创建了"+client);
        return client;
    }

    @Override
    public boolean validateObject(RestHighLevelClient restHighLevelClient) {
        return true;
    }

    @Override
    public void activateObject(RestHighLevelClient restHighLevelClient) throws Exception {
        // logger.info("对象被激活了"+restHighLevelClient);
    }

    @Override
    public void destroyObject(RestHighLevelClient restHighLevelClient) throws Exception {
        // logger.info("对象被销毁了"+restHighLevelClient);
    }

    @Override
    public void passivateObject(RestHighLevelClient restHighLevelClient) throws Exception {
        // logger.info("回收并进行钝化操作"+restHighLevelClient);
    }
}
