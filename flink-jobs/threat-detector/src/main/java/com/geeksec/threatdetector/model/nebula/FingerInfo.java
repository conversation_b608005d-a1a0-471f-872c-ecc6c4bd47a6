package com.geeksec.threatdetector.model.nebula;


import lombok.Data;

/**
 * Represents fingerprint information for SSL/TLS fingerprinting.
 * 
 * <AUTHOR>
 * <AUTHOR>
 */
@Data
public class FingerInfo {
    /** Database ID */
    private Long id;
    
    /** Fingerprint ID */
    private String fingerId;
    
    /** JA3 hash of the fingerprint */
    private String ja3Hash;
    
    /** Description of the fingerprint */
    private String desc;
    
    /** Type of the fingerprint */
    private String type;

    /**
     * Gets the fingerprint ID.
     * 
     * @return the fingerprint ID
     */
    public String getFingerId() {
        return fingerId;
    }

    /**
     * Sets the fingerprint ID.
     * 
     * @param fingerId the fingerprint ID to set
     */
    public void setFingerId(String fingerId) {
        this.fingerId = fingerId;
    }

    /**
     * Gets the JA3 hash.
     * 
     * @return the JA3 hash
     */
    public String getJa3Hash() {
        return ja3Hash;
    }

    /**
     * Sets the JA3 hash.
     * 
     * @param ja3Hash the JA3 hash to set
     */
    public void setJa3Hash(String ja3Hash) {
        this.ja3Hash = ja3Hash;
    }

    /**
     * Gets the description.
     * 
     * @return the description
     */
    public String getDesc() {
        return desc;
    }

    /**
     * Sets the description.
     * 
     * @param desc the description to set
     */
    public void setDesc(String desc) {
        this.desc = desc;
    }

    /**
     * Gets the type.
     * 
     * @return the type
     */
    public String getType() {
        return type;
    }

    /**
     * Sets the type.
     * 
     * @param type the type to set
     */
    public void setType(String type) {
        this.type = type;
    }

    /**
     * Gets the database ID.
     * 
     * @return the database ID
     */
    public Long getId() {
        return id;
    }

    /**
     * Sets the database ID.
     * 
     * @param id the database ID to set
     */
    public void setId(Long id) {
        this.id = id;
    }
}
