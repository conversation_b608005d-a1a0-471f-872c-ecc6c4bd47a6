package com.geeksec.threatdetector.util;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.common.config.ConfigurationManager;
import com.geeksec.common.utils.crypto.CryptoUtils;
import com.geeksec.common.utils.http.HttpUtils;
import com.geeksec.threatdetector.common.constant.ConfigConstants;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2022/12/16
 */

public class AlarmUtils {
    public static Logger logger = LoggerFactory.getLogger(AlarmUtils.class);
    private static final ParameterTool CONFIG = ConfigurationManager.getConfig();
    public static final String WS_HOST = CONFIG.get(ConfigConstants.ES_WS_HOST);

    // 临时定义 Alarm_Info_Map
    private static final Map<String, Map<String, String>> Alarm_Info_Map = new HashMap<>();

    public static Map<String, Object> getDefaultAlarmMap() {
        Map<String, Object> Default_alarm_map = new HashMap<>();
        Default_alarm_map.put("alarm_reason", new ArrayList<>());
        Default_alarm_map.put("alarm_status", 0);// 0:未处理 1:确认 2:误报
        Default_alarm_map.put("alarm_type", new ArrayList<>());// 防御/模型/规则
        // Default_alarm_map.put("attack_chain_name",new ArrayList<>());//
        // 侦查探测/漏洞利用/安装植入/命令控制/目标行动/其他
        Default_alarm_map.put("targets", new ArrayList<>());
        Default_alarm_map.put("victim", new ArrayList<>());
        Default_alarm_map.put("attacker", new ArrayList<>());
        Default_alarm_map.put("attack_family", new ArrayList<>());
        Default_alarm_map.put("ioc", new ArrayList<>());
        return Default_alarm_map;
    }

    public static Map<String, Object> getSendData(Map<String, Object> alarm_data, String taskId, String batchId) {
        Map<String, Object> send_data = new HashMap<>();
        send_data.put("TaskId", taskId);
        send_data.put("BatchId", batchId);
        alarm_data.put("task_id", Integer.valueOf(taskId));
        send_data.put("Alarm", alarm_data);
        return send_data;
    }

    /**
     * 创建标签边的行记录
     * 
     * @param fieldValue 字段值
     * @param labelCode  标签代码
     * @return 包含标签边的行记录
     */
    public static Row getLabelEdgeRow(Object fieldValue, String labelCode) {
        return Row.of(fieldValue, labelCode, System.currentTimeMillis() / 1000);
    }

    public static void write_alarm(JSONObject send_json) {
        JSONObject responseData = HttpUtils.sendPost(WS_HOST, send_json);
        logger.info("写入ES告警返回结果{}", responseData);
    }

    public static void write_alarm_noreturn(JSONObject send_json) {
        HttpUtils.sendPostOnly(WS_HOST, send_json);
    }

    public static Map<String, Object> getKnownAlarmInfo(Row InfoRow) {
        String Alarm_type = InfoRow.getFieldAs(0);

        // TODO 此处加入告警类提取告警中的共性字段

        Map<String, Object> Know_info = new HashMap<>();
        Map<String, String> alarm_know_data = Alarm_Info_Map.get(Alarm_type);
        Know_info.put("alarm_knowledge_id", alarm_know_data.get("alarm_knowledge_id"));
        Know_info.put("attack_level", alarm_know_data.get("attack_level"));
        Know_info.put("alarm_name", alarm_know_data.get("alarm_name"));
        Know_info.put("alarm_principle", alarm_know_data.get("alarm_principle"));
        Know_info.put("attack_chain_name", alarm_know_data.get("attack_chain_name"));
        long time = System.currentTimeMillis();

        String input = "localhost" + String.valueOf(time) + UUID.randomUUID().toString();
        String alarmId = generateAlarmIdByString(input);
        Know_info.put("alarm_id", alarmId);

        // 本次新增的一些属性
        Know_info.put("mitre_chain_name", alarm_know_data.get("mitre_chain_name"));
        Know_info.put("level", alarm_know_data.get("level"));

        Long currentTime = System.currentTimeMillis();
        int current_time = new BigDecimal(currentTime).divide(new BigDecimal(1000)).intValue();
        Know_info.put("time", current_time);
        return Know_info;
    }

    public static Row getLabelEdgeRow(Object src, Object dst) {
        Row resultRow = new Row(5);
        resultRow.setField(0, src);
        resultRow.setField(1, dst);
        resultRow.setField(2, 0);
        resultRow.setField(3, "Add_Label");
        resultRow.setField(4, "");
        return resultRow;
    }

    public static Row writeFingerAlarm(String AlarmTypeText, String FingerTypeText, Row fingerTagRow,
            String finger_Label) {
        Row alarm_row = new Row(9);
        String ja3 = fingerTagRow.getFieldAs(2); // Value can be null if field is null
        alarm_row.setField(0, AlarmTypeText);
        alarm_row.setField(1, FingerTypeText);
        alarm_row.setField(2, fingerTagRow.getField(5));// sip
        alarm_row.setField(3, fingerTagRow.getField(6));// dip
        Object fingerIdObj = fingerTagRow.getField(1);
        alarm_row.setField(4, fingerIdObj != null ? fingerIdObj.toString() : "");// fingerID
        alarm_row.setField(5, finger_Label);
        alarm_row.setField(6, fingerTagRow.getField(8));// 会话ID
        alarm_row.setField(7, fingerTagRow.getField(9));// es_key
        alarm_row.setField(8, fingerTagRow.getField(10));
        // JSONObject send_json =
        // write_fingerDetectAlarm.get_fingerDetect_alarmJson(alarm_row);
        // AlarmUtils.write_alarm(send_json);
        logger.info("指纹检测告警，告警类型：{}，指纹ja3：{}", AlarmTypeText, (ja3 != null ? ja3 : "N/A"));
        return alarm_row;
    }

    public static String generateAlarmIdByString(String str) {
        if (str == null) {
            // CryptoUtils.md5会为null输入返回null，如果需要UUID作为回退，则在此处理
            logger.warn("Input string for generateAlarmIdByString is null, returning UUID as fallback.");
            return UUID.randomUUID().toString();
        }
        String md5Hash = CryptoUtils.md5(str);
        if (md5Hash == null) {
            // CryptoUtils.md5内部如果哈希失败（不太可能对于MD5标准算法），也可能返回null
            logger.error("MD5 hash generation failed for string: {}. Returning UUID as fallback.", str);
            return UUID.randomUUID().toString();
        }
        return md5Hash;
    }


    public static List<String> getAttackChainList(List<Map<String, String>> victim_list_info,
            List<Map<String, String>> attacker_list_info, List<String> labels, String alarm_id) {
        List<String> victim_list = new ArrayList<>();
        List<String> attacker_list = new ArrayList<>();
        for (Map<String, String> vitim_info : victim_list_info) {
            victim_list.add(vitim_info.get("ip"));
        }
        for (Map<String, String> attacker_info : attacker_list_info) {
            attacker_list.add(attacker_info.get("ip"));
        }
        List<String> attack_chain_list = new ArrayList<>();
        for (String victim : victim_list) {
            for (String attacker : attacker_list) {
                if (labels.size() != 0) {
                    for (String label : labels) {
                        String attack_chain = victim + "_" + attacker + "_" + label;
                        attack_chain_list.add(attack_chain);
                    }
                } else {
                    String attack_chain = victim + "_" + attacker + "_" + alarm_id;
                    attack_chain_list.add(attack_chain);
                }

            }
        }
        return attack_chain_list;
    }

    public static List<String> getPcapFileList(Collection<String> alarmSessionList, Jedis jedis) {
        Set<String> pcapFileList = new HashSet<>();
        for (String alarmSession : alarmSessionList) {
            Set<String> pcapFileSet = jedis.smembers(alarmSession);
            if (pcapFileSet != null) {
                pcapFileList.addAll(pcapFileSet);
            }
        }
        return new ArrayList<>(pcapFileList);
    }
}
