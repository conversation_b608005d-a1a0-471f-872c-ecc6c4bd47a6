package com.geeksec.threatdetector.model.nebula;

/**
 * <AUTHOR>
 * @Description：
 */
public class BaseEdge {

    /**
     * 起始VID
     */
    private String srcId;

    /**
     * 结束VID
     */
    private String dstId;

    /**
     * 最早发现时间
     */
    private Integer firstSeen;

    /**
     * 最晚发现时间
     */
    private Integer lastSeen;

    /**
     * 会话出现次数
     */
    private Long sessionCnt;

    // Explicit getters (already present)
    public String getSrcId() {
        return srcId;
    }

    public String getDstId() {
        return dstId;
    }

    public Integer getFirstSeen() {
        return firstSeen;
    }

    public Integer getLastSeen() {
        return lastSeen;
    }

    public Long getSessionCnt() {
        return sessionCnt;
    }

    // Explicit setters (added)
    public void setSrcId(String srcId) {
        this.srcId = srcId;
    }

    public void setDstId(String dstId) {
        this.dstId = dstId;
    }

    public void setFirstSeen(Integer firstTime) {
        this.firstSeen = firstTime;
    }

    public void setLastSeen(Integer lastTime) {
        this.lastSeen = lastTime;
    }

    public void setSessionCnt(Long sessionCnt) {
        this.sessionCnt = sessionCnt;
    }
}
