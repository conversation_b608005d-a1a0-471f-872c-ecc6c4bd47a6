package com.geeksec.threatdetector.operator.alarm;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.threatdetector.model.event.alarm.AlarmEvent;
import com.geeksec.threatdetector.model.event.protocol.ProtocolEvent;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 威胁告警生成器抽象基类
 * 负责将各种威胁事件转换为告警事件
 *
 * <AUTHOR>
 * @since 2023/01/01
 */
public abstract class AbstractThreatAlarmGenerator<T extends ProtocolEvent> extends RichFlatMapFunction<T, AlarmEvent> {
    
    private static final Logger logger = LoggerFactory.getLogger(AbstractThreatAlarmGenerator.class);
    
    /**
     * 生成告警事件
     *
     * @param event 输入事件
     * @param collector 收集器
     * @throws Exception 异常
     */
    @Override
    public void flatMap(T event, Collector<AlarmEvent> collector) throws Exception {
        try {
            // 检测是否需要生成告警
            if (detectThreat(event)) {
                // 生成告警事件
                AlarmEvent alertEvent = convertToAlarmEvent(event);
                if (alertEvent != null) {
                    // 收集告警事件
                    collector.collect(alertEvent);
                    // 记录告警日志
                    logAlarm(alertEvent);
                }
            }
        } catch (Exception e) {
            logger.error("生成告警事件时发生错误: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 检测是否需要生成告警
     *
     * @param event 输入事件
     * @return 是否需要生成告警
     */
    protected abstract boolean detectThreat(T event);
    
    /**
     * 生成告警事件
     *
     * @param event 输入事件
     * @return 告警事件
     */
    protected abstract AlarmEvent convertToAlarmEvent(T event);
    
    /**
     * 记录告警日志
     *
     * @param alertEvent 告警事件
     */
    protected void logAlarm(AlarmEvent alertEvent) {
        logger.warn("生成告警: 类型={}, 名称={}, 级别={}, 源IP={}, 目标IP={}",
                alertEvent.getType(),
                alertEvent.getName(),
                alertEvent.getSeverity(),
                alertEvent.getSourceIp(),
                alertEvent.getDestinationIp());
    }
    
    /**
     * 创建基础告警事件
     *
     * @param alertType 告警类型
     * @param alertName 告警名称
     * @param alertLevel 告警级别
     * @param description 告警描述
     * @param solution 解决方案
     * @param sourceIp 源IP
     * @param sourcePort 源端口
     * @param destinationIp 目标IP
     * @param destinationPort 目标端口
     * @return 告警事件
     */
    protected AlarmEvent createBaseAlertEvent(
            String alertType,
            String alertName,
            String alertLevel,
            String description,
            String solution,
            String sourceIp,
            Integer sourcePort,
            String destinationIp,
            Integer destinationPort) {
        
        AlarmEvent alertEvent = new AlarmEvent();
        
        // 设置基本属性
        alertEvent.setId(UUID.randomUUID().toString());
        alertEvent.setType(alertType);
        alertEvent.setName(alertName);
        alertEvent.setSeverity(alertLevel);
        alertEvent.setDescription(description);
        alertEvent.setSolution(solution);
        
        // 设置IP和端口信息
        alertEvent.setSourceIp(sourceIp);
        alertEvent.setSourcePort(sourcePort);
        alertEvent.setDestinationIp(destinationIp);
        alertEvent.setDestinationPort(destinationPort);
        
        // 设置时间戳
        alertEvent.setTimestamp(System.currentTimeMillis());
        
        // 初始化详细信息
        alertEvent.setDetails(new HashMap<>());
        
        return alertEvent;
    }
    
    /**
     * 将JSON对象转换为告警事件
     *
     * @param jsonObject JSON对象
     * @return 告警事件
     */
    protected AlarmEvent convertJsonToAlertEvent(JSONObject jsonObject) {
        if (jsonObject == null) {
            return null;
        }
        
        AlarmEvent alertEvent = new AlarmEvent();
        
        // 设置基本属性
        alertEvent.setId(jsonObject.getString("alarm_id"));
        alertEvent.setType(jsonObject.getString("alarm_type"));
        alertEvent.setName(jsonObject.getString("alarm_name"));
        alertEvent.setSeverity(jsonObject.getString("alarm_level"));
        alertEvent.setDescription(jsonObject.getString("alarm_desc"));
        alertEvent.setSolution(jsonObject.getString("alarm_handle_method"));
        
        // 设置IP和端口信息
        alertEvent.setSourceIp(jsonObject.getString("sIp"));
        alertEvent.setSourcePort(jsonObject.getInteger("sPort"));
        alertEvent.setDestinationIp(jsonObject.getString("dIp"));
        alertEvent.setDestinationPort(jsonObject.getInteger("dPort"));
        
        // 设置时间戳
        alertEvent.setTimestamp(jsonObject.getLongValue("alarm_time"));
        
        // 设置详细信息
        Map<String, Object> details = new HashMap<>();
        details.put("tranProto", jsonObject.getString("tranProto"));
        details.put("appProto", jsonObject.getString("appProto"));
        details.put("httpDomain", jsonObject.getString("httpDomain"));
        details.put("sniDomain", jsonObject.getString("sniDomain"));
        details.put("alarm_reason", jsonObject.get("alarm_reason"));
        details.put("attack_family", jsonObject.get("attack_family"));
        details.put("targets", jsonObject.get("targets"));
        details.put("victim", jsonObject.get("victim"));
        details.put("attacker", jsonObject.get("attacker"));
        details.put("alarm_related_label", jsonObject.get("alarm_related_label"));
        details.put("attack_route", jsonObject.get("attack_route"));
        details.put("alarm_session_list", jsonObject.get("alarm_session_list"));
        details.put("attack_chain_list", jsonObject.get("attack_chain_list"));
        details.put("model_id", jsonObject.getString("model_id"));
        details.put("PcapFileList", jsonObject.get("PcapFileList"));
        
        alertEvent.setDetails(details);
        
        return alertEvent;
    }
}
