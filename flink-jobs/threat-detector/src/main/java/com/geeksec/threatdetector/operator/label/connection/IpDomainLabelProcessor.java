package com.geeksec.threatdetector.operator.label.connection;

import com.geeksec.common.utils.knowledgebase.PublicSuffixManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashSet;
import java.util.Set;

/**
 * IP域名标签处理器
 * 负责处理IP和域名之间的关系标签，包括挖矿域名检测和DNS服务器分类
 *
 * <AUTHOR>
 */
@Slf4j
public class IpDomainLabelProcessor extends RichFlatMapFunction<Row, Row> {
    
    private static final Logger logger = LoggerFactory.getLogger(IpDomainLabelProcessor.class);
    
    /**
     * 挖矿域名列表
     */
    public static final Set<String> MINE_DOMAIN_LIST = new HashSet<>();
    
    /**
     * 良性DNS服务器映射
     */
    public static final Set<String> BenignDNSServer_Map = new HashSet<>();
    
    private PublicSuffixManager publicSuffixManager;
    
    static {
        // 初始化挖矿域名列表
        initMineDomainList();
        
        // 初始化良性DNS服务器列表
        initBenignDnsServerList();
    }
    
    /**
     * 初始化挖矿域名列表
     */
    private static void initMineDomainList() {
        // 添加一些常见的挖矿域名
        MINE_DOMAIN_LIST.add("coinhive.com");
        MINE_DOMAIN_LIST.add("coin-hive.com");
        MINE_DOMAIN_LIST.add("jsecoin.com");
        MINE_DOMAIN_LIST.add("cryptoloot.pro");
        MINE_DOMAIN_LIST.add("webminepool.com");
        MINE_DOMAIN_LIST.add("minero.cc");
        MINE_DOMAIN_LIST.add("crypto-loot.com");
        MINE_DOMAIN_LIST.add("coinerra.com");
        MINE_DOMAIN_LIST.add("papoto.com");
        MINE_DOMAIN_LIST.add("minescripts.info");
        
        log.info("初始化挖矿域名列表完成，共{}个域名", MINE_DOMAIN_LIST.size());
    }
    
    /**
     * 初始化良性DNS服务器列表
     */
    private static void initBenignDnsServerList() {
        // 添加一些常见的良性DNS服务器
        BenignDNSServer_Map.add("*******");        // Google DNS
        BenignDNSServer_Map.add("*******");        // Google DNS
        BenignDNSServer_Map.add("*******");        // Cloudflare DNS
        BenignDNSServer_Map.add("*******");        // Cloudflare DNS
        BenignDNSServer_Map.add("**************"); // OpenDNS
        BenignDNSServer_Map.add("**************"); // OpenDNS
        BenignDNSServer_Map.add("***************"); // 114 DNS
        BenignDNSServer_Map.add("*********");      // 阿里DNS
        BenignDNSServer_Map.add("*********");      // 阿里DNS
        BenignDNSServer_Map.add("************");   // 腾讯DNS
        
        log.info("初始化良性DNS服务器列表完成，共{}个服务器", BenignDNSServer_Map.size());
    }
    
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        publicSuffixManager = PublicSuffixManager.getInstance();
        log.info("IpDomainEdgeRowLabelFlatMap初始化完成");
    }
    
    @Override
    public void flatMap(Row value, Collector<Row> out) throws Exception {
        try {
            if (value == null || value.getArity() < 2) {
                return;
            }
            
            // 获取IP和域名
            String ip = value.getField(0) != null ? value.getField(0).toString() : null;
            String domain = value.getField(1) != null ? value.getField(1).toString() : null;
            
            if (ip == null || domain == null || ip.trim().isEmpty() || domain.trim().isEmpty()) {
                return;
            }
            
            // 创建输出行
            Row outputRow = new Row(value.getArity() + 2); // 增加两个字段：标签类型和标签值
            
            // 复制原始数据
            for (int i = 0; i < value.getArity(); i++) {
                outputRow.setField(i, value.getField(i));
            }
            
            // 添加标签信息
            String labelType = determineLabelType(ip, domain);
            String labelValue = determineLabelValue(ip, domain, labelType);
            
            outputRow.setField(value.getArity(), labelType);
            outputRow.setField(value.getArity() + 1, labelValue);
            
            out.collect(outputRow);
            
        } catch (Exception e) {
            log.error("处理IP域名边标签时发生错误", e);
        }
    }
    
    /**
     * 确定标签类型
     */
    private String determineLabelType(String ip, String domain) {
        // 检查是否为挖矿域名
        if (publicSuffixManager.isPslAvailable()) {
            String registrableDomain = publicSuffixManager.getRegistrableDomain(domain);
            if (registrableDomain != null && MINE_DOMAIN_LIST.contains(registrableDomain)) {
                return "MINING";
            }
        }
        
        // 检查是否为良性DNS服务器
        if (BenignDNSServer_Map.contains(ip)) {
            return "BENIGN_DNS";
        }
        
        // 默认为正常
        return "NORMAL";
    }
    
    /**
     * 确定标签值
     */
    private String determineLabelValue(String ip, String domain, String labelType) {
        switch (labelType) {
            case "MINING":
                return "1";
            case "BENIGN_DNS":
                return "0";
            default:
                return "0";
        }
    }
}
