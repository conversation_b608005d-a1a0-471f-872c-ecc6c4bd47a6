package com.geeksec.threatdetector.operator.analysis.webshell;

// 不再需要SpecProtocolEnum枚举

import com.geeksec.threatdetector.model.event.alarm.AlarmEvent;
import com.geeksec.threatdetector.model.event.protocol.HttpEvent;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.regex.Pattern;

/**
 * 加密webshell协议识别
 *
 * <AUTHOR>
 */
/**
 * 加密WebShell检测器
 * 用于检测加密的WebShell攻击流量特征
 *
 * <AUTHOR>
 * @since 2023/01/01
 */
public class EncryptedWebShellDetector extends RichFlatMapFunction<HttpEvent, AlarmEvent> {
    private static final Logger logger = LoggerFactory.getLogger(EncryptedWebShellDetector.class);
    public static final String B374K_STR = "(?=.*pass=)(?=.*; s_self=)(?=.*; cwd=)";
    static final String WEBACOO_STR = "(?=.*cm=)(?=.*; cn=)(?=.*; cp=)";
    static final String JSP_MASTER_STR = "\n\n\n\n\n\n\n\n\n";
    static final String XISE_STR = "xise=%40eval%2F%2A%15%99%D0%21%03%19s%20%0B%CB%A8%DD%E3%A3%C5%C4";

    /**
     * 将十六进制字符串转换为字节数组
     */
    public static byte[] hexStringToByteArray(String hexString) {
        try {
            int len = hexString.length();
            byte[] data = new byte[len / 2];
            for (int i = 0; i < len; i += 2) {
                data[i / 2] = (byte) ((Character.digit(hexString.charAt(i), 16) << 4)
                        + Character.digit(hexString.charAt(i + 1), 16));
            }
            return data;
        } catch (Exception e) {
            logger.error("十六进制字符串转换失败: {}", hexString, e);
        }
        return new byte[0];
    }

    @Override
    public void flatMap(HttpEvent httpEvent, Collector<AlarmEvent> collector) throws Exception {
        try {
            // 检测基于响应体的加密WebShell
            if (detectResponsePayloadWebShell(httpEvent)) {
                AlarmEvent alert = createAlertEvent(httpEvent, "ENCRYPTED_WEBSHELL", "加密WebShell检测", "HIGH");
                collector.collect(alert);
                logger.warn("检测到加密WebShell访问: {} {}", httpEvent.getMethod(), httpEvent.getUri());
            }
            
            // 检测基于请求头的加密WebShell
            if (detectRequestHeaderWebShell(httpEvent)) {
                AlarmEvent alert = createAlertEvent(httpEvent, "ENCRYPTED_WEBSHELL", "加密WebShell检测", "HIGH");
                collector.collect(alert);
                logger.warn("检测到加密WebShell访问: {} {}", httpEvent.getMethod(), httpEvent.getUri());
            }
            
            // 检测基于Cookie的加密WebShell
            if (detectCookieWebShell(httpEvent)) {
                AlarmEvent alert = createAlertEvent(httpEvent, "ENCRYPTED_WEBSHELL", "加密WebShell检测", "HIGH");
                collector.collect(alert);
                logger.warn("检测到加密WebShell访问: {} {}", httpEvent.getMethod(), httpEvent.getUri());
            }
        } catch (Exception e) {
            logger.error("加密WebShell检测错误: {}", e.getMessage(), e);
        }
    }

    /**
     * 检测基于响应体的加密WebShell
     */
    private boolean detectResponsePayloadWebShell(HttpEvent httpEvent) {
        // 检测Xise WebShell
        if (detectXise(httpEvent)) {
            return true;
        }
        
        // 检测JspMaster WebShell
        String responseBody = httpEvent.getResponseBody();
        if (responseBody != null && JSP_MASTER_STR.equals(responseBody)) {
            logger.info("识别到Jspmaster");
            return true;
        }
        
        return false;
    }
    
    /**
     * 检测Xise WebShell
     */
    private boolean detectXise(HttpEvent httpEvent) {
        String requestBody = httpEvent.getRequestBody();
        if (requestBody != null) {
            // 分割负载内容
            String[] payloadSeq = requestBody.split("&");
            // 第一段负载，一般是登录语句和密码
            if (payloadSeq.length > 0 && XISE_STR.equals(payloadSeq[0])) {
                logger.info("识别到Xise");
                return true;
            }
        }
        return false;
    }
    
    /**
     * 检测基于请求头的加密WebShell
     */
    private boolean detectRequestHeaderWebShell(HttpEvent httpEvent) {
        // 检测哥斯拉WebShell
        String cookie = httpEvent.getHeaders().get("Cookie");
        if (cookie != null && cookie.endsWith(";")) {
            String responseBody = httpEvent.getResponseBody();
            if (responseBody != null && responseBody.endsWith("6C37")) {
                logger.info("识别到哥斯拉");
                return true;
            }
        }
        
        // 检测SharPyShell
        String contentType = httpEvent.getHeaders().get("Content-Type");
        if (contentType != null && contentType.startsWith("multipart/form-data; boundary=")) {
            String requestBody = httpEvent.getRequestBody();
            if (requestBody != null && requestBody.endsWith("Content-Disposition: form-da")) {
                logger.info("识别到SharPyShell");
                return true;
            }
        }
        
        // 检测天蝎WebShell
        if ("application/octet-stream".equals(contentType)) {
            // 由于单个事件无法检测多个请求的模式，这里简化处理
            // 实际应用中可能需要使用窗口函数或状态来跟踪多个请求
            logger.info("可能识别到天蝎");
            return true;
        }
        
        return false;
    }
    
    /**
     * 检测基于Cookie的加密WebShell
     */
    private boolean detectCookieWebShell(HttpEvent httpEvent) {
        String cookie = httpEvent.getHeaders().get("Cookie");
        if (cookie != null) {
            // 检测WeBacoo
            if (Pattern.compile(WEBACOO_STR).matcher(cookie).find()) {
                logger.info("识别到WeBacoo");
                return true;
            }
            
            // 检测B374k
            if (Pattern.compile(B374K_STR).matcher(cookie).find()) {
                logger.info("识别到B374k");
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 创建WebShell告警事件
     */
    private AlarmEvent createAlertEvent(HttpEvent httpEvent, String alertType, String alertName, String alertLevel) {
        AlarmEvent alert = new AlarmEvent();
        
        // 设置基本属性
        alert.setId(UUID.randomUUID().toString());
        alert.setType(alertType);
        alert.setName(alertName);
        alert.setSeverity(alertLevel);
        alert.setDescription("检测到可能的加密WebShell访问行为");
        alert.setSolution("1. 检查服务器上是否存在可疑文件\n2. 更新Web应用程序到最新版本\n3. 检查服务器日志以获取更多信息");
        
        // 设置源IP和端口
        alert.setSourceIp(httpEvent.getSourceIp());
        alert.setSourcePort(httpEvent.getSourcePort());
        alert.setDestinationIp(httpEvent.getDestinationIp());
        alert.setDestinationPort(httpEvent.getDestinationPort());
        
        // 设置时间戳
        alert.setTimestamp(System.currentTimeMillis());
        
        // 设置详细信息
        Map<String, Object> details = new HashMap<>();
        details.put("method", httpEvent.getMethod());
        details.put("uri", httpEvent.getUri());
        details.put("userAgent", httpEvent.getUserAgent());
        details.put("host", httpEvent.getHost());
        details.put("requestHeaders", httpEvent.getHeaders());
        details.put("requestBody", httpEvent.getRequestBody());
        alert.setDetails(details);
        
        return alert;
    }
    
    /**
     * 创建WebShell检测数据流
     */
    public static DataStream<AlarmEvent> detectEncryptedWebShell(DataStream<HttpEvent> httpEvents) {
        return httpEvents
                .flatMap(new EncryptedWebShellDetector())
                .name("加密WebShell检测")
                .setParallelism(2);
    }
}
