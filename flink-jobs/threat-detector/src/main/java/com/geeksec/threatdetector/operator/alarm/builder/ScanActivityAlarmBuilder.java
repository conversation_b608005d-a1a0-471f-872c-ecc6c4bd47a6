package com.geeksec.threatdetector.operator.alarm.builder;

import com.alibaba.fastjson.JSONObject;
import org.apache.flink.types.Row;
import redis.clients.jedis.Jedis;

/**
 * 扫描活动告警构建器
 * 用于构建扫描活动相关的告警信息
 *
 * <AUTHOR>
 * @since 2023/01/01
 */
public class ScanActivityAlarmBuilder extends BaseAlarmBuilder {
    
    /**
     * 单例实例
     */
    private static final ScanActivityAlarmBuilder INSTANCE = new ScanActivityAlarmBuilder();
    
    /**
     * 私有构造函数
     */
    private ScanActivityAlarmBuilder() {
        // 私有构造函数
    }
    
    /**
     * 获取单例实例
     *
     * @return 单例实例
     */
    public static ScanActivityAlarmBuilder getInstance() {
        return INSTANCE;
    }
    
    @Override
    public JSONObject getAlarmJson(Row alarmRow, Jedis jedis) {
        // 创建基础告警JSON
        JSONObject alarmJson = createBaseAlarmJson("scan_activity", null);
        
        // 添加特定于扫描活动告警的字段
        // 这里根据实际需求从alarmRow中提取并设置字段
        
        return alarmJson;
    }
}
