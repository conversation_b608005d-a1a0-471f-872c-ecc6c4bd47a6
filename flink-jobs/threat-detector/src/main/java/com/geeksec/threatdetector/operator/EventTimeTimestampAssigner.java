package com.geeksec.threatdetector.operator;

import org.apache.flink.api.common.eventtime.TimestampAssigner;
import org.apache.flink.types.Row;

/**
 * Assigns timestamps to {@link Row} elements based on a specific field.
 *
 * <AUTHOR>
 * @Date 2022/12/8
 */
public class EventTimeTimestampAssigner implements TimestampAssigner<Row> {
    private int timeStampPosition;
    @Override
    public long extractTimestamp(Row row, long previousElementTimestamp) {
        // 获取当前记录的时间戳, 并将其从秒转换为毫秒
        // previousElementTimestamp 参数在此实现中未使用，但保留以符合接口定义
        long currentTs = Long.parseLong(row.getField(this.timeStampPosition).toString()) * 1000L;
        return currentTs; // 打上真实的时间戳，在后续watermark中再做延时处理
    }

    public void setTimeStampPosition(int timeStampPosition){
        this.timeStampPosition = timeStampPosition;
    }
}
