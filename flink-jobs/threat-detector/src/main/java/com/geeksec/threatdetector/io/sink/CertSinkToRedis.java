package com.geeksec.threatdetector.io.sink;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.geeksec.common.utils.redis.RedisUtils;
import com.geeksec.threatdetector.model.protocol.X509Cert;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

/**
 * <AUTHOR>
 * @Date 2024/9/12
 */

public class CertSinkToRedis extends RichSinkFunction<X509Cert> {
    private static transient JedisPool jedisPool = null;
    private final static Logger logger = LoggerFactory.getLogger(CertSinkToRedis.class);

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        // redis池 初始化.
        jedisPool = RedisUtils.initJedisPool();
        logger.info("生成jedisPool成功! {}", jedisPool.getNumIdle(),jedisPool.hashCode());
    }

    @Override
    public void close() throws Exception {
        super.close();
        if(!jedisPool.isClosed()){
            jedisPool.close();
        }
    }

    @Override
    public void invoke(X509Cert x509Cert, Context context) throws Exception {
        Jedis jedis = null;
        try{
            jedis = RedisUtils.getJedis(jedisPool);
            jedis.select(6);

            // 去掉cert的元数据，减轻redis压力
            JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(x509Cert));
            jsonObject.remove("cert");
            String simpleCertInfo = jsonObject.toJSONString();

            jedis.setex(x509Cert.getASN1SHA1().toString(),86400,simpleCertInfo);

        }catch (Exception e){
            logger.error("证书简单信息写入redis，读取redis失败，error:——{}——",e.toString());
        }finally {
            if (jedis != null){
                jedis.close();
            }
        }
    }
}
