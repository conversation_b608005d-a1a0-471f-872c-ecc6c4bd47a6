package com.geeksec.threatdetector.util.constants;

import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

import java.util.Map;

/**
 * <AUTHOR>
 * @Description：Flink 数据分流OutPutTag分流仓库
 */
public class LabelOutputTagConstants {

    public static final OutputTag<Map<String, Object>> CONNECT_INFO_PBMAP_INFO_OUTPUT = new OutputTag<>("CONNECT_INFO_PBMAP_INFO_OUTPUT",
            TypeInformation.of(Map.class));
    public static final OutputTag<Map<String, Object>> DNS_PBMAP_INFO_OUTPUT = new OutputTag<>("DNS_PBMAP_INFO_OUTPUT",
            TypeInformation.of(Map.class));
    public static final OutputTag<Map<String, Object>> SSL_PBMAP_INFO_OUTPUT = new OutputTag<>("SSL_PBMAP_INFO_OUTPUT",
            TypeInformation.of(Map.class));
    public static final OutputTag<Map<String, Object>> HTTP_PBMAP_INFO_OUTPUT = new OutputTag<>("HTTP_PBMAP_INFO_OUTPUT",
            TypeInformation.of(Map.class));
    public static final OutputTag<Map<String, Object>> NTP_PBMAP_INFO_OUTPUT = new OutputTag<>("NTP_PBMAP_INFO_OUTPUT",
            TypeInformation.of(Map.class));

    // 点Row Tag
    public static final OutputTag<Row> SSL_FINGER_INFO = new OutputTag<>("SSL_FINGER_INFO",
            TypeInformation.of(Row.class));

    // 边Row Tag
    // 会话部分
    public static final OutputTag<Row> CLIENT_HTTP_CONNECT_DOMAIN_EDGE = new OutputTag<>(
            "CLIENT_HTTP_CONNECT_DOMAIN_EDGE", TypeInformation.of(Row.class));
    public static final OutputTag<Row> SERVER_HTTP_CONNECT_DOMAIN_EDGE = new OutputTag<>(
            "SERVER_HTTP_CONNECT_DOMAIN_EDGE", TypeInformation.of(Row.class));

    // SSL部分
    public static final OutputTag<Row> CLIENT_SSL_CONNECT_DOMAIN_EDGE = new OutputTag<>(
            "CLIENT_SSL_CONNECT_DOMAIN_EDGE", TypeInformation.of(Row.class));
    public static final OutputTag<Row> SERVER_SSL_CONNECT_DOMAIN_EDGE = new OutputTag<>(
            "SERVER_SSL_CONNECT_DOMAIN_EDGE", TypeInformation.of(Row.class));

    // DNS部分
    public static final OutputTag<Row> CLIENT_QUERY_DOMAIN_EDGE = new OutputTag<>("CLIENT_QUERY_DOMAIN_EDGE",
            TypeInformation.of(Row.class));
    public static final OutputTag<Row> DNS_PARSE_TO_EDGE = new OutputTag<>("DNS_PARSE_TO_EDGE",
            TypeInformation.of(Row.class));

    // HTTP 部分

    // 标签相关
    public static final OutputTag<Row> SIP_DIP_FINGER_ROW = new OutputTag<>("SIP_DIP_FINGER_ROW",
            TypeInformation.of(Row.class));
    public static final OutputTag<Row> http_webLogin_info = new OutputTag<>("http_webLogin_info",
            TypeInformation.of(Row.class));
    public static final OutputTag<Row> Web_Login_Info = new OutputTag<>("Web_Login_Info",
            TypeInformation.of(Row.class));
    public static final OutputTag<Row> Port_Scan_Row = new OutputTag<>("Port_Scan_Row", TypeInformation.of(Row.class));
    public static final OutputTag<Row> DNS_Tunnel_Row = new OutputTag<>("DNS_Tunnel_Row",
            TypeInformation.of(Row.class));
    public static final OutputTag<Row> HTTP_Tunnel_Row = new OutputTag<>("HTTP_Tunnel_Row",
            TypeInformation.of(Row.class));
    public static final OutputTag<Row> TCP_Tunnel_Row = new OutputTag<>("TCP_Tunnel_Row",
            TypeInformation.of(Row.class));
    public static final OutputTag<Row> ICMP_Tunnel_Row = new OutputTag<>("ICMP_Tunnel_Row",
            TypeInformation.of(Row.class));
    public static final OutputTag<Row> NTP_Tunnel_Row = new OutputTag<>("NTP_Tunnel_Row",
            TypeInformation.of(Row.class));
    public static final OutputTag<Row> SSL_Tunnel_Row = new OutputTag<>("SSL_Tunnel_Row",
            TypeInformation.of(Row.class));
    public static final OutputTag<Row> CONNECT_INFO_DNS = new OutputTag<>("CONNECT_INFO_DNS",
            TypeInformation.of(Row.class));
    public static final OutputTag<Row> NEOREGEO_INFO = new OutputTag<>("NEOREGEO_INFO", TypeInformation.of(Row.class));
    public static final OutputTag<Row> RDP_INFO = new OutputTag<>("RDP_INFO", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ORACLE_INFO = new OutputTag<>("ORACLE_INFO", TypeInformation.of(Row.class));
    public static final OutputTag<Row> MYSQL_INFO = new OutputTag<>("MYSQL_INFO", TypeInformation.of(Row.class));
    public static final OutputTag<Row> SMB_INFO = new OutputTag<>("SMB_INFO", TypeInformation.of(Row.class));
    public static final OutputTag<Row> XRAY_FINGER_ROW = new OutputTag<>("XRAY_FINGER_ROW",
            TypeInformation.of(Row.class));
    public static final OutputTag<Row> SUO5_INFO = new OutputTag<>("SUO5_INFO", TypeInformation.of(Row.class));
    public static final OutputTag<Row> BEHINDER_INFO = new OutputTag<>("BEHINDER_INFO", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ANTSWORD_INFO = new OutputTag<>("ANTSWORD_INFO", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ANTSWORD_PHP_INFO = new OutputTag<>("ANTSWORD_PHP_INFO",
            TypeInformation.of(Row.class));
    public static final OutputTag<Row> TUNNEL_INFO = new OutputTag<>("TUNNEL_INFO", TypeInformation.of(Row.class));
    public static final OutputTag<Row> SRCP_INFO_ROW = new OutputTag<>("SRCP_INFO_ROW", TypeInformation.of(Row.class));
    public static final OutputTag<Row> URCP_INFO_ROW = new OutputTag<>("URCP_INFO_ROW", TypeInformation.of(Row.class));
    public static final OutputTag<Row> WEB_SHELL_INFO = new OutputTag<>("WEB_SHELL_INFO", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ENCRYPTED_TOOL_INFO = new OutputTag<>("ENCRYPTED_TOOL_INFO",
            TypeInformation.of(Row.class));
    public static final OutputTag<Row> TO_DESK_ROW = new OutputTag<>("TO_DESK_ROW", TypeInformation.of(Row.class));
}
