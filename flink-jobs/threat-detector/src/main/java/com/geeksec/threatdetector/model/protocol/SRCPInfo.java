package com.geeksec.threatdetector.model.protocol;

import com.geeksec.threatdetector.common.enums.ThreatTypeEnum;
import com.geeksec.threatdetector.model.connection.ConnectBasicInfo;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/8/1
 * SRCPInfo：Standard Remote Control Protocol Info
 */

@NoArgsConstructor
@AllArgsConstructor
public class SRCPInfo {

    public ConnectBasicInfo getConnectBasicInfo() {
        return connectBasicInfo;
    }

    public void setConnectBasicInfo(ConnectBasicInfo connectBasicInfo) {
        this.connectBasicInfo = connectBasicInfo;
    }

    public String getRCPType() {
        return RCPType;
    }

    public void setRCPType(String RCPType) {
        this.RCPType = RCPType;
    }

    public boolean isInBaseline() {
        return inBaseline;
    }

    public void setInBaseline(boolean inBaseline) {
        this.inBaseline = inBaseline;
    }

    public Set<String> getDIpdPorts() {
        return dIpdPorts;
    }

    public void setDIpdPorts(Set<String> dIpdPorts) {
        this.dIpdPorts = dIpdPorts;
    }

    public Set<String> getSIpdPorts() {
        return sIpdPorts;
    }

    public void setSIpdPorts(Set<String> sIpdPorts) {
        this.sIpdPorts = sIpdPorts;
    }
    /** 基础会话信息*/
    private ConnectBasicInfo connectBasicInfo;

    /** 远程控制协议类型,Remote Control Protocol type*/
    private String RCPType;

    /** 标签:
     * 标准远程控制协议下的C2行为
     **/
    public static final String SRCP_C2_TAG = ThreatTypeEnum.SRCP_C2.getCode();

    /** 支持的解析的远程控制协议（Labels）的标签列表
    向日葵，TeamViewer，ToDesk，AnyDesk，SecureCRT，
     XShell，FinalShell，PuTTY，MobaXterm，
     ToDesk包括精简版和正式版，向日葵包括完全版和SOS版
     24231 FinalShell远程连接
     24232 Mobaxterm远程连接
     24233 Putty远程连接
     24234 SecureCRT远程连接
     24235 Xshell远程连接
     */
    public static List<String> RCPLabelKnowledge = Arrays.asList(ThreatTypeEnum.FINAL_SHELL.getCode(), ThreatTypeEnum.MOBAXTERM.getCode(),
            ThreatTypeEnum.PUTTY.getCode(), ThreatTypeEnum.SECURE_CRT.getCode(), ThreatTypeEnum.XSHELL.getCode(),
            ThreatTypeEnum.XRKWQB.getCode(), ThreatTypeEnum.XRKZK.getCode(), ThreatTypeEnum.XRKBK.getCode(),
            ThreatTypeEnum.TDJJ.getCode(), ThreatTypeEnum.TEAMVIEWER.getCode(), ThreatTypeEnum.TDFZ.getCode(),
            ThreatTypeEnum.TDXZK.getCode(), ThreatTypeEnum.TDDZK.getCode(), ThreatTypeEnum.ANYDESK.getCode());

    /** 支持的解析的远程控制协议的协议类型（AppName）列表 */
    public static List<String> RCPProKnowledge = Arrays.asList("SSH","Telnet","Rlogin","VNC","RDP","XDMCP","RDP");

    private boolean inBaseline;
    private Set<String> dIpdPorts;
    private Set<String> sIpdPorts;
}
