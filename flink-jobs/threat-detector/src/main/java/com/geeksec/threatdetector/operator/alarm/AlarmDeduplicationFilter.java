package com.geeksec.threatdetector.operator.alarm;

import com.geeksec.threatdetector.model.event.alarm.AlarmEvent;
import org.apache.flink.api.common.functions.RichFilterFunction;
import org.apache.flink.api.common.state.MapState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 告警去重过滤器
 * 用于对告警事件进行去重处理
 *
 * <AUTHOR>
 */
public class AlarmDeduplicationFilter extends RichFilterFunction<AlarmEvent> {
    
    private static final Logger logger = LoggerFactory.getLogger(AlarmDeduplicationFilter.class);
    
    // 告警去重时间窗口，单位：毫秒
    private static final long DEDUPLICATION_WINDOW = 300000; // 5分钟
    
    // 用于存储告警事件的最后时间戳
    private transient MapState<String, Long> lastAlertTimeState;
    
    @Override
    public void open(Configuration parameters) throws Exception {
        // 初始化状态
        MapStateDescriptor<String, Long> descriptor = new MapStateDescriptor<>(
                "alert-deduplication-state",
                TypeInformation.of(new TypeHint<String>() {}),
                TypeInformation.of(new TypeHint<Long>() {})
        );
        lastAlertTimeState = getRuntimeContext().getMapState(descriptor);
    }
    
    @Override
    public boolean filter(AlarmEvent alertEvent) throws Exception {
        // 构建告警事件的唯一标识
        String alertKey = buildAlertKey(alertEvent);
        
        // 获取当前时间戳
        long currentTime = System.currentTimeMillis();
        
        // 检查是否存在相同的告警事件
        if (lastAlertTimeState.contains(alertKey)) {
            long lastTime = lastAlertTimeState.get(alertKey);
            
            // 如果在去重时间窗口内，则过滤掉
            if (currentTime - lastTime < DEDUPLICATION_WINDOW) {
                logger.debug("过滤掉重复告警: {}, 上次告警时间: {}", alertKey, lastTime);
                return false;
            }
        }
        
        // 更新告警事件的最后时间戳
        lastAlertTimeState.put(alertKey, currentTime);
        logger.debug("通过告警去重过滤: {}", alertKey);
        return true;
    }
    
    /**
     * 构建告警事件的唯一标识
     *
     * @param alertEvent 告警事件
     * @return 唯一标识
     */
    private String buildAlertKey(AlarmEvent alertEvent) {
        // 使用源IP、目标IP、告警类型、告警级别等信息构建唯一标识
        return String.format("%s_%s_%s_%s",
                alertEvent.getSourceIp(),
                alertEvent.getDestinationIp(),
                alertEvent.getType(),
                alertEvent.getSeverity());
    }
}
