package com.geeksec.threatdetector.operator.analysis.tunnel;

import com.geeksec.threatdetector.model.event.alarm.AlarmEvent;
import com.geeksec.threatdetector.model.event.protocol.HttpEvent;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.regex.Pattern;

/**
 * HTTP隧道检测函数
 *
 * <AUTHOR>
 */
/**
 * HTTP隧道检测器
 * 用于检测基于HTTP协议的隧道流量特征
 *
 * <AUTHOR>
 * @since 2023/01/01
 */
// @Slf4j // 移除
public class HttpTunnelDetector extends RichFlatMapFunction<HttpEvent, AlarmEvent> {
    private static final Logger log = LoggerFactory.getLogger(HttpTunnelDetector.class); // 新增 Logger
    
    private static final String HTTP_TUNNEL_ALERT_TYPE = "HTTP_TUNNEL_DETECTED";
    private static final String HTTP_TUNNEL_ALERT_NAME = "HTTP隧道检测";
    private static final String HTTP_TUNNEL_ALERT_LEVEL = "HIGH";
    
    // 异常请求头模式
    private static final Pattern ABNORMAL_HEADER_PATTERN = Pattern.compile("^[A-Za-z0-9+/]{50,}={0,2}$");
    
    // 异常Cookie模式
    private static final Pattern ABNORMAL_COOKIE_PATTERN = Pattern.compile("^[A-Za-z0-9+/]{30,}={0,2}$");
    
    @Override
    public void flatMap(HttpEvent httpEvent, Collector<AlarmEvent> out) throws Exception {
        try {
            // 检测HTTP隧道特征
            if (isHttpTunnel(httpEvent)) {
                // 创建告警事件
                AlarmEvent alert = createAlertEvent(httpEvent);
                out.collect(alert);
                
                log.warn("检测到HTTP隧道: {} {}", httpEvent.getMethod(), httpEvent.getUri());
            }
        } catch (Exception e) {
            log.error("HTTP隧道检测错误: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 检测HTTP请求是否包含隧道特征
     * @param httpEvent HTTP事件对象
     * @return 如果检测到隧道特征则返回true，否则返回false
     */
    private boolean isHttpTunnel(HttpEvent httpEvent) {
        // 1. 检查请求方法是否为CONNECT（常用于代理隧道）
        if ("CONNECT".equalsIgnoreCase(httpEvent.getMethod())) {
            return true;
        }
        
        // 2. 检查User-Agent是否为空或异常 (一些隧道工具可能不设置UA或使用固定UA)
        String userAgent = httpEvent.getUserAgent();
        if (userAgent == null || userAgent.isEmpty() || "Java/1.8.0_292".equals(userAgent) /*示例特定UA*/) {
            // 更复杂的UA检查逻辑可以放在这里
        }
        
        // 3. 检查请求头中是否存在异常的长字符串 (可能是编码后的数据)
        if (httpEvent.getHeaders() != null) {
            for (Map.Entry<String, String> entry : httpEvent.getHeaders().entrySet()) {
                // 排除常见的长头部，如Cookie, Authorization
                if (!"Cookie".equalsIgnoreCase(entry.getKey()) && !"Authorization".equalsIgnoreCase(entry.getKey())) {
                    if (entry.getValue() != null && ABNORMAL_HEADER_PATTERN.matcher(entry.getValue()).matches()) {
                        return true;
                    }
                }
            }
        }
        
        // 4. 检查Cookie中是否存在异常的长字符串
        String cookie = httpEvent.getCookie();
        if (cookie != null && ABNORMAL_COOKIE_PATTERN.matcher(cookie).matches()){
            // return true; // 暂时注释，因为Cookie本身就可能很长
        }

        // 5. 检查请求体或响应体大小是否异常 (非常大或非常小但持续)
        // 此处需要结合流量大小，暂时不实现
        
        // 6. 检查请求参数中是否包含特定命令或编码特征
        // 例如，检查URL参数或POST表单中的可疑模式
        // String requestBody = httpEvent.getRequestBody();
        // if (requestBody != null && requestBody.contains("some_tunnel_signature")) {
        //    return true;
        // }

        // 7. 检查是否存在连续重复字符过多的情况 (一种简单的隐蔽信道特征)
        if (httpEvent.getRequestBody() != null && hasExcessiveRepeatedChars(httpEvent.getRequestBody())) {
            return true;
        }
        if (httpEvent.getHeaders() != null) {
            for (String headerValue : httpEvent.getHeaders().values()) {
                if (headerValue != null && hasExcessiveRepeatedChars(headerValue)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 检查字符串中是否存在连续重复字符过多的情况。
     * @param text 要检查的文本
     * @return 如果存在过多重复字符则返回true
     */
    private boolean hasExcessiveRepeatedChars(String text) {
        if (text == null || text.length() < 20) { // 对于太短的文本不敏感
            return false;
        }
        int maxRepeatCount = 0;
        int repeatCount = 1;
        char prevChar = text.charAt(0);
        for (int i = 1; i < text.length(); i++) {
            char currentChar = text.charAt(i);
            if (currentChar == prevChar) {
                repeatCount++;
            } else {
                maxRepeatCount = Math.max(maxRepeatCount, repeatCount);
                repeatCount = 1;
                prevChar = currentChar;
            }
        }
        
        maxRepeatCount = Math.max(maxRepeatCount, repeatCount);
        return maxRepeatCount > 20; // 阈值，例如连续20个相同字符
    }
    
    /**
     * 创建HTTP隧道告警事件
     */
    private AlarmEvent createAlertEvent(HttpEvent httpEvent) {
        AlarmEvent alert = new AlarmEvent();
        
        // 设置基本属性
        alert.setId(UUID.randomUUID().toString());
        alert.setType(HTTP_TUNNEL_ALERT_TYPE);
        alert.setName(HTTP_TUNNEL_ALERT_NAME);
        alert.setSeverity(HTTP_TUNNEL_ALERT_LEVEL);
        alert.setDescription("检测到可能的HTTP隧道通信行为");
        alert.setSolution("1. 检查源IP和目标IP之间的HTTP通信\n2. 分析HTTP请求和响应内容是否存在数据泄露\n3. 检查是否存在未授权的通信通道");
        
        // 设置源IP和端口
        alert.setSourceIp(httpEvent.getSourceIp());
        alert.setSourcePort(httpEvent.getSourcePort());
        alert.setDestinationIp(httpEvent.getDestinationIp());
        alert.setDestinationPort(httpEvent.getDestinationPort());
        
        // 设置时间戳
        alert.setTimestamp(System.currentTimeMillis());
        
        // 设置详细信息
        Map<String, Object> details = new HashMap<>();
        details.put("method", httpEvent.getMethod());
        details.put("uri", httpEvent.getUri());
        details.put("userAgent", httpEvent.getUserAgent());
        details.put("host", httpEvent.getHost());
        details.put("requestHeaders", httpEvent.getHeaders());
        details.put("requestBody", httpEvent.getRequestBody());
        alert.setDetails(details);
        
        return alert;
    }
    
    /**
     * 创建HTTP隧道检测数据流
     */
    public static DataStream<AlarmEvent> detectHttpTunnel(DataStream<HttpEvent> httpEvents) {
        return httpEvents
                .flatMap(new HttpTunnelDetector())
                .name("HTTP隧道检测")
                .setParallelism(2); // 示例并行度
    }
}
