package com.geeksec.threatdetector.operator;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

import static com.geeksec.threatdetector.common.util.EsUtils.addLabelToSession;

/**
 * Converts a {@link org.apache.flink.types.Row} containing session label data into a {@link com.alibaba.fastjson.JSONObject}.
 * This Flink {@link org.apache.flink.api.common.functions.RichFlatMapFunction} is used to transform structured row-based session data
 * into a JSON object format, typically for further processing or output.
 *
 * <AUTHOR>
 */
@Slf4j
public class LabeledSessionToJsonConverter extends RichFlatMapFunction<Row, JSONObject> {
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
    }

    @Override
    public void flatMap(Row SessionLabelRow, Collector<JSONObject> collector) throws Exception {
        JSONObject sendJson = addLabelToSession(
            SessionLabelRow.getFieldAs(1),
            SessionLabelRow.getFieldAs(2),
            SessionLabelRow.getFieldAs(3)
        );
        collector.collect(sendJson);
    }
}
