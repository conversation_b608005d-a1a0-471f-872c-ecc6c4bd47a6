package com.geeksec.threatdetector.util;

import org.graylog2.syslog4j.Syslog;
import org.graylog2.syslog4j.SyslogIF;
import org.graylog2.syslog4j.SyslogRuntimeException;

/**
 * Syslog工具类
 * 提供Syslog格式化和处理功能
 *
 * <AUTHOR>
 * @since 2023/6/6
 */
public final class SyslogUtils {

    private static final String SYSLOG_SERVER_HOST = "***************";
    private static final int SYSLOG_SERVER_PORT = 514;

    private SyslogUtils() {
        // 防止实例化
    }
    
    /**
     * 发送 Syslog 消息
     * @param msg 要发送的消息
     * @throws SyslogRuntimeException 如果发送消息时发生错误
     */
    public static void sendMessage(String msg) throws SyslogRuntimeException {
        // 获取 Syslog 实例
        SyslogIF syslog = Syslog.getInstance("udp");
        
        // 配置 Syslog 服务器
        syslog.getConfig().setHost(SYSLOG_SERVER_HOST);
        syslog.getConfig().setPort(SYSLOG_SERVER_PORT);
        syslog.getConfig().setFacility(SyslogIF.FACILITY_USER);
        syslog.getConfig().setLocalName("gk_alarm");
        syslog.getConfig().setSendLocalTimestamp(false);
        
        // 发送消息
        syslog.warn(msg);
        
        // 关闭 Syslog 连接
        syslog.shutdown();
    }
}
