package com.geeksec.threatdetector.job;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.threatdetector.model.connection.PacketInfo;
import org.apache.flink.api.common.JobExecutionResult;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.common.typeinfo.BasicTypeInfo;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumer;
import org.apache.flink.util.Collector;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * 威胁检测流水线主类
 */
public class ThreatDetectionPipeline {

    // 协议类型常量
    private static final String PROTOCOL_HTTP = "http";
    private static final String PROTOCOL_DNS = "dns";
    private static final String PROTOCOL_SSL = "ssl";

    // 配置常量
    private static final String CHECKPOINT_DIR = "hdfs://localhost:9000/flink/checkpoints";
    private static final String KAFKA_BOOTSTRAP_SERVERS = "localhost:9092";
    private static final String KAFKA_GROUP_ID = "threat-detector-group";
    private static final String KAFKA_TOPIC = "threat-detection-events";

    // 并行度常量
    public static final int PARALLELISM_1 = 1;
    public static final int PARALLELISM_2 = 2;
    public static final int PARALLELISM_4 = 4;
    public static final int PARALLELISM_8 = 8;
    public static final int PARALLELISM_16 = 16;

    // 状态描述符
    private static final MapStateDescriptor<String, Object> CONFIG_DESCRIPTOR = new MapStateDescriptor<>("config",
            BasicTypeInfo.STRING_TYPE_INFO, TypeInformation.of(Object.class));

    // 告警日志属性
    private static final Map<String, Object> ALERT_LOG_PROPERTIES = new HashMap<>();

    // 日志记录器
    private static final Logger log = LoggerFactory.getLogger(ThreatDetectionPipeline.class);

    // 初始化静态块
    static {
        ALERT_LOG_PROPERTIES.put("log.level", "INFO");
        ALERT_LOG_PROPERTIES.put("log.path", "/var/log/threat-detector");
        ALERT_LOG_PROPERTIES.put("log.max.size", "100MB");
        ALERT_LOG_PROPERTIES.put("log.max.backup", 10);
    }

    public static void main(String[] args) throws Exception {
        // 创建执行环境
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();

        try {
            // 配置执行环境
            configureEnvironment(env);

            // 构建并执行任务
            buildAndExecuteJob(env);

            // 执行任务
            env.execute("Threat Detection Pipeline");
        } catch (Exception e) {
            log.error("Error executing pipeline", e);
            throw e;
        }
    }

    /**
     * 配置执行环境
     * 
     * @param env Flink执行环境
     */
    private static void configureEnvironment(StreamExecutionEnvironment env) {
        // 配置检查点
        env.enableCheckpointing(10000, CheckpointingMode.EXACTLY_ONCE);
        env.getCheckpointConfig().setCheckpointTimeout(60000);
        env.getCheckpointConfig().setMinPauseBetweenCheckpoints(500);
        env.getCheckpointConfig().setTolerableCheckpointFailureNumber(3);
        env.getCheckpointConfig().setMaxConcurrentCheckpoints(1);

        // 配置重启策略
        env.setRestartStrategy(
                RestartStrategies.fixedDelayRestart(
                        3, // 最大重试次数
                        Time.seconds(30) // 重试间隔
                ));

        // 设置默认并行度
        env.setParallelism(4);
    }

    private static JobExecutionResult buildAndExecuteJob(StreamExecutionEnvironment env) throws Exception {
        // 1. 创建Kafka消费者配置
        Properties kafkaProps = new Properties();
        kafkaProps.setProperty(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, KAFKA_BOOTSTRAP_SERVERS);
        kafkaProps.setProperty(ConsumerConfig.GROUP_ID_CONFIG, KAFKA_GROUP_ID);
        kafkaProps.setProperty(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest");

        // 2. 创建Kafka数据源
        FlinkKafkaConsumer<String> consumer = new FlinkKafkaConsumer<>(
                KAFKA_TOPIC,
                new SimpleStringSchema(),
                kafkaProps);

        // 3. 添加数据源到执行环境
        DataStream<String> inputStream = env.addSource(consumer).name("Kafka Source");

        // 4. 解析JSON数据
        DataStream<Map<String, Object>> parsedStream = inputStream
                .map(json -> {
                    try {
                        return new ObjectMapper().readValue(json, new TypeReference<Map<String, Object>>() {
                        });
                    } catch (Exception e) {
                        log.error("Failed to parse JSON: " + json, e);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .name("Parse JSON");

        // 5. 处理协议数据
        SingleOutputStreamOperator<Map<String, Object>> processedStream = parsedStream
                .flatMap(new FlatMapFunction<Map<String, Object>, Map<String, Object>>() {
                    @Override
                    public void flatMap(Map<String, Object> value, Collector<Map<String, Object>> out)
                            throws Exception {
                        try {
                            processProtocolData(value, ALERT_LOG_PROPERTIES, out);
                        } catch (Exception e) {
                            log.error("Error processing data: {}", value, e);
                        }
                    }
                })
                .name("Process Protocol Data");

        // 6. 输出结果到控制台
        processedStream.print().name("Print Results");

        // 7. 执行作业
        return env.execute("Threat Detection Pipeline");
    }

    /**
     * 处理协议数据
     * 
     * @param value         输入数据
     * @param alertLogProps 告警日志配置
     * @param out           输出收集器
     */
    private static void processProtocolData(
            Map<String, Object> value,
            Map<String, Object> alertLogProps,
            Collector<Map<String, Object>> out) {

        try {
            // 1. 获取协议类型
            String protocol = (String) value.get("protocol");
            if (protocol == null || protocol.isEmpty()) {
                log.warn("Protocol type is missing in data: {}", value);
                return;
            }

            // 2. 根据协议类型处理数据
            switch (protocol.toLowerCase()) {
                case PROTOCOL_HTTP:
                    processHttpProtocol(value, out);
                    break;
                case PROTOCOL_DNS:
                    processDnsProtocol(value, out);
                    break;
                case PROTOCOL_SSL:
                    processSslProtocol(value, out);
                    break;
                default:
                    log.warn("Unsupported protocol type: {}", protocol);
                    out.collect(value);
            }
        } catch (Exception e) {
            log.error("Error processing protocol data: {}", value, e);
        }
    }

    /**
     * 处理HTTP协议数据
     * 
     * @param value 输入数据
     * @param out   输出收集器
     */
    private static void processHttpProtocol(Map<String, Object> value, Collector<Map<String, Object>> out) {
        try {
            Map<String, Object> httpData = new HashMap<>();
            httpData.put("protocol", "http");
            httpData.put("timestamp", value.get("timestamp"));
            httpData.put("src_ip", value.get("src_ip"));
            httpData.put("src_port", value.get("src_port"));
            httpData.put("dst_ip", value.get("dst_ip"));
            httpData.put("dst_port", value.get("dst_port"));
            httpData.put("method", value.get("method"));
            httpData.put("host", value.get("host"));
            httpData.put("uri", value.get("uri"));
            httpData.put("user_agent", value.get("user_agent"));
            httpData.put("status", value.get("status"));

            out.collect(httpData);
        } catch (Exception e) {
            log.error("Error processing HTTP protocol data: {}", value, e);
        }
    }

    /**
     * 处理DNS协议数据
     * 
     * @param value 输入数据
     * @param out   输出收集器
     */
    private static void processDnsProtocol(Map<String, Object> value, Collector<Map<String, Object>> out) {
        try {
            Map<String, Object> dnsData = new HashMap<>();
            dnsData.put("protocol", "dns");
            dnsData.put("timestamp", value.get("timestamp"));
            dnsData.put("src_ip", value.get("src_ip"));
            dnsData.put("src_port", value.get("src_port"));
            dnsData.put("dst_ip", value.get("dst_ip"));
            dnsData.put("dst_port", value.get("dst_port"));
            dnsData.put("query", value.get("query"));
            dnsData.put("answer", value.get("answer"));
            dnsData.put("qtype", value.get("qtype"));
            dnsData.put("rcode", value.get("rcode"));

            out.collect(dnsData);
        } catch (Exception e) {
            log.error("Error processing DNS protocol data: {}", value, e);
        }
    }

    /**
     * 处理SSL协议数据
     * 
     * @param value 输入数据
     * @param out   输出收集器
     */
    private static void processSslProtocol(Map<String, Object> value, Collector<Map<String, Object>> out) {
        try {
            Map<String, Object> sslData = new HashMap<>();
            sslData.put("protocol", "ssl");
            sslData.put("timestamp", value.get("timestamp"));
            sslData.put("src_ip", value.get("src_ip"));
            sslData.put("src_port", value.get("src_port"));
            sslData.put("dst_ip", value.get("dst_ip"));
            sslData.put("dst_port", value.get("dst_port"));
            sslData.put("server_name", value.get("server_name"));
            sslData.put("version", value.get("version"));
            sslData.put("cipher", value.get("cipher"));
            sslData.put("subject", value.get("subject"));
            sslData.put("issuer", value.get("issuer"));

            out.collect(sslData);
        } catch (Exception e) {
            log.error("Error processing SSL protocol data: {}", value, e);
        }
    }

    /**
     * 处理连接信息
     * 
     * @param value 输入数据
     * @param out   输出收集器
     */
    private static void processConnectInfo(Map<String, Object> value, Collector<Map<String, Object>> out) {
        try {
            // 创建连接信息结果
            Map<String, Object> connectInfo = new HashMap<>();
            connectInfo.put("protocol", "connect");
            connectInfo.put("timestamp", value.get("timestamp"));
            connectInfo.put("src_ip", value.get("src_ip"));
            connectInfo.put("src_port", value.get("src_port"));
            connectInfo.put("dst_ip", value.get("dst_ip"));
            connectInfo.put("dst_port", value.get("dst_port"));

            // 添加连接状态信息
            if (value.get("status") != null) {
                connectInfo.put("status", value.get("status"));
            }

            // 添加其他连接相关的元数据
            if (value.get("bytes_sent") != null) {
                connectInfo.put("bytes_sent", value.get("bytes_sent"));
            }
            if (value.get("bytes_received") != null) {
                connectInfo.put("bytes_received", value.get("bytes_received"));
            }
            if (value.get("duration") != null) {
                connectInfo.put("duration", value.get("duration"));
            }

            out.collect(connectInfo);
        } catch (Exception e) {
            log.error("Error processing connect info: {}", value, e);
        }
    }

    /**
     * 获取数据包信息列表
     * 
     * @param pktInfos 数据包信息映射列表
     * @return 数据包信息对象列表
     */
    private static List<PacketInfo> getPktInfoList(List<Map<String, Object>> pktInfos) {
        List<PacketInfo> pktInfoList = new ArrayList<>();
        if (pktInfos == null || pktInfos.isEmpty()) {
            return pktInfoList;
        }

        for (int i = 0; i < pktInfos.size(); i++) {
            PacketInfo packetInfo = new PacketInfo();
            Map<String, Object> stringObjectMap = pktInfos.get(i);
            packetInfo.setDirection((Integer) stringObjectMap.get("direction"));
            packetInfo.setSingleTime((Integer) stringObjectMap.get("singleTime"));
            packetInfo.setNSingleTime((Integer) stringObjectMap.get("nSingleTime"));
            packetInfo.setByteNum((Integer) stringObjectMap.get("byteNum"));
            packetInfo.setCount((Integer) stringObjectMap.get("count"));
            pktInfoList.add(packetInfo);
        }
        return pktInfoList;
    }
}
