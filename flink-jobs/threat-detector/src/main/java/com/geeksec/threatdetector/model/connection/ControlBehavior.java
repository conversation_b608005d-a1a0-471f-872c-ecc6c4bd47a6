package com.geeksec.threatdetector.model.connection;

import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 远程控制行为特征。
 *
 * @param serverPackets 服务端发包序列
 * @param clientPackets 客户端（被控端）收包序列 (通常与服务端发包对应)
 * <AUTHOR>
 */
@Slf4j
public record ControlBehavior(List<PacketInfo> serverPackets, List<PacketInfo> clientPackets) {

    /**
     * 紧凑构造函数，确保列表的不可变性。
     */
    public ControlBehavior {
        this.serverPackets = (serverPackets == null) ? List.of() : List.copyOf(serverPackets);
        this.clientPackets = (clientPackets == null) ? List.of() : List.copyOf(clientPackets);
    }
}
