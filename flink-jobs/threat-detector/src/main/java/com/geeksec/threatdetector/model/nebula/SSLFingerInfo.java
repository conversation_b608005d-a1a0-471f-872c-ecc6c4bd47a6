package com.geeksec.threatdetector.model.nebula;

import lombok.Data;

/**
 * <AUTHOR>
 * @Description：SSL指纹信息
 */
@Data
public class SSLFingerInfo {

    /**
     * 指纹ID
     */
    private String fingerId;

    /**
     * Ja3指纹Hash
     */
    private String ja3Hash;

    /**
     * 指纹说明
     */
    private String desc;

    /**
     * 客户端/服务端
     */
    private String type;

    /**
     * 源IP
     */
    private String sIp;

    /**
     * 目的IP
     */
    private String dIp;

    /**
     * 服务器域名
     */
    private String server_domain;

    /**
     * 所属SessionId
     */
    private String sessionId;
    
    /**
     * ES键
     */
    private String esKey;


    
    // Explicit getters for compatibility with Lombok
    public String getFingerId() {
        return fingerId;
    }
    
    public String getJa3Hash() {
        return ja3Hash;
    }
    
    public String getDesc() {
        return desc;
    }
    
    public String getType() {
        return type;
    }
    
    public String getSIp() {
        return sIp;
    }
    
    public String getDIp() {
        return dIp;
    }
    
    public String getServer_domain() {
        return server_domain;
    }
    
    public String getSessionId() {
        return sessionId;
    }
    
    public String getEsKey() {
        return esKey;
    }
    

}
