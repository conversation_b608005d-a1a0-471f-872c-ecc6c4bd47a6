package com.geeksec.threatdetector.io.serializer;

import org.apache.flink.api.common.serialization.SerializationSchema;

import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * 告警日志键序列化器
 * <AUTHOR>
 * @Date 2024/11/27
 */
public class AlarmLogKeySerializationSchema implements SerializationSchema<Map<String, Object>> {
    private static final long serialVersionUID = 1L;

    @Override
    public byte[] serialize(Map<String, Object> alarmLog) {
        String time = alarmLog.get("time").toString();
        return time.getBytes(StandardCharsets.UTF_8);
    }
}
