package com.geeksec.threatdetector.operator.alarm;

import com.geeksec.threatdetector.function.alarm.EncryptedToolAlarmGenerator;
import com.geeksec.threatdetector.function.alarm.TunnelAlarmGenerator;
import com.geeksec.threatdetector.function.alarm.WebShellAlarmGenerator;
import com.geeksec.threatdetector.model.event.alarm.AlarmEvent;
import com.geeksec.threatdetector.model.event.protocol.HttpEvent;
import com.geeksec.threatdetector.model.event.protocol.ProtocolEvent;
import com.geeksec.threatdetector.model.event.protocol.SslEvent;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;

/**
 * 告警管理器
 * 用于统一管理各种告警生成器
 *
 * <AUTHOR>
 */
@Slf4j
public class AlarmStreamFactory {

        /**
         * 创建WebShell告警数据流
         *
         * @param httpEvents HTTP事件数据流
         * @return 告警事件数据流
         */
        public static DataStream<AlarmEvent> createWebShellAlarms(DataStream<HttpEvent> httpEvents) {
                return WebShellAlarmGenerator.generateWebShellAlarms(httpEvents);
        }

        /**
         * 创建隧道检测告警数据流
         *
         * @param events 基础事件数据流
         * @return 告警事件数据流
         */
        public static DataStream<AlarmEvent> createTunnelAlarms(DataStream<ProtocolEvent> events) {
                // 创建各种类型的隧道告警
                DataStream<AlarmEvent> httpTunnelAlarms = TunnelAlarmGenerator.generateTunnelAlarms(
                                events, TunnelAlarmGenerator.TunnelType.HTTP);

                DataStream<AlarmEvent> sslTunnelAlarms = TunnelAlarmGenerator.generateTunnelAlarms(
                                events, TunnelAlarmGenerator.TunnelType.SSL);

                DataStream<AlarmEvent> tcpTunnelAlarms = TunnelAlarmGenerator.generateTunnelAlarms(
                                events, TunnelAlarmGenerator.TunnelType.TCP);

                DataStream<AlarmEvent> dnsTunnelAlarms = TunnelAlarmGenerator.generateTunnelAlarms(
                                events, TunnelAlarmGenerator.TunnelType.DNS);

                DataStream<AlarmEvent> ntpTunnelAlarms = TunnelAlarmGenerator.generateTunnelAlarms(
                                events, TunnelAlarmGenerator.TunnelType.NTP);

                // 合并所有隧道告警
                return httpTunnelAlarms
                                .union(sslTunnelAlarms)
                                .union(tcpTunnelAlarms)
                                .union(dnsTunnelAlarms)
                                .union(ntpTunnelAlarms);
        }

        /**
         * 创建加密工具检测告警数据流
         *
         * @param sslEvents SSL事件数据流
         * @return 告警事件数据流
         */
        public static DataStream<AlarmEvent> createEncryptedToolAlarms(DataStream<SslEvent> sslEvents) {
            // 创建各种类型的加密工具告警
            DataStream<AlarmEvent> metasploitAlarms = EncryptedToolAlarmGenerator.generateEncryptedToolAlarms(
                sslEvents,
                EncryptedToolAlarmGenerator.EncryptedToolType.METASPLOIT,
                "Metasploit框架");

            DataStream<AlarmEvent> cobaltStrikeAlarms = EncryptedToolAlarmGenerator.generateEncryptedToolAlarms(
                sslEvents,
                EncryptedToolAlarmGenerator.EncryptedToolType.COBALT_STRIKE,
                "CobaltStrike框架");

            DataStream<AlarmEvent> ratAlarms = EncryptedToolAlarmGenerator.generateEncryptedToolAlarms(
                sslEvents,
                EncryptedToolAlarmGenerator.EncryptedToolType.RAT,
                "远程访问木马");

            DataStream<AlarmEvent> c2FrameworkAlarms = EncryptedToolAlarmGenerator.generateEncryptedToolAlarms(
                sslEvents,
                EncryptedToolAlarmGenerator.EncryptedToolType.C2_FRAMEWORK,
                "命令与控制框架");

            DataStream<AlarmEvent> sslAttackToolAlarms = EncryptedToolAlarmGenerator.generateEncryptedToolAlarms(
                sslEvents,
                EncryptedToolAlarmGenerator.EncryptedToolType.SSL_ATTACK_TOOL,
                "SSL攻击工具");

            // 合并所有加密工具检测告警
            return metasploitAlarms
                .union(cobaltStrikeAlarms)
                .union(ratAlarms)
                .union(c2FrameworkAlarms)
                .union(sslAttackToolAlarms);
        }

        /**
         * 创建所有类型的告警数据流
         *
         * @param httpEvents HTTP事件数据流
         * @param sslEvents  SSL事件数据流
         * @param baseEvents 基础事件数据流
         * @return 告警事件数据流
         */
        public static DataStream<AlarmEvent> createAllAlarms(
                        DataStream<HttpEvent> httpEvents,
                        DataStream<SslEvent> sslEvents,
                        DataStream<ProtocolEvent> baseEvents) {

                // 创建WebShell告警
                DataStream<AlarmEvent> webShellAlarms = createWebShellAlarms(httpEvents);

                // 创建隧道检测告警
                DataStream<AlarmEvent> tunnelAlarms = createTunnelAlarms(baseEvents);

                // 创建加密工具检测告警
                DataStream<AlarmEvent> encryptedToolAlarms = createEncryptedToolAlarms(sslEvents);

                // 合并所有告警
                return webShellAlarms
                                .union(tunnelAlarms)
                                .union(encryptedToolAlarms);
        }

        /**
         * 对告警事件进行去重处理
         *
         * @param alertEvents 告警事件数据流
         * @return 去重后的告警事件数据流
         */
        public static SingleOutputStreamOperator<AlarmEvent> deduplicateAlerts(DataStream<AlarmEvent> alertEvents) {
                // 实现告警去重逻辑
                // 可以基于时间窗口、会话ID等进行去重
                // 这里简单实现，后续可以根据需求扩展
                return alertEvents
                                .keyBy(alert -> alert.getSourceIp() + "_" + alert.getDestinationIp() + "_"
                                                + alert.getType())
                                .filter(new AlarmDeduplicationFilter())
                                .name("告警去重处理");
        }
}
