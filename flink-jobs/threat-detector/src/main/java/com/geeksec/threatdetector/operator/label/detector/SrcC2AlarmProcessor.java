package com.geeksec.threatdetector.operator.label.detector;

import com.geeksec.threatdetector.model.connection.ConnectBasicInfo;
import com.geeksec.threatdetector.model.protocol.SRCPInfo;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static com.geeksec.threatdetector.common.util.AlarmUtils.getLabelEdgeRow;

/**
 * 标准远程控制协议C2行为告警处理器
 * 负责检测和处理标准远程控制协议中的C2(Command and Control)行为攻击
 *
 * <AUTHOR>
 */
public class SrcC2AlarmProcessor extends RichFlatMapFunction<Row, Row> {
    private static final Logger logger = LoggerFactory.getLogger(SrcC2AlarmProcessor.class);
    private static transient JedisPool jedisPool = null;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        // redis池 初始化.
        // Assuming Redis is running on localhost:6379
        jedisPool = new JedisPool("localhost", 6379);
        // logger.info("生成jedisPool成功! {}",
        // jedisPool.getNumIdle(),jedisPool.hashCode());
        // 初始化网络连接基线的建立状态
        Jedis jedis = null;
        try {
            jedis = jedisPool.getResource();
            jedis.select(6);
            jedis.setex("SRCPBaselineBuild", 86400 * 7, "1");
            // 设置建立历史记录，值为时间戳
            jedis.sadd("SRCPBaselineBuildHistory", String.valueOf(System.currentTimeMillis() / 1000));
        } catch (Exception e) {
            logger.error("标准远程控制协议下的C2行为攻击告警信息读取redis失败，error:——{}——", e.toString());
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
    }

    @Override
    public void close() throws Exception {
        super.close();
        if (!jedisPool.isClosed()) {
            jedisPool.close();
        }
    }

    @Override
    public void flatMap(Row row, Collector<Row> collector) throws Exception {

        SRCPInfo srcpInfo = row.getFieldAs(1);
        ConnectBasicInfo connectBasicInfo = srcpInfo.getConnectBasicInfo();
        if (connectBasicInfo == null) {
            logger.warn("ConnectBasicInfo is null for SRCPInfo: {}", srcpInfo);
            return; // Or handle appropriately
        }

        // connectBasicInfo.handleControl(); // Method not found in ConnectBasicInfo

        String sIp = connectBasicInfo.getSrcIp();
        String dIp = connectBasicInfo.getDstIp();
        int dPort = Integer.parseInt(connectBasicInfo.getDstPort());
        int sPort = Integer.parseInt(connectBasicInfo.getSrcPort());

        String SessionId = connectBasicInfo.getSessionId();
        String rcpType = srcpInfo.getRCPType();
        // 标准远控工具IP打标
        Row rcpToolIp = getLabelEdgeRow(srcpInfo.getConnectBasicInfo().getDIp(), rcpType);
        collector.collect(rcpToolIp);

        Jedis jedis = null;
        try {
            jedis = jedisPool.getResource();
            jedis.select(6);
            if (jedis.exists("SRCPBaselineBuild") && jedis.smembers("SRCPBaselineBuildHistory").size() <= 1) {
                // 建立客户端基线
                jedis.sadd("C_" + sIp, dIp + "_" + dPort);
                // 建立服务端基线
                jedis.sadd("S_" + dIp, sIp + "_" + sPort);
                return;
            }

            // 不存在控制行为
            if (!connectBasicInfo.isControl()) {
                return;
            }

            // 基线建立完成后，且该会话有控制行为的可疑性
            if (jedis.exists("C_" + sIp) && jedis.exists("S_" + dIp)) {
                Set<String> dIpdPorts = jedis.smembers(sIp);
                Set<String> sIpsPorts = jedis.smembers(dIp);
                if (!dIpdPorts.contains(dIp + "_" + dPort) && !sIpsPorts.contains(sIp + "_" + sPort)) {
                    logger.info("标准远程控制协议下的C2行为告警检测{} {}", sIp, dIp + "_" + dPort);
                    List<String> labelList = connectBasicInfo.getAnalysisLabelList();
                    labelList.add(SRCPInfo.SRCP_C2_TAG);
                    connectBasicInfo.setAnalysisLabelList(labelList);
                    srcpInfo.setConnectBasicInfo(connectBasicInfo);
                    srcpInfo.setInBaseline(true);
                    srcpInfo.setDIpdPorts(dIpdPorts);
                    srcpInfo.setSIpdPorts(sIpsPorts);
                    collectInfo(collector, srcpInfo, SessionId, rcpType);
                }
            } else {
                logger.info("标准远程控制协议下的C2行为基线中未出现的{} {}", sIp, dIp + "_" + dPort);
                List<String> labelList = connectBasicInfo.getAnalysisLabelList();
                labelList.add(SRCPInfo.SRCP_C2_TAG);
                connectBasicInfo.setAnalysisLabelList(labelList);
                srcpInfo.setConnectBasicInfo(connectBasicInfo);
                srcpInfo.setInBaseline(false);
                collectInfo(collector, srcpInfo, SessionId, rcpType);
            }

        } catch (Exception e) {
            logger.error("标准远程控制协议下的C2行为攻击告警信息读取redis失败，error:——{}——", e.toString());
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
    }

    private void collectInfo(Collector<Row> collector, SRCPInfo srcpInfo, String sessionId, String rcpType) {
        Row alarmRow = new Row(3);
        alarmRow.setField(0, "标准远程控制协议下的C2行为");
        // 标准远程控制协议下的C2行为基类
        alarmRow.setField(1, srcpInfo);
        collector.collect(alarmRow);

        Row SessionLabelRow = new Row(4);
        SessionLabelRow.setField(0, "会话打标");
        SessionLabelRow.setField(1, sessionId);
        Set<String> labels = new HashSet<>();
        labels.add(SRCPInfo.SRCP_C2_TAG);
        // 给会话打心跳，控制，激活的标签
        if (srcpInfo.getConnectBasicInfo().isControl()) {
            labels.add(ConnectBasicInfo.controlTag);
        }
        SessionLabelRow.setField(2, labels);
        SessionLabelRow.setField(3, srcpInfo.getConnectBasicInfo().getEsKey());
        collector.collect(SessionLabelRow);
        // addSessionLabel(collector, srcpInfo, sessionId);
    }

    private static void addSessionLabel(Collector<Row> collector, SRCPInfo srcpInfo, String SessionId) {
        ConnectBasicInfo connectBasicInfo = srcpInfo.getConnectBasicInfo();

        if (connectBasicInfo.isControl()) {
            Row SessionLabelRow = new Row(3);
            SessionLabelRow.setField(0, "会话打标");
            SessionLabelRow.setField(1, SessionId);
            SessionLabelRow.setField(2, ConnectBasicInfo.controlTag);
            collector.collect(SessionLabelRow);
        }
    }
}
