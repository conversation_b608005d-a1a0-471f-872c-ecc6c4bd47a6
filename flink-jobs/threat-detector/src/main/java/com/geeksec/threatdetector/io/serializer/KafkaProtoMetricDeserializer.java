package com.geeksec.threatdetector.io.serializer;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.SerializationException;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.connector.kafka.source.reader.deserializer.KafkaRecordDeserializationSchema;
import org.apache.flink.util.Collector;
import org.apache.kafka.clients.consumer.ConsumerRecord;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * Deserializes Kafka records containing JSON strings into a {@code Map<String, Object>}.
 * Assumes the value of the Kafka record is a UTF-8 encoded JSON string.
 *
 * <AUTHOR>
 */
@Slf4j
public class KafkaProtoMetricDeserializer implements KafkaRecordDeserializationSchema<Map<String, Object>> {


    @Override
    public TypeInformation<Map<String, Object>> getProducedType() {
        return TypeInformation.of(new TypeHint<Map<String, Object>>(){});
    }

    @Override
    public void deserialize(ConsumerRecord<byte[], byte[]> consumerRecord, Collector<Map<String, Object>> collector) throws IOException {
        byte[] values = consumerRecord.value();

        if (values == null || values.length == 0){
            log.info("Kafka record value is null or empty, skipping deserialization.");
        }else {
            try {
                String jsonString = new String(values, StandardCharsets.UTF_8);
                HashMap<String, Object> jsonMap = JSONObject.parseObject(jsonString, HashMap.class);
                collector.collect(jsonMap);
            } catch (Exception e) {
                throw new SerializationException("Error when serializing JSON byte[] " + e);
            }
        }
    }
}
