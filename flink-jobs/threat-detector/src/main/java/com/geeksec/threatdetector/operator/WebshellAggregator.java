package com.geeksec.threatdetector.operator;

import com.geeksec.threatdetector.infra.trigger.ConnectTrigger;
import com.geeksec.threatdetector.model.connection.ConnectBasicInfo;
import com.geeksec.threatdetector.model.protocol.HttpSimpleInfo;
import com.geeksec.threatdetector.model.protocol.SslSimpleInfo;
import com.geeksec.threatdetector.model.webshell.WebshellInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.functions.AggregateFunction;

import java.util.List;
import java.util.Map;

/**
 * Aggregates connection, SSL, and HTTP information to build a {@link WebshellInfo} object.
 * This aggregator is designed for Flink stream processing to detect webshell-related activities.
 *
 * <AUTHOR>
 * @Date 2024/9/9
 */
@Slf4j
public class WebshellAggregator implements AggregateFunction<Map<String, Object>, WebshellInfo, WebshellInfo> {


    @Override
    public WebshellInfo createAccumulator() {
        return new WebshellInfo();
    }

    @Override
    public WebshellInfo add(Map<String, Object> pbMap, WebshellInfo webshellInfo) {
        String type = (String) pbMap.get("type");
        if (ConnectTrigger.CONNECTION_END_TYPE.equals(type)){
            ConnectBasicInfo connectBasicInfo = new ConnectBasicInfo(pbMap);
            webshellInfo.setConnectBasicInfo(connectBasicInfo);
            return webshellInfo;
        }else if (ConnectTrigger.SSL_TYPE.equals(type)){
            SslSimpleInfo sslSimpleInfo = new SslSimpleInfo(pbMap);
            List<SslSimpleInfo> sslSimpleInfos = webshellInfo.getSslSimpleInfos();
            sslSimpleInfos.add(sslSimpleInfo);
            webshellInfo.setSslSimpleInfos(sslSimpleInfos);
            return webshellInfo;
        } else if (ConnectTrigger.HTTP_TYPE.equals(type)) {
            HttpSimpleInfo httpSimpleInfo = new HttpSimpleInfo(pbMap);
            List<HttpSimpleInfo> httpSimpleInfos = webshellInfo.getHttpSimpleInfos();
            httpSimpleInfos.add(httpSimpleInfo);
            webshellInfo.setHttpSimpleInfos(httpSimpleInfos);
            return webshellInfo;
        }else {
            log.error("WebshellAggr中混入了不属于 SSL, HTTP, CONNECT 的数据类型, 不做处理, 直接返回");
            return webshellInfo;
        }
    }

    @Override
    public WebshellInfo getResult(WebshellInfo webshellInfo) { return webshellInfo;}

    @Override
    public WebshellInfo merge(WebshellInfo webshellInfo1, WebshellInfo webshellInfo2) {

        WebshellInfo webshellInfo = new WebshellInfo();
        if (webshellInfo1.getConnectBasicInfo() != null){
            webshellInfo.setConnectBasicInfo(webshellInfo1.getConnectBasicInfo());
        }
        if (webshellInfo2.getConnectBasicInfo() != null){
            webshellInfo.setConnectBasicInfo(webshellInfo2.getConnectBasicInfo());
        }

        List<SslSimpleInfo> sslSimpleInfos1 = webshellInfo1.getSslSimpleInfos();
        List<SslSimpleInfo> sslSimpleInfos2 = webshellInfo2.getSslSimpleInfos();
        sslSimpleInfos1.addAll(sslSimpleInfos2);
        webshellInfo.setSslSimpleInfos(sslSimpleInfos1);

        List<HttpSimpleInfo> httpSimpleInfos1 = webshellInfo1.getHttpSimpleInfos();
        List<HttpSimpleInfo> httpSimpleInfos2 = webshellInfo2.getHttpSimpleInfos();
        httpSimpleInfos1.addAll(httpSimpleInfos2);
        webshellInfo.setHttpSimpleInfos(httpSimpleInfos1);
        return webshellInfo;
    }
}
