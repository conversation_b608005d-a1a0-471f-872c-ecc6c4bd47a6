package com.geeksec.threatdetector.io.serializer;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.SerializationException;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.connector.kafka.source.reader.deserializer.KafkaRecordDeserializationSchema;
import org.apache.flink.util.Collector;
import org.apache.kafka.clients.consumer.ConsumerRecord;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * 模型开关反序列化器
 * 用于反序列化Kafka中的模型开关配置消息
 *
 * <AUTHOR>
 * @since 2024/1/25
 */
@Slf4j
public class ModelToggleDeserializer implements KafkaRecordDeserializationSchema<Map<String, Object>> {

    @Override
    public void deserialize(ConsumerRecord<byte[], byte[]> consumerRecord, Collector<Map<String, Object>> collector)
            throws IOException {
        byte[] values = consumerRecord.value();
        byte[] key = consumerRecord.key();
        String keyString = new String(key, StandardCharsets.UTF_8);
        if (values == null) {
            log.info("modelToggle值为空");
        } else {
            try {
                JSONObject obj = JSONObject.parseObject(new String(values, StandardCharsets.UTF_8));
                if (keyString.equals("switch_change")) {
                    Map<String, Object> infoMap = obj.getInnerMap();
                    collector.collect(infoMap);
                } else {
                    log.info("消费到了告警数据，topic：{}", keyString);
                }
            } catch (Exception e) {
                throw new SerializationException("Error when serializing Customerto byte[] " + e);
            }
        }
    }

    @Override
    public TypeInformation<Map<String, Object>> getProducedType() {
        return TypeInformation.of(new TypeHint<Map<String, Object>>() {
        });
    }
}
