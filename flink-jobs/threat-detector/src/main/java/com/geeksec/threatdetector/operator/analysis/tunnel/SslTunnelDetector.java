package com.geeksec.threatdetector.operator.analysis.tunnel;

import com.geeksec.threatdetector.model.event.alarm.AlarmEvent;
import com.geeksec.threatdetector.model.event.protocol.SslEvent;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * SSL隧道检测函数
 *
 * <AUTHOR>
 */
/**
 * SSL隧道检测器
 * 用于检测基于SSL/TLS协议的隧道流量特征
 *
 * <AUTHOR>
 * @since 2023/01/01
 */
public class SslTunnelDetector extends RichFlatMapFunction<SslEvent, AlarmEvent> {
    
    private static final Logger logger = LoggerFactory.getLogger(SslTunnelDetector.class);
    private static final String SSL_TUNNEL_ALERT_TYPE = "SSL_TUNNEL_DETECTED";
    private static final String SSL_TUNNEL_ALERT_NAME = "SSL隧道检测";
    private static final String SSL_TUNNEL_ALERT_LEVEL = "HIGH";
    
    // 可疑的SSL版本
    private static final String[] SUSPICIOUS_SSL_VERSIONS = {"SSLv2", "SSLv3"};
    
    // 可疑的加密套件
    private static final String[] SUSPICIOUS_CIPHER_SUITES = {
        "NULL", "EXPORT", "DES", "RC4", "MD5", "anon"
    };
    
    @Override
    public void flatMap(SslEvent sslEvent, Collector<AlarmEvent> out) throws Exception {
        try {
            // 检测SSL隧道特征
            if (isSslTunnel(sslEvent)) {
                // 创建告警事件
                AlarmEvent alert = createAlertEvent(sslEvent);
                out.collect(alert);
                
                logger.warn("检测到SSL隧道: {}:{} -> {}:{}", 
                        sslEvent.getSourceIp(), sslEvent.getSourcePort(),
                        sslEvent.getDestinationIp(), sslEvent.getDestinationPort());
            }
        } catch (Exception e) {
            logger.error("SSL隧道检测错误: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 检测SSL连接是否包含隧道特征
     */
    private boolean isSslTunnel(SslEvent sslEvent) {
        // 1. 检查SSL版本是否可疑
        String sslVersion = sslEvent.getSslVersion();
        if (sslVersion != null) {
            for (String suspiciousVersion : SUSPICIOUS_SSL_VERSIONS) {
                if (sslVersion.contains(suspiciousVersion)) {
                    return true;
                }
            }
        }
        
        // 2. 检查加密套件是否可疑
        String cipherSuite = sslEvent.getCipherSuite();
        if (cipherSuite != null) {
            for (String suspiciousCipher : SUSPICIOUS_CIPHER_SUITES) {
                if (cipherSuite.contains(suspiciousCipher)) {
                    return true;
                }
            }
        }
        
        // 3. 检查证书是否可疑
        Map<String, String> certificate = sslEvent.getCertificate();
        if (certificate != null) {
            // 检查自签名证书
            String issuer = certificate.get("issuer");
            String subject = certificate.get("subject");
            if (issuer != null && subject != null && issuer.equals(subject)) {
                return true;
            }
            
            // 检查证书有效期是否异常短
            String validFrom = certificate.get("validFrom");
            String validTo = certificate.get("validTo");
            if (validFrom != null && validTo != null) {
                try {
                    long fromTime = Long.parseLong(validFrom);
                    long toTime = Long.parseLong(validTo);
                    long validityPeriod = toTime - fromTime;
                    // 有效期小于7天的证书可能是可疑的
                    if (validityPeriod < 7 * 24 * 60 * 60 * 1000) {
                        return true;
                    }
                } catch (NumberFormatException e) {
                    logger.error("解析证书有效期失败: {}", e.getMessage());
                }
            }
            
            // 检查证书主题是否可疑
            if (subject != null) {
                // 检查通用名称是否为IP地址
                if (subject.matches(".*CN=\\d+\\.\\d+\\.\\d+\\.\\d+.*")) {
                    return true;
                }
                
                // 检查通用名称是否为明显的随机字符串
                if (subject.matches(".*CN=[a-zA-Z0-9]{10,}.*") && !subject.toLowerCase().contains("com") && !subject.toLowerCase().contains("org")) {
                    return true;
                }
            }
        }
        
        // 4. 检查客户端Hello消息是否异常
        String clientHello = sslEvent.getClientHello();
        if (clientHello != null) {
            // 检查是否包含异常的扩展
            // 这里简化处理，实际应用中需要更复杂的逻辑
        }
        
        // 5. 检查服务器Hello消息是否异常
        String serverHello = sslEvent.getServerHello();
        if (serverHello != null) {
            // 检查是否包含异常的扩展
            // 这里简化处理，实际应用中需要更复杂的逻辑
        }
        
        // 6. 检查通信模式是否异常
        // 例如：心跳包频率、数据包大小分布等
        // 这部分需要状态管理，这里简化处理
        
        return false;
    }
    
    /**
     * 创建SSL隧道告警事件
     */
    private AlarmEvent createAlertEvent(SslEvent sslEvent) {
        AlarmEvent alert = new AlarmEvent();
        
        // 设置基本属性
        alert.setId(UUID.randomUUID().toString());
        alert.setType(SSL_TUNNEL_ALERT_TYPE);
        alert.setName(SSL_TUNNEL_ALERT_NAME);
        alert.setSeverity(SSL_TUNNEL_ALERT_LEVEL);
        alert.setDescription("检测到可能的SSL隧道通信行为");
        alert.setSolution("1. 检查源IP和目标IP之间的SSL通信\n2. 分析SSL证书和加密套件是否异常\n3. 检查是否存在未授权的通信通道");
        
        // 设置源IP和端口
        alert.setSourceIp(sslEvent.getSourceIp());
        alert.setSourcePort(sslEvent.getSourcePort());
        alert.setDestinationIp(sslEvent.getDestinationIp());
        alert.setDestinationPort(sslEvent.getDestinationPort());
        
        // 设置时间戳
        alert.setTimestamp(System.currentTimeMillis());
        
        // 设置详细信息
        Map<String, Object> details = new HashMap<>();
        details.put("sslVersion", sslEvent.getSslVersion());
        details.put("cipherSuite", sslEvent.getCipherSuite());
        details.put("certificate", sslEvent.getCertificate());
        details.put("serverName", sslEvent.getServerName());
        alert.setDetails(details);
        
        return alert;
    }
    
    /**
     * 创建SSL隧道检测数据流
     */
    public static DataStream<AlarmEvent> detectSslTunnel(DataStream<SslEvent> sslEvents) {
        return sslEvents
                .flatMap(new SslTunnelDetector())
                .name("SSL隧道检测")
                .setParallelism(2);
    }
}
