package com.geeksec.threatdetector.model.event.protocol;

import com.geeksec.threatdetector.model.event.BaseEvent;

/**
 * 协议基础事件模型
 * 所有协议事件的基类
 *
 * <AUTHOR>
 */
public class ProtocolEvent extends BaseEvent {
    
    /**
     * 会话ID
     */
    private String sessionId;
    
    /**
     * 源MAC地址
     */
    private String sourceMac;
    
    /**
     * 目标MAC地址
     */
    private String destinationMac;
    
    /**
     * 负载数据
     */
    private String payload;
    
    /**
     * 数据包长度
     */
    private Integer packetLength;

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getSourceMac() {
        return sourceMac;
    }

    public void setSourceMac(String sourceMac) {
        this.sourceMac = sourceMac;
    }

    public String getDestinationMac() {
        return destinationMac;
    }

    public void setDestinationMac(String destinationMac) {
        this.destinationMac = destinationMac;
    }

    public String getPayload() {
        return payload;
    }

    public void setPayload(String payload) {
        this.payload = payload;
    }

    public Integer getPacketLength() {
        return packetLength;
    }

    public void setPacketLength(Integer packetLength) {
        this.packetLength = packetLength;
    }

    @Override
    public String getEventType() {
        return "protocol";
    }
    
    @Override
    public String toString() {
        return "ProtocolEvent{" +
                "sessionId='" + sessionId + '\'' +
                ", protocol='" + getProtocol() + '\'' +
                ", sourceIp='" + getSourceIp() + '\'' +
                ", sourcePort=" + getSourcePort() +
                ", destinationIp='" + getDestinationIp() + '\'' +
                ", destinationPort=" + getDestinationPort() +
                ", timestamp=" + getTimestamp() +
                '}';
    }
}
