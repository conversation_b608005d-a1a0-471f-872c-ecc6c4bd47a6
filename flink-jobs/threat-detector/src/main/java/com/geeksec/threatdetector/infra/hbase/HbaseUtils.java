package com.geeksec.threatdetector.infra.hbase;

import org.apache.commons.pool.impl.GenericObjectPool;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.Admin;
import org.apache.hadoop.hbase.client.ColumnFamilyDescriptorBuilder;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.client.Table;
import org.apache.hadoop.hbase.util.Bytes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @description: HBase数据库操作工具类
 */
public class HbaseUtils {

    private static final Logger logger = LoggerFactory.getLogger(HbaseUtils.class);

    public static GenericObjectPool<Connection> initHbasePool() {
        GenericObjectPool.Config HbasePoolConfig = new GenericObjectPool.Config();
        HbasePoolConfig.maxActive = 20;
        HbasePoolConfig.maxIdle = 20;
        HbasePoolConfig.maxWait = 50;
        HbasePoolConfig.minIdle = 10;
        HbasePoolConfig.testOnBorrow = false;
        HbasePoolConfig.testOnReturn = false;
        HbasePoolConfig.whenExhaustedAction = 1;
        HbasePoolFactory hbasePoolFactory = new HbasePoolFactory();
        return new GenericObjectPool<>(hbasePoolFactory, HbasePoolConfig);
    }

    public static Connection getHbaseClient(GenericObjectPool<Connection> clientPool) throws Exception {
        return clientPool.borrowObject();
    }

    public static void returnHbaseClient(Connection client, GenericObjectPool<Connection> clientPool) throws Exception {
        clientPool.returnObject(client);
    }

    public static void main(String[] args) {
        // HBase配置
        GenericObjectPool<Connection> hbasePoolFactory = initHbasePool();
        Connection connection = null;
        // 连接HBase
        try {
            connection = getHbaseClient(hbasePoolFactory);
            Admin admin = connection.getAdmin();
            Table table = connection.getTable(TableName.valueOf("CERT"));
            // 检查列族是否存在
            if (admin.getDescriptor(TableName.valueOf("CERT")).getColumnFamily(Bytes.toBytes("Labels"))!=null) {
                // 如果列族不存在，则创建列族
                admin.addColumnFamily(TableName.valueOf("CERT"), ColumnFamilyDescriptorBuilder.newBuilder(Bytes.toBytes("Labels")).build());
            }

//            // 准备Put操作
//            Put put = new Put(Bytes.toBytes("SHA1=a"));
//            put.addColumn(Bytes.toBytes("Labels"), Bytes.toBytes("your_column"), Bytes.toBytes("[1,2,3]"));
//
//            // 执行Put操作
//            table.put(put);

            System.out.println("操作完成");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}


