package com.geeksec.threatdetector.util.constant;

/**
 * 应用程序常量
 */
public final class AppConstants {
    
    private AppConstants() {
        // 防止实例化
    }
    
    // Kafka相关常量
    public static final String KAFKA_BOOTSTRAP_SERVERS = "kafka.bootstrap.servers";
    public static final String KAFKA_GROUP_ID = "kafka.group.id";
    public static final String KAFKA_TOPIC_EVENTS = "kafka.topic.events";
    public static final String KAFKA_TOPIC_ALERTS = "kafka.topic.alerts";
    
    // Flink配置常量
    public static final String FLINK_CHECKPOINT_INTERVAL_MS = "flink.checkpoint.interval.ms";
    public static final String FLINK_PARALLELISM = "flink.parallelism";
    public static final String FLINK_RESTART_ATTEMPTS = "flink.restart.attempts";
    public static final String FLINK_RESTART_DELAY_MS = "flink.restart.delay.ms";
    
    // 作业配置
    public static final String JOB_NAME = "threat-detector";
    public static final String JOB_VERSION = "1.0.0";
    
    // 其他常量
    public static final String DEFAULT_ENCODING = "UTF-8";
    public static final int DEFAULT_HTTP_PORT = 80;
    public static final int DEFAULT_HTTPS_PORT = 443;
    
    // 时间相关常量（毫秒）
    public static final long ONE_SECOND = 1000L;
    public static final long ONE_MINUTE = 60 * ONE_SECOND;
    public static final long ONE_HOUR = 60 * ONE_MINUTE;
    public static final long ONE_DAY = 24 * ONE_HOUR;
}
