package com.geeksec.threatdetector.io.source.kafka;

import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.connector.kafka.source.reader.deserializer.KafkaRecordDeserializationSchema;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.OffsetResetStrategy;
import org.apache.kafka.common.serialization.ByteArrayDeserializer;
import org.apache.kafka.common.serialization.StringDeserializer;

import java.util.Arrays;
import java.util.Properties;
import java.util.regex.Pattern;

/**
 * Kafka源构建器 - 使用Flink 1.15+的新API
 */
public class KafkaSourceBuilder<T> {
    private final String topic;
    private boolean isPattern = false;
    private String bootstrapServers = "localhost:9092";
    private String groupId = "threat-detector-group";
    private String clientIdPrefix = "threat-detector-client";
    private DeserializationSchema<T> deserializationSchema;
    private Properties properties = new Properties();
    private OffsetsInitializer startingOffsets = OffsetsInitializer.earliest();
    private OffsetsInitializer offsetCommitMode = OffsetsInitializer.committedOffsets(OffsetResetStrategy.EARLIEST);

    public KafkaSourceBuilder(String topic) {
        this.topic = topic;
    }

    /**
     * 设置主题为模式匹配
     */
    public KafkaSourceBuilder<T> asPattern() {
        this.isPattern = true;
        return this;
    }

    public KafkaSourceBuilder<T> setBootstrapServers(String bootstrapServers) {
        this.bootstrapServers = bootstrapServers;
        return this;
    }

    public KafkaSourceBuilder<T> setGroupId(String groupId) {
        this.groupId = groupId;
        return this;
    }

    public KafkaSourceBuilder<T> setClientIdPrefix(String clientIdPrefix) {
        this.clientIdPrefix = clientIdPrefix;
        return this;
    }

    public KafkaSourceBuilder<T> setDeserializationSchema(DeserializationSchema<T> deserializationSchema) {
        this.deserializationSchema = deserializationSchema;
        return this;
    }

    public KafkaSourceBuilder<T> setProperties(Properties properties) {
        this.properties = properties;
        return this;
    }

    public KafkaSourceBuilder<T> addProperty(String key, String value) {
        this.properties.setProperty(key, value);
        return this;
    }

    /**
     * 设置起始偏移量
     */
    public KafkaSourceBuilder<T> setStartingOffsets(OffsetsInitializer startingOffsets) {
        this.startingOffsets = startingOffsets;
        return this;
    }

    /**
     * 设置偏移量提交模式
     */
    public KafkaSourceBuilder<T> setOffsetCommitMode(OffsetsInitializer offsetCommitMode) {
        this.offsetCommitMode = offsetCommitMode;
        return this;
    }

    /**
     * 构建KafkaSource
     */
    public KafkaSource<T> build() {
        if (deserializationSchema == null) {
            throw new IllegalStateException("deserializationSchema must be set");
        }

        // 设置Kafka属性
        Properties kafkaProps = new Properties();
        kafkaProps.setProperty(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        kafkaProps.setProperty(ConsumerConfig.GROUP_ID_CONFIG, groupId);
        kafkaProps.setProperty(ConsumerConfig.CLIENT_ID_CONFIG, clientIdPrefix + "-" + System.currentTimeMillis());
        kafkaProps.setProperty(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
        kafkaProps.setProperty(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, ByteArrayDeserializer.class.getName());
        
        // 合并自定义属性
        kafkaProps.putAll(properties);

        // 创建KafkaSource构建器
        org.apache.flink.connector.kafka.source.KafkaSourceBuilder<T> sourceBuilder = KafkaSource.<T>builder()
                .setBootstrapServers(bootstrapServers)
                .setDeserializer(KafkaRecordDeserializationSchema.valueOnly(deserializationSchema))
                .setProperties(kafkaProps)
                .setStartingOffsets(startingOffsets);

        // 设置主题或主题模式
        if (isPattern) {
            sourceBuilder.setTopicPattern(Pattern.compile(topic));
        } else {
            sourceBuilder.setTopics(Arrays.asList(topic.split(",")));
        }

        // 设置消费组ID
        if (groupId != null && !groupId.isEmpty()) {
            sourceBuilder.setGroupId(groupId);
        }

        // 设置客户端ID前缀
        if (clientIdPrefix != null && !clientIdPrefix.isEmpty()) {
            sourceBuilder.setClientIdPrefix(clientIdPrefix);
        }

        return sourceBuilder.build();
    }
}
