package com.geeksec.threatdetector.operator.label.http;

import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

import java.util.Map;

/**
 * HTTP事件行映射器
 * 负责将不同类型的HTTP相关事件（如Web登录、地理位置信息、隧道工具等）映射为行数据
 *
 * <AUTHOR>
 */
public class HttpEventRowMapper extends RichFlatMapFunction<Map<String, Object>, Row> {

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
    }

    @Override
    public void flatMap(Map<String, Object> Infomap, Collector<Row> collector) throws Exception {
        Row webLoginInfoRow = (Row) Infomap.get("Web_Login_Info");
        if (webLoginInfoRow!=null){
            collector.collect(webLoginInfoRow);
        }

        Row NeoregeoInfoRow = (Row) Infomap.get("Neoregeo_info");
        if (NeoregeoInfoRow!=null){
            collector.collect(NeoregeoInfoRow);
        }

        Row suo5InfoRow = (Row) Infomap.get("suo5_info");
        if (suo5InfoRow!=null){
            collector.collect(suo5InfoRow);
        }

        Row BeHinder_infoRow = (Row) Infomap.get("BeHinder_info");
        if (BeHinder_infoRow!=null){
            collector.collect(BeHinder_infoRow);
        }

        Row antSword_infoRow = (Row) Infomap.get("antSword_info");
        if (antSword_infoRow!=null){
            collector.collect(antSword_infoRow);
        }
    }
}
