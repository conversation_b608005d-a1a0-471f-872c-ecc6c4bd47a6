package com.geeksec.threatdetector.model.nebula;

// import lombok.Data; // Removed

/**
 * <AUTHOR>
 * @Description：
 */
// @Data // Removed
public class DnsParseToInfo extends BaseEdge {
    /**
     *  DNS解析服务器IP
     */
    private String dnsServer;

    /**
     * 是否为最终解析
     */
    private Boolean finalParse;

    /**
     * 最大有效期
     */
    private Integer maxTTL;

    /**
     * 最小有效期
     */
    private Integer minTTL;

    public String getDnsServer() {
        return dnsServer;
    }

    public void setDnsServer(String dnsServer) {
        this.dnsServer = dnsServer;
    }

    public Boolean getFinalParse() { // Lombok would generate isFinalParse for boolean, but getFinalParse is also common
        return finalParse;
    }

    public void setFinalParse(Boolean finalParse) {
        this.finalParse = finalParse;
    }

    public Integer getMaxTTL() {
        return maxTTL;
    }

    public void setMaxTTL(Integer maxTTL) {
        this.maxTTL = maxTTL;
    }

    public Integer getMinTTL() {
        return minTTL;
    }

    public void setMinTTL(Integer minTTL) {
        this.minTTL = minTTL;
    }
}
