package com.geeksec.threatdetector.operator;

import com.geeksec.threatdetector.function.handler.LabelOutputTagConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.state.BroadcastState;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.co.BroadcastProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 模型开关处理器
 * 根据广播状态控制不同模型的启用和禁用
 *
 * <AUTHOR>
 */
@Slf4j
public class ModelToggleProcessor extends BroadcastProcessFunction<Row, Map, Row> {


    /**
     * 行类型到模型ID的映射
     */
    private static final Map<String, Integer> ROW_MODEL_MAP = new HashMap<>();

    static {
        // 初始化行类型到模型ID的映射
        ROW_MODEL_MAP.put("SSL_FINGER_INFO", 1);
        ROW_MODEL_MAP.put("SIP_DIP_FINGER_ROW", 2);
        ROW_MODEL_MAP.put("CLIENT_HTTP_CONNECT_DOMAIN_EDGE", 3);
        ROW_MODEL_MAP.put("DNS_PARSE_TO_EDGE", 4);
        ROW_MODEL_MAP.put("CLIENT_QUERY_DOMAIN_EDGE", 5);
        ROW_MODEL_MAP.put("SERVER_HTTP_CONNECT_DOMAIN_EDGE", 6);
        ROW_MODEL_MAP.put("CLIENT_SSL_CONNECT_DOMAIN_EDGE", 7);
        ROW_MODEL_MAP.put("SERVER_SSL_CONNECT_DOMAIN_EDGE", 8);
        ROW_MODEL_MAP.put("HTTP_WEB_LOGIN", 9);
        ROW_MODEL_MAP.put("WEB_LOGIN_INFO", 10);
        ROW_MODEL_MAP.put("PORT_SCAN_ROW", 11);
        ROW_MODEL_MAP.put("DNS_TUNNEL_INFO", 12);
        ROW_MODEL_MAP.put("CONNECT_INFO_DNS", 13);
        ROW_MODEL_MAP.put("NEOREGEO_INFO", 14);
        ROW_MODEL_MAP.put("RDP_ROW", 15);
        ROW_MODEL_MAP.put("ORACLE_ROW", 16);
        ROW_MODEL_MAP.put("MYSQL_ROW", 17);
        ROW_MODEL_MAP.put("SMB_ROW", 18);
        ROW_MODEL_MAP.put("XRAY_FINGER_ROW", 19);
        ROW_MODEL_MAP.put("SUO5_INFO", 20);
        ROW_MODEL_MAP.put("BEHINDER_INFO", 21);
        ROW_MODEL_MAP.put("ANTSWORD_INFO", 22);
        ROW_MODEL_MAP.put("ANTSWORD_PHP_INFO", 23);
        ROW_MODEL_MAP.put("HTTP_TUNNEL_INFO", 24);
        ROW_MODEL_MAP.put("TCP_TUNNEL_INFO", 25);
        ROW_MODEL_MAP.put("NTP_TUNNEL_INFO", 26);
        ROW_MODEL_MAP.put("SSL_TUNNEL_INFO", 27);
        ROW_MODEL_MAP.put("ICMP_TUNNEL_INFO", 28);
        ROW_MODEL_MAP.put("SRCP_INFO_ROW", 29);
        ROW_MODEL_MAP.put("URCP_INFO_ROW", 30);
        ROW_MODEL_MAP.put("WEB_SHELL_INFO", 31);
        ROW_MODEL_MAP.put("ENCRYPTED_TOOL_INFO", 32);
        ROW_MODEL_MAP.put("TODESK_ROW", 33);
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
    }

    @Override
    public void close() throws Exception {
        super.close();
    }

    // TODO 跑测试
    @Override
    public void processElement(Row row, BroadcastProcessFunction<Row, Map, Row>.ReadOnlyContext ctx,
            Collector<Row> collector) throws Exception {

        if (row != null && row.getArity() != 0) {
            String type = row.getField(0).toString(); // 获取sink Row 类型

            BroadcastState<Integer, Integer> stateMap = (BroadcastState<Integer, Integer>) ctx
                    .getBroadcastState(modelSwitchStateDescriptor);

            Integer model_switch = stateMap.get(ROW_MODEL_MAP.get(type));
            // 开关状态默认是1，开启
            if (Objects.isNull(model_switch)) {
                model_switch = 1;
            }
            if (model_switch == 1) {
                switch (type) {
                    case "SSL_FINGER_INFO":
                        ctx.output(LabelOutputTagConstants.SSL_FINGER_INFO, row);
                        break;
                    case "SIP_DIP_FINGER_ROW":
                        ctx.output(LabelOutputTagConstants.SIP_DIP_FINGER_ROW, row);
                        break;
                    case "CLIENT_HTTP_CONNECT_DOMAIN_EDGE":
                        ctx.output(LabelOutputTagConstants.CLIENT_HTTP_CONNECT_DOMAIN_EDGE, row);
                        break;
                    case "DNS_PARSE_TO_EDGE":
                        ctx.output(LabelOutputTagConstants.DNS_PARSE_TO_EDGE, row);
                        break;
                    case "CLIENT_QUERY_DOMAIN_EDGE":
                        ctx.output(LabelOutputTagConstants.CLIENT_QUERY_DOMAIN_EDGE, row);
                        break;
                    case "SERVER_HTTP_CONNECT_DOMAIN_EDGE":
                        ctx.output(LabelOutputTagConstants.SERVER_HTTP_CONNECT_DOMAIN_EDGE, row);
                        break;
                    case "CLIENT_SSL_CONNECT_DOMAIN_EDGE":
                        ctx.output(LabelOutputTagConstants.CLIENT_SSL_CONNECT_DOMAIN_EDGE, row);
                        break;
                    case "SERVER_SSL_CONNECT_DOMAIN_EDGE":
                        ctx.output(LabelOutputTagConstants.SERVER_SSL_CONNECT_DOMAIN_EDGE, row);
                        break;
                    case "HTTP_WEB_LOGIN":
                        ctx.output(LabelOutputTagConstants.http_weblogin_info, row);
                        break;
                    case "WEB_LOGIN_INFO":
                        ctx.output(LabelOutputTagConstants.Web_login_Info, row);
                        break;
                    case "PORT_SCAN_ROW":
                        ctx.output(LabelOutputTagConstants.Port_Scan_Row, row);
                        break;
                    case "DNS_TUNNEL_INFO":
                        ctx.output(LabelOutputTagConstants.DNS_Tunnel_Row, row);
                        break;
                    case "CONNECT_INFO_DNS":
                        ctx.output(LabelOutputTagConstants.ConnectInfo_Dns, row);
                        break;
                    case "NEOREGEO_INFO":
                        ctx.output(LabelOutputTagConstants.Neoregeo_info, row);
                        break;
                    case "RDP_ROW":
                        ctx.output(LabelOutputTagConstants.RDP_info, row);
                        break;
                    case "ORACLE_ROW":
                        ctx.output(LabelOutputTagConstants.Oracle_info, row);
                        break;
                    case "MYSQL_ROW":
                        ctx.output(LabelOutputTagConstants.MYSQL_info, row);
                        break;
                    case "SMB_ROW":
                        ctx.output(LabelOutputTagConstants.SMB_info, row);
                        break;
                    case "XRAY_FINGER_ROW":
                        ctx.output(LabelOutputTagConstants.xRay_Finger_Row, row);
                        break;
                    case "SUO5_INFO":
                        ctx.output(LabelOutputTagConstants.suo5_info, row);
                        break;
                    case "BEHINDER_INFO":
                        ctx.output(LabelOutputTagConstants.BeHinder_info, row);
                        break;
                    case "ANTSWORD_INFO":
                        ctx.output(LabelOutputTagConstants.antSword_info, row);
                        break;
                    case "ANTSWORD_PHP_INFO":
                        ctx.output(LabelOutputTagConstants.antSword_php_info, row);
                        break;
                    case "HTTP_TUNNEL_INFO":
                        ctx.output(LabelOutputTagConstants.HTTP_Tunnel_Row, row);
                        break;
                    case "TCP_TUNNEL_INFO":
                        ctx.output(LabelOutputTagConstants.TCP_Tunnel_Row, row);
                        break;
                    case "NTP_TUNNEL_INFO":
                        ctx.output(LabelOutputTagConstants.NTP_Tunnel_Row, row);
                        break;
                    case "SSL_TUNNEL_INFO":
                        ctx.output(LabelOutputTagConstants.SSL_Tunnel_Row, row);
                        break;
                    case "ICMP_TUNNEL_INFO":
                        ctx.output(LabelOutputTagConstants.ICMP_Tunnel_Row, row);
                        break;
                    case "SRCP_INFO_ROW":
                        ctx.output(LabelOutputTagConstants.srcpInfoRow, row);
                        break;
                    case "URCP_INFO_ROW":
                        ctx.output(LabelOutputTagConstants.urcpInfoRow, row);
                        break;
                    case "WEB_SHELL_INFO":
                        ctx.output(LabelOutputTagConstants.webShell_info, row);
                        break;
                    case "ENCRYPTED_TOOL_INFO":
                        ctx.output(LabelOutputTagConstants.encryptedTool_info, row);
                        break;
                    case "TODESK_ROW":
                        ctx.output(LabelOutputTagConstants.ToDeskRow, row);
                        break;
                    default:
                        break;
                }
            } else {
                log.info("__________________——{}——模型未开启__________________", ROW_MODEL_MAP.get(type));
            }
        }
    }

    @Override
    public void processBroadcastElement(Map changeMap, BroadcastProcessFunction<Row, Map, Row>.Context ctx,
            Collector<Row> collector) throws Exception {
        for (Object modelID : changeMap.keySet()) {
            Integer nowSwitchState = (Integer) changeMap.get(modelID);
            BroadcastState<Integer, Integer> stateMap = ctx.getBroadcastState(modelToggleStateDescriptor);
            if (stateMap.contains((Integer) modelID)) {
                Integer oldState = stateMap.get((Integer) modelID);
                log.info(modelID + "存在且状态为: " + oldState);
                log.info(modelID + "即将修改状态为" + nowSwitchState);
            } else {
                log.info(modelID + "不存在");
                log.info(modelID + "即将修改状态为" + nowSwitchState);
            }
            // 更新模型状态
            stateMap.put((Integer) modelID, nowSwitchState);
        }
    }
}
