package com.geeksec.threatdetector.model.tunnel;

import com.geeksec.threatdetector.model.connection.ConnectBasicInfo;


/**
 * Basic tunnel information class
 */
public class BasicTunnel {
    private ConnectBasicInfo connectBasicInfo;

    private String tunnelType;

    public ConnectBasicInfo getConnectBasicInfo() {
        return connectBasicInfo;
    }

    public void setConnectBasicInfo(ConnectBasicInfo connectBasicInfo) {
        this.connectBasicInfo = connectBasicInfo;
    }

    public Object getNeededInfo() {
        return null;
    }

    public void setNeededInfo(Object neededInfo) {
        // Do nothing - NeededInfo has been removed
    }

    public String getTunnelType() {
        return tunnelType;
    }

    public void setTunnelType(String tunnelType) {
        this.tunnelType = tunnelType;
    }

    @Override
    public String toString() {
        return "BasicTunnel{" +
                "connectBasicInfo=" + connectBasicInfo +
                ", tunnelType='" + tunnelType + '\'' +
                '}';
    }
}
