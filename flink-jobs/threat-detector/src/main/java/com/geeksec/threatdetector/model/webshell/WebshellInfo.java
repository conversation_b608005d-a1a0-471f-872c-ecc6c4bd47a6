package com.geeksec.threatdetector.model.webshell;

import com.geeksec.threatdetector.model.connection.ConnectBasicInfo;
import com.geeksec.threatdetector.model.protocol.HttpSimpleInfo;
import com.geeksec.threatdetector.model.protocol.SslSimpleInfo;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * Webshell 检测信息。
 *
 * @param connectionDetails 基础会话信息
 * @param sslInfoList 基础SSL元数据信息列表
 * @param httpInfoList 基础HTTP信息列表
 * @param isUnidirectional Webshell是否单向
 * @param type Webshell类型
 * @param isDetected 是否检测到webshell
 * @param detectedToolName 检测到的webshell工具名称
 * @param detectedToolTag 检测到的webshell工具标签
 * @param detectedToolVersion 检测到的webshell工具版本
 * @param detectedToolDescription 检测到的webshell工具描述
 * @param toolLabel 检测到的webshell工具标签 (详细分类)
 * @param toolLabelDescription 检测到的webshell工具标签描述
 * @param riskIdentifier 检测到的webshell工具风险标识
 * @param riskDescription 检测到的webshell工具风险描述
 * @param riskLevel 检测到的webshell工具风险等级
 * @param riskScore 检测到的webshell工具风险评分
 * @param riskType 检测到的webshell工具风险类型
 * @param riskSource 检测到的webshell工具风险来源
 * @param riskSourceType 检测到的webshell工具风险来源类型
 * @param riskSourceVersion 检测到的webshell工具风险来源版本
 * @param riskSourceDescription 检测到的webshell工具风险来源描述
 * @param riskSourceUrl 检测到的webshell工具风险来源URL
 * @param riskSourceReferences 检测到的webshell工具风险来源参考
 * @param cveId 检测到的webshell工具风险CVE编号
 * @param cweId 检测到的webshell工具风险CWE编号
 * @param owaspCategory 检测到的webshell工具风险OWASP分类
 * @param sansTop25Category 检测到的webshell工具风险SANS Top 25分类
 * @param certCcCategory 检测到的webshell工具风险CERT CC分类
 * @param wascCategory 检测到的webshell工具风险WASC分类
 * @param pciDssContext 检测到的webshell工具风险PCI DSS合规性相关
 * @param hipaaContext 检测到的webshell工具风险HIPAA合规性相关
 * @param gdprContext 检测到的webshell工具风险GDPR合规性相关
 * @param nistContext 检测到的webshell工具风险NIST标准相关
 * @param iso27001Context 检测到的webshell工具风险ISO 27001标准相关
 * <AUTHOR>
 */
@Slf4j
public record WebshellInfo(
    ConnectBasicInfo connectionDetails,
    List<SslSimpleInfo> sslInfoList,
    List<HttpSimpleInfo> httpInfoList,
    boolean isUnidirectional,
    String type,
    boolean isDetected,
    String detectedToolName,
    String detectedToolTag,
    String detectedToolVersion,
    String detectedToolDescription,
    String toolLabel,
    String toolLabelDescription,
    String riskIdentifier,
    String riskDescription,
    String riskLevel,
    String riskScore,
    String riskType,
    String riskSource,
    String riskSourceType,
    String riskSourceVersion,
    String riskSourceDescription,
    String riskSourceUrl,
    String riskSourceReferences,
    String cveId,
    String cweId,
    String owaspCategory,
    String sansTop25Category,
    String certCcCategory,
    String wascCategory,
    String pciDssContext,
    String hipaaContext,
    String gdprContext,
    String nistContext,
    String iso27001Context
) {

    /**
     * Webshell单向流量标签tag号
     * TODO 标签号待定 (来自原代码)
     */
    public static final String UNIDIRECTIONAL_TRAFFIC_TAG = "xxxxx";

    /**
     * 支持的解析的webshell类工具的标签列表
     */
    public static final List<String> SUPPORTED_WEBSHELL_TOOLS = List.of(
        "中国菜刀", "中国蚁剑", "冰蝎", "哥斯拉", "CobaltStrike", "Metasploit"
    );

    /**
     * 紧凑型构造函数，用于校验和确保集合的不可变性。
     * 同时处理null列表输入，将其视为空列表，以匹配原POJO的初始化行为。
     */
    public WebshellInfo {
        this.sslInfoList = (sslInfoList == null) ? List.of() : List.copyOf(sslInfoList);
        this.httpInfoList = (httpInfoList == null) ? List.of() : List.copyOf(httpInfoList);
        // 其他对象类型的组件 (如 ConnectBasicInfo, String) 如果传入 null，则它们的值即为 null。
        // 如果需要对某些组件强制非空，可以在此添加 Objects.requireNonNull(component, "component must not be null");
    }
}
