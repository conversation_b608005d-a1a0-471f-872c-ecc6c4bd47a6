package com.geeksec.threatdetector.operator.alarm.builder;

import com.alibaba.fastjson.JSONObject;
import org.apache.flink.types.Row;
import redis.clients.jedis.Jedis;

/**
 * Base class for alarm builders that convert Rows to JSON objects for different types of alarms.
 * <AUTHOR>
 */
public abstract class BaseAlarmBuilder {
    
    /**
     * Converts a Row to a JSON object representing an alarm.
     * 
     * @param alarmRow The input Row containing alarm data
     * @param jedis Redis client for additional data lookup
     * @return JSONObject containing the alarm data
     */
    public abstract JSONObject getAlarmJson(Row alarmRow, Jedis jedis);
    
    /**
     * Creates a base alarm JSON object with common fields.
     * 
     * @param alarmType Type of the alarm
     * @param alarmSubType Subtype of the alarm (can be null)
     * @return A JSONObject with common alarm fields initialized
     */
    protected JSONObject createBaseAlarmJson(String alarmType, String alarmSubType) {
        JSONObject alarmJson = new JSONObject();
        alarmJson.put("alarm_type", alarmType);
        if (alarmSubType != null) {
            alarmJson.put("alarm_subtype", alarmSubType);
        }
        alarmJson.put("timestamp", System.currentTimeMillis());
        return alarmJson;
    }
    
    /**
     * Gets a string field from a Row safely.
     * 
     * @param row The Row to get the field from
     * @param fieldIndex The index of the field
     * @return The field value as a String, or null if the field is null or the index is out of bounds
     */
    protected String getStringFieldSafely(Row row, int fieldIndex) {
        try {
            Object value = row.getField(fieldIndex);
            return value != null ? value.toString() : null;
        } catch (IndexOutOfBoundsException e) {
            return null;
        }
    }
}
