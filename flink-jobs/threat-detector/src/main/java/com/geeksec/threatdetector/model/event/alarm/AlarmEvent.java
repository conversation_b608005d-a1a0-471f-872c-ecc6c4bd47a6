package com.geeksec.threatdetector.model.event.alarm;

import com.geeksec.threatdetector.model.event.BaseEvent;
import lombok.EqualsAndHashCode;

import java.util.Map;

/**
 * 告警事件
 */

@EqualsAndHashCode(callSuper = true)
public class AlarmEvent extends BaseEvent {
    private String id;
    private String type;
    private String name;
    private String severity;
    private String description;
    private String solution;
    private Map<String, Object> details;
    private boolean isHandled;
    private long handledTime;
    private String handledBy;
    private String handledComment;
    
    @Override
    public String getEventType() {
        return "alarm";
    }
    
    // Explicit getters for compatibility with Lombok
    public String getType() {
        return type;
    }
    
    public String getName() {
        return name;
    }
    
    public String getSeverity() {
        return severity;
    }
    
    // 显式添加 setter 方法以解决编译问题
    public void setId(String alertId) {
        this.id = alertId;
    }
    
    public void setType(String alertType) {
        this.type = alertType;
    }
    
    public void setName(String alertName) {
        this.name = alertName;
    }
    
    public void setSeverity(String alertLevel) {
        this.severity = alertLevel;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public void setSolution(String solution) {
        this.solution = solution;
    }
    
    public void setDetails(Map<String, Object> details) {
        this.details = details;
    }
    
    public void setHandled(boolean handled) {
        isHandled = handled;
    }
    
    public void setHandledTime(long handledTime) {
        this.handledTime = handledTime;
    }
    
    public void setHandledBy(String handledBy) {
        this.handledBy = handledBy;
    }
    
    public void setHandledComment(String handledComment) {
        this.handledComment = handledComment;
    }
    
    public String getId() {
        return id;
    }
    
    public String getDescription() {
        return description;
    }
    
    public String getSolution() {
        return solution;
    }
    
    public Map<String, Object> getDetails() {
        return details;
    }
    
    public boolean isHandled() {
        return isHandled;
    }
    
    public long getHandledTime() {
        return handledTime;
    }
    
    public String getHandledBy() {
        return handledBy;
    }
    
    public String getHandledComment() {
        return handledComment;
    }
}
