package com.geeksec.threatdetector.operator.alarm.builder;

import com.alibaba.fastjson.JSONObject;
import org.apache.flink.types.Row;
import redis.clients.jedis.Jedis;

/**
 * 加密隧道告警构建器
 * 用于构建加密隧道相关的告警信息
 *
 * <AUTHOR>
 * @since 2023/01/01
 */
public class EncryptedTunnelAlarmBuilder extends BaseAlarmBuilder {
    
    /**
     * 单例实例
     */
    private static final EncryptedTunnelAlarmBuilder INSTANCE = new EncryptedTunnelAlarmBuilder();
    
    /**
     * 私有构造函数
     */
    private EncryptedTunnelAlarmBuilder() {
        // 私有构造函数
    }
    
    /**
     * 获取单例实例
     *
     * @return 单例实例
     */
    public static EncryptedTunnelAlarmBuilder getInstance() {
        return INSTANCE;
    }
    
    @Override
    public JSONObject getAlarmJson(Row alarmRow, Jedis jedis) {
        // 创建基础告警JSON
        JSONObject alarmJson = createBaseAlarmJson("encrypted_tunnel", null);
        
        // 添加特定于加密隧道告警的字段
        // 这里根据实际需求从alarmRow中提取并设置字段
        
        return alarmJson;
    }
}
