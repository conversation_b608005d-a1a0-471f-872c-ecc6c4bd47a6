package com.geeksec.threatdetector.operator.alarm.builder;

import com.alibaba.fastjson.JSONObject;
import org.apache.flink.types.Row;
import redis.clients.jedis.Jedis;

/**
 * Builder for DNS tunnel related alarms.
 * <AUTHOR>
 */
public class DnsTunnelAlarmBuilder extends BaseAlarmBuilder {
    
    private static final DnsTunnelAlarmBuilder INSTANCE = new DnsTunnelAlarmBuilder();
    
    private DnsTunnelAlarmBuilder() {}
    
    public static DnsTunnelAlarmBuilder getInstance() {
        return INSTANCE;
    }
    
    @Override
    public JSONObject getAlarmJson(Row alarmRow, Jedis jedis) {
        JSONObject alarmJson = createBaseAlarmJson("DNS隐蔽隧道", null);
        
        // Extract fields from the Row and add to the JSON object
        alarmJson.put("source_ip", getStringFieldSafely(alarmRow, 2));
        alarmJson.put("source_port", getStringFieldSafely(alarmRow, 3));
        alarmJson.put("destination_ip", getStringFieldSafely(alarmRow, 4));
        alarmJson.put("destination_port", getStringFieldSafely(alarmRow, 5));
        alarmJson.put("domain", getStringFieldSafely(alarmRow, 6));
        alarmJson.put("timestamp", getStringFieldSafely(alarmRow, 7));
        
        return alarmJson;
    }
    
    /**
     * Static helper method to build a DNS tunnel alarm JSON.
     * @param alarmRow The input Row containing alarm data
     * @param jedis Redis client for additional data lookup
     * @return JSONObject containing the alarm data
     */
    public static JSONObject buildDnsTunnelAlarmJson(Row alarmRow, Jedis jedis) {
        return getInstance().getAlarmJson(alarmRow, jedis);
    }
}
