package com.geeksec.threatdetector.operator.alarm.builder;

import com.alibaba.fastjson.JSONObject;
import org.apache.flink.types.Row;
import redis.clients.jedis.Jedis;

/**
 * 加密隧道攻击告警构建器
 * 用于构建加密隧道攻击相关的告警信息
 *
 * <AUTHOR>
 * @since 2023/01/01
 */
public class EncryptedTunnelAttackAlarmBuilder extends BaseAlarmBuilder {
    
    /**
     * 单例实例
     */
    private static final EncryptedTunnelAttackAlarmBuilder INSTANCE = new EncryptedTunnelAttackAlarmBuilder();
    
    /**
     * 私有构造函数
     */
    private EncryptedTunnelAttackAlarmBuilder() {
        // 私有构造函数
    }
    
    /**
     * 获取单例实例
     *
     * @return 单例实例
     */
    public static EncryptedTunnelAttackAlarmBuilder getInstance() {
        return INSTANCE;
    }
    
    @Override
    public JSONObject getAlarmJson(Row alarmRow, Jedis jedis) {
        // 创建基础告警JSON
        JSONObject alarmJson = createBaseAlarmJson("encrypted_tunnel_attack", null);
        
        // 添加特定于加密隧道攻击告警的字段
        // 这里根据实际需求从alarmRow中提取并设置字段
        
        return alarmJson;
    }
}
