package com.geeksec.threatdetector.operator;

import org.apache.flink.api.java.functions.KeySelector;
import org.apache.flink.types.Row;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/11/3
 */

public class SipDipFingerLabelKeySelector implements KeySelector<Row, List<String>> {
    @Override
    public List<String> getKey(Row row) throws Exception {
        List<String> sipDipFingerLabelKey = row.getFieldAs(1);
        return sipDipFingerLabelKey;
    }
}
