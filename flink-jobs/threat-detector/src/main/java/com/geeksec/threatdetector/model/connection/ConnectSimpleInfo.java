package com.geeksec.threatdetector.model.connection;

import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * 较为简单的会话信息，主要包括五元组信息和探针打的标签，以及包序列及负载信息。
 *
 * @param sIp 源IP
 * @param dIp 目的IP
 * @param sPort 源端口
 * @param dPort 目的端口
 * @param appName 协议信息
 * @param startTime 会话开始时间
 * @param sessionId 会话ID
 * @param labels 探针打的标签
 * @param packetInfoList 包序列信息
 * @param client4PayloadList 客户端前4个包负载信息
 * @param server4PayloadList 服务端前4个包负载信息
 * <AUTHOR>
 */
@Slf4j
public record ConnectSimpleInfo(
    String sIp,
    String dIp,
    String sPort,
    String dPort,
    String appName,
    Long startTime,
    String sessionId,
    List<Integer> labels,
    List<PacketInfo> packetInfoList,
    List<String> client4PayloadList,
    List<String> server4PayloadList
) {

    /**
     * 从 Map 对象创建 ConnectSimpleInfo 实例的静态工厂方法。
     *
     * @param pbMap 包含会话信息的 Map
     * @return ConnectSimpleInfo 的新实例
     */
    @SuppressWarnings("unchecked") // 对于从Map中强制转换类型进行抑制
    public static ConnectSimpleInfo fromMap(Map<String, Object> pbMap) {
        if (pbMap == null) {
            // 或者根据业务需求抛出 IllegalArgumentException
            log.warn("Input map is null, returning ConnectSimpleInfo with all null/empty fields.");
            return new ConnectSimpleInfo(null, null, null, null, null, null, null, List.of(), List.of(), List.of(), List.of());
        }

        String sIp = (String) pbMap.get("sIp");
        String dIp = (String) pbMap.get("dIp");
        String sPort = (String) pbMap.get("sPort");
        String dPort = (String) pbMap.get("dPort");
        String appName = (String) pbMap.get("AppProto");
        Long startTime = pbMap.get("StartTime") instanceof Number ? ((Number) pbMap.get("StartTime")).longValue() : null;
        String sessionId = (String) pbMap.get("SessionId");

        List<Integer> labels = pbMap.get("Labels") instanceof List ? 
                                List.copyOf((List<Integer>) pbMap.get("Labels")) : 
                                List.of();
        List<PacketInfo> packetInfoList = pbMap.get("pktInfo") instanceof List ? 
                                          List.copyOf((List<PacketInfo>) pbMap.get("pktInfo")) : 
                                          List.of();
        
        // 注意：原始代码中 client4PayloadList 使用 "Server4PayloadList"键，server4PayloadList 使用 "Client4PayloadList"键。
        // 这里保持了原始逻辑，请确认是否正确。
        List<String> client4PayloadList = pbMap.get("Server4PayloadList") instanceof List ? 
                                          List.copyOf((List<String>) pbMap.get("Server4PayloadList")) : 
                                          List.of();
        List<String> server4PayloadList = pbMap.get("Client4PayloadList") instanceof List ? 
                                          List.copyOf((List<String>) pbMap.get("Client4PayloadList")) : 
                                          List.of();

        return new ConnectSimpleInfo(
            sIp,
            dIp,
            sPort,
            dPort,
            appName,
            startTime,
            sessionId,
            labels,
            packetInfoList,
            client4PayloadList,
            server4PayloadList
        );
    }
    
    // 紧凑构造函数，用于确保传入的集合参数在通过规范构造函数创建Record实例时是不可变的。
    // 如果通过 new ConnectSimpleInfo(...) 直接创建，此构造函数会确保集合的不可变性。
    // 对于 fromMap 工厂方法，因为我们已经使用了 List.copyOf()，所以这里的处理是冗余的，但保留它可以增加直接构造时的安全性。
    public ConnectSimpleInfo {
        this.labels = (labels == null) ? List.of() : List.copyOf(labels);
        this.packetInfoList = (packetInfoList == null) ? List.of() : List.copyOf(packetInfoList);
        this.client4PayloadList = (client4PayloadList == null) ? List.of() : List.copyOf(client4PayloadList);
        this.server4PayloadList = (server4PayloadList == null) ? List.of() : List.copyOf(server4PayloadList);
    }
}
