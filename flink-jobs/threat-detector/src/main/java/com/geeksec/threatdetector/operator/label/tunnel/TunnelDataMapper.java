package com.geeksec.threatdetector.operator.label.tunnel;

import com.geeksec.threatdetector.model.tunnel.BasicTunnel;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 隧道数据映射器，负责将不同类型的隧道对象映射为Flink的行数据格式
 * 支持ICMP、NTP、TCP等隧道类型
 * 
 * <AUTHOR>
 * <AUTHOR>
 * @since 2024/07/26
 */
public class TunnelDataMapper extends RichFlatMapFunction<BasicTunnel, Row> {

    private static final Logger logger = LoggerFactory.getLogger(TunnelDataMapper.class);

    @Override
    public void close() throws Exception {
        super.close();
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
    }

    @Override
    public void flatMap(BasicTunnel basicTunnel, Collector<Row> collector) throws Exception {
        // 根据隧道类型创建相应的行数据
        Row tunnelRow = new Row(2);
        
        switch (basicTunnel.getTunnelType()) {
            case "ICMP":
                tunnelRow.setField(0, "icmp_tunnel_info");
                tunnelRow.setField(1, basicTunnel);
                collector.collect(tunnelRow);
                break;
            case "NTP":
                tunnelRow.setField(0, "ntp_tunnel_info");
                tunnelRow.setField(1, basicTunnel);
                collector.collect(tunnelRow);
                break;
            case "TCP":
                tunnelRow.setField(0, "tcp_tunnel_info");
                tunnelRow.setField(1, basicTunnel);
                collector.collect(tunnelRow);
                break;
            case "SSL":
                tunnelRow.setField(0, "ssl_tunnel_info");
                tunnelRow.setField(1, basicTunnel);
                collector.collect(tunnelRow);
                break;
            case "HTTP":
                tunnelRow.setField(0, "http_tunnel_info");
                tunnelRow.setField(1, basicTunnel);
                collector.collect(tunnelRow);
                break;
            default:
                logger.warn("Unknown tunnel type: {}", basicTunnel.getTunnelType());
                break;
        }
    }
}
