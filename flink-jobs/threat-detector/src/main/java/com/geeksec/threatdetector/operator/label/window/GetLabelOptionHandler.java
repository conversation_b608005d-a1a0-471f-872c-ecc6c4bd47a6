package com.geeksec.threatdetector.operator.label.window;

import com.geeksec.threatdetector.common.enums.ThreatTypeEnum;
import com.geeksec.threatdetector.function.alarm.writer.WriteScanActAlarm;
import com.geeksec.threatdetector.function.label.PredictionService;
import com.geeksec.threatdetector.infra.outputtag.SinkOutPutTag.AlarmOutPutTag;
import com.geeksec.threatdetector.infra.outputtag.SinkOutPutTag.NebulaEdgeOutPutTag;
import com.geeksec.threatdetector.infra.outputtag.SinkOutPutTag.SessionOutPutTag;
import com.geeksec.threatdetector.infra.selector.SipDipFingerLabelKeySelector;
import com.geeksec.threatdetector.infra.watermark.MyWatermarkStrategy;
import org.apache.flink.api.common.functions.ReduceFunction;
import org.apache.flink.api.java.functions.KeySelector;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.streaming.api.functions.windowing.ProcessWindowFunction;
import org.apache.flink.streaming.api.windowing.assigners.TumblingEventTimeWindows;
import org.apache.flink.streaming.api.windowing.time.Time;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

import static com.geeksec.threatdetector.common.util.AlarmUtils.getLabelEdgeRow;
import static com.geeksec.threatdetector.pipeline.ThreatDetectionPipeline.PARALLELISM_4;

/**
 * <AUTHOR>
 * @Date 2022/11/3
 */

public class GetLabelOptionHandler {
    private static final Logger logger = LoggerFactory.getLogger(GetLabelOptionHandler.class);
    public static SingleOutputStreamOperator<Row> randomFingerToSipLabelFunction(DataStream<Row> dipdSSLFingerSipRow){
        long randomFingerToSip_delay = 30000;//30秒
        int randomFingerToSip_timeStampPosition = 3;
        MyWatermarkStrategy myWatermarkStrategy = new MyWatermarkStrategy();
        myWatermarkStrategy.setMyEventTimeTimeStamp(randomFingerToSip_timeStampPosition);
        myWatermarkStrategy.setMyDelay(randomFingerToSip_delay);
        DataStream<Row> randomFingerToSipLabelRowWithTimeStamp = dipdSSLFingerSipRow.assignTimestampsAndWatermarks(myWatermarkStrategy).name("生成事件时间戳和水印").setParallelism(2);//打上时间戳和水印都打
//        DataStream<Row> randomFingerToSipLabelRowWithTimeStamp = dipdSSLFingerSipRow.assignTimestampsAndWatermarks(new BoundedOutOfOrdernessTimestampExtractor<Row>(Time.seconds(0)) {
//            @Override
//            public long extractTimestamp(Row row) {
//                Long timeStamp = Long.valueOf(row.getField(3).toString())*1000;
//                return timeStamp;
//            }
//        });//只打时间戳,没有watermark

        SingleOutputStreamOperator<Row> randomFingerToSipLabelRow = randomFingerToSipLabelRowWithTimeStamp.keyBy(new SipDipFingerLabelKeySelector())
            .window(TumblingEventTimeWindows.of(Time.minutes(1)))//5分钟为窗口大小
            .reduce(new ReduceFunction<Row>() {
                @Override
                public Row reduce(Row row, Row t1) throws Exception {
                    Collection<String> fingerSetA = row.getFieldAs(2);
                    Collection<String> fingerSetB = t1.getFieldAs(2);
                    fingerSetA.addAll(fingerSetB);
                    Collection<String> SessionId_listA = row.getFieldAs(4);
                    Collection<String> SessionId_listB = t1.getFieldAs(4);
//                    if (SessionId_listA.size()<=10){
//                        SessionId_listA.addAll(SessionId_listB);
//                    }
                    SessionId_listA.addAll(SessionId_listB);
                    Row tmp = new Row(7);
                    tmp.setField(0,row.getField(0));
                    tmp.setField(1,row.getField(1));
                    tmp.setField(2,fingerSetA);
                    tmp.setField(3,row.getField(3));
                    tmp.setField(4,SessionId_listA);
                    tmp.setField(5,row.getFieldAs(5));
                    tmp.setField(6,row.getFieldAs(6));
                    return tmp;
                }
            }).name("finger数量进行窗口聚合").setParallelism(PARALLELISM_4)
            .process(new ProcessFunction<Row, Row>() {
                @Override
                public void processElement(Row row, ProcessFunction<Row, Row>.Context context, Collector<Row> collector) throws Exception {
                    Collection<String> fingerSet = row.getFieldAs(2);
                    if (fingerSet.size()>9){
                        List<String> IpList= (List<String>)row.getField(1);
                        if(fingerSet.size()<50){
                            String dIp = IpList.get(0);
                            String sIp = IpList.get(1);
                            Row resultRow = getLabelEdgeRow(dIp, ThreatTypeEnum.RANDOM_FINGER.getCode());//指纹随机化服务端IP标签
                            collector.collect(resultRow);
                            Row alarm_row =new Row(8);
                            alarm_row.setField(0,"指纹随机化访问服务端");
                            alarm_row.setField(1,sIp);
                            alarm_row.setField(2,dIp);
                            alarm_row.setField(3,fingerSet);
                            alarm_row.setField(4,ThreatTypeEnum.RANDOM_FINGER.getCode());
                            alarm_row.setField(5,row.getField(4));
                            alarm_row.setField(6,row.getField(5));
                            alarm_row.setField(7,row.getField(6));
                            collector.collect(alarm_row);
                            logger.info("Random_Finger_Server_Label 边插入{},1007",dIp);
                            logger.info("指纹随机化访问服务端告警{}",dIp);
                            Collection<String> SessionId_collection = row.getFieldAs(4);
                            for (String sessionId:SessionId_collection){
                                Row SessionLabelRow0 = new Row(4);
                                SessionLabelRow0.setField(0,"会话打标");
                                SessionLabelRow0.setField(1,sessionId);
                                Set<String> sessionLabels = new HashSet<>();
                                sessionLabels.add(ThreatTypeEnum.RANDOM_PASS_EXT.getCode());// 密码套件随机性
                                sessionLabels.add(ThreatTypeEnum.RANDOM_ENCRYPT_EXT.getCode());// 加密套件随机性
                                sessionLabels.add(ThreatTypeEnum.ALG_RANDOM_ML.getCode());// 随机数对抗算法
                                sessionLabels.add(ThreatTypeEnum.ALG_COMPRESS_ML.getCode());// 压缩函数对抗算法
                                sessionLabels.add(ThreatTypeEnum.ALG_ENCODE_ML.getCode());// 编码对抗算法
                                sessionLabels.add(ThreatTypeEnum.ALG_HIDDEN_ML.getCode());// 隐写对抗算法
                                sessionLabels.add(ThreatTypeEnum.ALG_ADV_ML.getCode());// 行为对抗算法
                                SessionLabelRow0.setField(2,sessionLabels);
                                SessionLabelRow0.setField(3,row.getFieldAs(5));
                                collector.collect(SessionLabelRow0);
                            }
                        }else {
                            String dIp = IpList.get(0);
                            String sIp = IpList.get(1);
                            Row alarm_row =new Row(8);
                            alarm_row.setField(0,"加密通道攻击行为");
                            alarm_row.setField(1,"AppScan工具");
                            alarm_row.setField(2,sIp);
                            alarm_row.setField(3,dIp);
                            alarm_row.setField(4,fingerSet);
                            alarm_row.setField(5,row.getField(4));
                            alarm_row.setField(6,row.getField(5));
                            alarm_row.setField(7,row.getField(6));
                            collector.collect(alarm_row);
                            Row resultRow = getLabelEdgeRow(sIp,ThreatTypeEnum.APP_SCAN.getCode());//APP_SCAN工具IP标签
                            collector.collect(resultRow);
                            logger.info("APP_SCAN工具 边插入{},27062",sIp);
                            logger.info("APP_SCAN工具告警{}",sIp);
                        }

                    }
                }
            })
            .name("窗口函数获取指纹随机化服务端IP标签，获取APP_SCAN工具告警").setParallelism(PARALLELISM_4)
            .process(new ProcessFunction<Row, Row>() {
                @Override
                public void processElement(Row row, ProcessFunction<Row, Row>.Context context, Collector<Row> collector) throws Exception {
                    if (row.getField(1).equals(ThreatTypeEnum.RANDOM_FINGER.getCode())){
                        context.output(NebulaEdgeOutPutTag.Nebula_SIP_DIP_FINGER_ROW,row);
                    } else if (row.getField(1).equals(ThreatTypeEnum.APP_SCAN.getCode())) {
                        context.output(NebulaEdgeOutPutTag.Nebula_APP_SCAN_EdgeRow,row);
                    } else if (row.getField(0).equals("会话打标")) {
                        context.output(SessionOutPutTag.Session_RandomFinger_info,row);
                    } else {
                        if(row.getField(0).equals("加密通道攻击行为")){
                            context.output(AlarmOutPutTag.Alarm_APP_SCAN_ROW,row);
                        }else {
                            context.output(AlarmOutPutTag.Alarm_SIP_DIP_FINGER_ROW,row);
                        }

                    }
                }
            }).name("ES告警和Nebula打标分流").setParallelism(1);

       return randomFingerToSipLabelRow;
    }

    public static SingleOutputStreamOperator<Row> webLoginCrackingFunction(DataStream<Row> webLoginCrackingRow){
        long delay = 3000;//3秒
        int timeStampPosition = 3;
        MyWatermarkStrategy myWatermarkStrategy = new MyWatermarkStrategy();
        myWatermarkStrategy.setMyEventTimeTimeStamp(timeStampPosition);
        myWatermarkStrategy.setMyDelay(delay);
        DataStream<Row> webLoginCrackingLabelRowWithTimestamp = webLoginCrackingRow.assignTimestampsAndWatermarks(myWatermarkStrategy).name("生成事件时间戳和水印").setParallelism(2);//打上时间戳和水印都打

        SingleOutputStreamOperator<Row> webLoginCrackingLabelRow = webLoginCrackingLabelRowWithTimestamp.keyBy(new SipDipFingerLabelKeySelector())
                .window(TumblingEventTimeWindows.of(Time.seconds(10)))//10s为窗口大小
                .process(new ProcessWindowFunction<Row, Row, List<String>, TimeWindow>() {
                    @Override
                    public void process(List<String> strings, ProcessWindowFunction<Row, Row, List<String>, TimeWindow>.Context context, Iterable<Row> iterable, Collector<Row> collector) throws Exception {
                        int getCount = 0;
                        int postCount = 0;
                        Collection<String> SessionID_list = new HashSet<>();
                        for (Row row:iterable){
                            String Act = row.getFieldAs(2);
                            if (Act.equals("GET")){
                                getCount+=1;
                            }
                            if (Act.equals("POST")){
                                postCount+=1;
                            }
                            if (SessionID_list.size()<=10){
                                SessionID_list.addAll(row.getFieldAs(4));
                            }
                        }
                        if (getCount>=100 || postCount>=100){
                            String sIp = strings.get(0);
                            Row resultRow = getLabelEdgeRow(sIp,ThreatTypeEnum.WEB_LOG_BRUTE.getCode());//web登录爆破
                            collector.collect(resultRow);
                            Row alarm_row =new Row(9);
                            String dIp = strings.get(1);
                            alarm_row.setField(0,"扫描行为");
                            alarm_row.setField(1,"web登录爆破");
                            alarm_row.setField(2,sIp);
                            alarm_row.setField(3,dIp);
                            int max_count = Math.max(getCount,postCount);
                            Map<String,Integer> max_count_map = new HashMap<>();
                            max_count_map.put("Get或Post请求次数",max_count);
                            alarm_row.setField(4,max_count_map);
                            alarm_row.setField(5,ThreatTypeEnum.WEB_LOG_BRUTE.getCode());
                            alarm_row.setField(6,SessionID_list);
                            alarm_row.setField(7,iterable.iterator().next().getFieldAs(5));
                            alarm_row.setField(8,iterable.iterator().next().getFieldAs(6));
                            collector.collect(alarm_row);
                            logger.info("Http_Web_Login 边插入{}",sIp);
                            logger.info("web登录爆破告警{}",sIp);
                        }
                    }
                })
                .name("窗口函数获取web登录爆破标签").setParallelism(PARALLELISM_4)
                .process(new ProcessFunction<Row, Row>() {
                    @Override
                    public void processElement(Row row, ProcessFunction<Row, Row>.Context context, Collector<Row> collector) throws Exception {
                        if (row.getField(1).equals(ThreatTypeEnum.WEB_LOG_BRUTE.getCode())){
                            context.output(NebulaEdgeOutPutTag.Nebula_Web_Login_Info,row);
                        }else {
                            context.output(AlarmOutPutTag.Alarm_Web_Login_Info,row);
                        }
                    }
                }).name("ES告警和Nebula打标分流").setParallelism(1);

        return webLoginCrackingLabelRow;
   }

    public static SingleOutputStreamOperator<Row> portScanFunction(DataStream<Row> Port_Scan_Row){
        long delay = 3000;//3秒
        int timeStampPosition = 3;
        MyWatermarkStrategy myWatermarkStrategy = new MyWatermarkStrategy();
        myWatermarkStrategy.setMyEventTimeTimeStamp(timeStampPosition);
        myWatermarkStrategy.setMyDelay(delay);
        DataStream<Row> Port_Scan_RowWithTimeStamp = Port_Scan_Row.assignTimestampsAndWatermarks(myWatermarkStrategy).name("生成事件时间戳和水印").setParallelism(2);//打上时间戳和水印都打

        SingleOutputStreamOperator<Row> Port_Scan_sIp_LabelRow = Port_Scan_RowWithTimeStamp.keyBy((KeySelector<Row, String>) row -> {
                    String sIp = row.getFieldAs(1);
                    return sIp;})
                .window(TumblingEventTimeWindows.of(Time.seconds(10)))//10s为窗口大小
                .process(new ProcessWindowFunction<Row, Row, String, TimeWindow>() {
                    @Override
                    public void process(String sIp, ProcessWindowFunction<Row, Row, String, TimeWindow>.Context context, Iterable<Row> iterable, Collector<Row> collector) throws Exception {
                        HashSet<Integer> dPortSet = new HashSet<>();
                        HashSet<String> dIpSet = new HashSet<>();
                        Map<String,String> dip_session = new HashMap<>();
                        for (Row row:iterable){
                            int port = row.getFieldAs(4);
                            String dIp = row.getFieldAs(2);
                            dPortSet.add(port);
                            dIpSet.add(dIp);
                            dip_session.put(dIp,row.getFieldAs(5));
                        }
                        if (dIpSet.size()>=200 || dPortSet.size()>=200){
                            Row resultRow = getLabelEdgeRow(sIp,ThreatTypeEnum.PORT_SCAN.getCode());//端口扫描
                            collector.collect(resultRow);
                            Collection<String> SessionID_list = new HashSet<>();
                            Collection<String> sub_dIpSet = new HashSet<>();
                            for (String dip:dIpSet){
                                if (sub_dIpSet.size()<=10){
                                    SessionID_list.add(dip_session.get(dip));
                                    sub_dIpSet.add(dip);
                                }
                            }
                            Row alarm_row =new Row(10);
                            alarm_row.setField(0,"扫描行为");
                            alarm_row.setField(1,"端口扫描sip");
                            alarm_row.setField(2,sIp);
                            alarm_row.setField(3,dIpSet);
                            alarm_row.setField(4,dPortSet);
                            alarm_row.setField(5,ThreatTypeEnum.PORT_SCAN.getCode());
                            alarm_row.setField(6,SessionID_list);
                            alarm_row.setField(7,sub_dIpSet);
                            alarm_row.setField(8,iterable.iterator().next().getFieldAs(6));
                            alarm_row.setField(9,iterable.iterator().next().getFieldAs(7));
                            collector.collect(alarm_row);
                            logger.info("端口扫描sip告警",sIp);
                        }
                    }
                })
                .name("窗口函数获取端口扫描客户端IP标签").setParallelism(PARALLELISM_4);

        SingleOutputStreamOperator<Row> Port_Scan_dIp_LabelRow = Port_Scan_RowWithTimeStamp.keyBy((KeySelector<Row, String>) row -> {
                    String dIp = row.getFieldAs(2);
                    return dIp;})
                .window(TumblingEventTimeWindows.of(Time.seconds(10)))//10s为窗口大小
                .process(new ProcessWindowFunction<Row, Row, String, TimeWindow>() {
                    @Override
                    public void process(String dIp, ProcessWindowFunction<Row, Row, String, TimeWindow>.Context context, Iterable<Row> iterable, Collector<Row> collector) throws Exception {
                        HashSet<Integer> portSet = new HashSet<>();
                        Map<String,Set<Integer>> sIp_port_Set = new HashMap<>();
                        Map<String,String> sip_session = new HashMap<>();
                        for (Row row:iterable){
                            int dPort = row.getFieldAs(4);
                            portSet.add(dPort);
                            String sIp = row.getFieldAs(1);
                            if (sIp_port_Set.keySet().contains(sIp)){
                                Set<Integer> port_set =  sIp_port_Set.get(sIp);
                                port_set.add(dPort);
                                sIp_port_Set.put(sIp,port_set);
                            }else{
                                Set<Integer> port_set = new HashSet<>();
                                port_set.add(dPort);
                                sIp_port_Set.put(sIp,port_set);
                                sip_session.put(sIp,row.getFieldAs(5));
                            }
                        }
                        if (portSet.size()>=200){
                            // 将 Map<String, Set<Integer>> 转换为 Set<String> 格式 "sip:port"
                            Set<String> sipPortStringSet = new HashSet<>();
                            for (Map.Entry<String, Set<Integer>> entry : sIp_port_Set.entrySet()) {
                                String sip = entry.getKey();
                                for (Integer port : entry.getValue()) {
                                    sipPortStringSet.add(sip + ":" + port);
                                }
                            }
                            List<String> max3port_sip = WriteScanActAlarm.getMax3PortSip(sipPortStringSet);
                            Collection<String> max3port_sip_session_list = new HashSet<>();
                            for(String sip:max3port_sip){
                                max3port_sip_session_list.add(sip_session.get(sip));
                            }
                            Row resultRow = getLabelEdgeRow(dIp,ThreatTypeEnum.PORT_SCAN.getCode());//端口扫描
                            collector.collect(resultRow);
                            Row alarm_row =new Row(10);
                            alarm_row.setField(0,"扫描行为");
                            alarm_row.setField(1,"端口扫描dip");
                            alarm_row.setField(2,sIp_port_Set);
                            alarm_row.setField(3,dIp);
                            alarm_row.setField(4,portSet);
                            alarm_row.setField(5,ThreatTypeEnum.PORT_SCAN.getCode());
                            alarm_row.setField(6,max3port_sip_session_list);
                            alarm_row.setField(7,max3port_sip_session_list);
                            alarm_row.setField(8,iterable.iterator().next().getFieldAs(6));
                            alarm_row.setField(9,iterable.iterator().next().getFieldAs(7));
                            collector.collect(alarm_row);
                            logger.info("端口扫描dip告警",dIp);
                        }
                    }
                })
                .name("窗口函数获取端口扫描服务端IP标签").setParallelism(PARALLELISM_4);

        SingleOutputStreamOperator<Row> Port_Scan_LabelRow = Port_Scan_sIp_LabelRow.union(Port_Scan_dIp_LabelRow).process(new ProcessFunction<Row, Row>() {
            @Override
            public void processElement(Row row, ProcessFunction<Row, Row>.Context context, Collector<Row> collector) throws Exception {
                if (row.getField(1).equals(ThreatTypeEnum.PORT_SCAN.getCode())){
                    context.output(NebulaEdgeOutPutTag.Nebula_Port_Scan_Row,row);
                }else {
                    context.output(AlarmOutPutTag.Alarm_Port_Scan_Row,row);
                }
            }

        }).name("ES告警和Nebula打标分流").setParallelism(1);
        return Port_Scan_LabelRow;
    }

    public static SingleOutputStreamOperator<Row> dns_tunnelFunction(DataStream<Row> dns_tunnel_Row){
        long delay = 3000;//30秒
        int timeStampPosition = 2;
        MyWatermarkStrategy myWatermarkStrategy = new MyWatermarkStrategy();
        myWatermarkStrategy.setMyEventTimeTimeStamp(timeStampPosition);
        myWatermarkStrategy.setMyDelay(delay);
        DataStream<Row> dns_tunnel_RowWithTimeStamp = dns_tunnel_Row.assignTimestampsAndWatermarks(myWatermarkStrategy).name("生成事件时间戳和水印").setParallelism(2);//打上时间戳和水印都打

        SingleOutputStreamOperator<Row> dns_tunnel_LabelRow = dns_tunnel_RowWithTimeStamp.keyBy((KeySelector<Row, String>) row -> {
                    String dIp = row.getFieldAs(1);
                    return dIp;})
                .window(TumblingEventTimeWindows.of(Time.seconds(5)))//1h为窗口大小
                .reduce(new ReduceFunction<Row>() {
                    @Override
                    public Row reduce(Row row, Row t1) throws Exception {
                        Row dns_info_row = new Row(16);
                        int num1 = t1.getFieldAs(10);
                        int num2 = row.getFieldAs(10);
                        Set<String> sIp_set1 = row.getFieldAs(12);
                        Set<String> sIp_set2 = t1.getFieldAs(12);
                        Collection<String> SessionId_listA = row.getFieldAs(13);
                        Collection<String> SessionId_listB = t1.getFieldAs(13);
                        if(sIp_set1.size()<=10 && SessionId_listA.size()<=10){
                            sIp_set1.addAll(sIp_set2);
                            SessionId_listA.addAll(SessionId_listB);
                        }
                        List<Integer> ttl1 = row.getFieldAs(8);
                        List<Integer> ttl2 = t1.getFieldAs(8);
                        ttl1.set(0, ttl1.get(0)+ttl2.get(0));
                        ttl1.set(1, ttl1.get(1)+ttl2.get(1));
                        List<Integer> domain_ip1 = row.getFieldAs(9);
                        List<Integer> domain_ip2 = row.getFieldAs(9);
                        domain_ip1.set(0, domain_ip1.get(0)+domain_ip2.get(0));
                        domain_ip1.set(1, domain_ip1.get(1)+domain_ip2.get(1));
                        List<String> answer_name1 = row.getFieldAs(5);
                        List<String> answer_name2 = row.getFieldAs(5);
                        answer_name1.addAll(answer_name2);
                        List<String> answer_type1 = row.getFieldAs(6);
                        List<String> answer_type2 = row.getFieldAs(6);
                        answer_type1.addAll(answer_type2);
                        dns_info_row.setField(0,t1.getField(0));//"dns_tunnel_info"
                        dns_info_row.setField(1,t1.getField(1));//dip
                        dns_info_row.setField(2,t1.getField(2));//StartTime
                        dns_info_row.setField(3, add_map(t1.getFieldAs(3),row.getFieldAs(3)));//domain_map need map
                        dns_info_row.setField(4, add_map(t1.getFieldAs(4),row.getFieldAs(4)));//query_type need map
                        dns_info_row.setField(5,answer_name1);//answer_name not need map
                        dns_info_row.setField(6,answer_type1);//answer_type not need map
                        dns_info_row.setField(7, add_map(t1.getFieldAs(7),row.getFieldAs(7)));//answer_value need
                        dns_info_row.setField(8,ttl1);//answer_ttl:[总时长，总次数]
                        dns_info_row.setField(9,domain_ip1);//DomainIp:[""的个数(代表NXDomain应答状态码，域名不存在),domainIP总个数]
                        dns_info_row.setField(10,num1+num2);
                        dns_info_row.setField(11,get_new_time_info(t1.getFieldAs(11),row.getFieldAs(11)));
                        dns_info_row.setField(12,sIp_set1);//告警的少，普通的多，所以限制个数
                        dns_info_row.setField(13,SessionId_listA);
                        dns_info_row.setField(14,row.getFieldAs(14));
                        dns_info_row.setField(15,row.getFieldAs(15));
                        return dns_info_row;
                    }
                }).name("reduce函数聚合DNS隧道特征信息").setParallelism(PARALLELISM_4)
                .process(new ProcessFunction<Row, Row>() {
                    @Override
                    public void processElement(Row row, ProcessFunction<Row, Row>.Context context, Collector<Row> collector) throws Exception {
                        Map<String,Object> features = extractDnsTunnelFeatures(row);
//                        write_feature2csv.write_feature2csv(features);
//                        logger.info("****************写入feature文件一条数据****************");
                        Map<String,Double> dns_model_predict = PredictionService.predictDnsTunnel(features);
                        Double probability_1 = dns_model_predict.get("probability_1");
//                        logger.info("DNS隐蔽隧道告警——预测结果:{}——",probability_1);
                        if (probability_1 >= 0.8){
                            String dIp = row.getFieldAs(1);
                            Row resultRow = getLabelEdgeRow(dIp,ThreatTypeEnum.DNS_TUNN_SERVER.getCode());//DNS隧道服务器
                            collector.collect(resultRow);
                            Row dnsServerRow = getLabelEdgeRow(dIp,ThreatTypeEnum.DNS_LEG_SERVER.getCode());//非法DNS服务器
                            collector.collect(dnsServerRow);
                            Row alarm_row =new Row(9);
                            alarm_row.setField(0,"隐蔽隧道");
                            alarm_row.setField(1,"DNS隐蔽隧道");
                            alarm_row.setField(2,row.getField(12));//sIp_set
                            alarm_row.setField(3,dIp);
                            alarm_row.setField(4,probability_1);
                            alarm_row.setField(5,ThreatTypeEnum.DNS_TUNN_SERVER.getCode());
                            alarm_row.setField(6,row.getField(13));
                            alarm_row.setField(7,row.getField(14));
                            alarm_row.setField(8,row.getField(15));
                            collector.collect(alarm_row);
                            // 会话打标
                            Row SessionLabelRow = new Row(4);
                            SessionLabelRow.setField(0,"会话打标");
                            Set<String> sessionList = row.getFieldAs(13);
                            String sessionId = sessionList.iterator().next();
                            SessionLabelRow.setField(1, sessionId);
                            Set<String> conncetLabels = new HashSet<>();
                            conncetLabels.add(ThreatTypeEnum.TUNN_DNS.getCode());
                            // 伪造DNS协议打标
                            conncetLabels.add(ThreatTypeEnum.SPOOF_DNS.getCode());
                            SessionLabelRow.setField(2,conncetLabels);
                            SessionLabelRow.setField(3,row.getFieldAs(14));
                            collector.collect(SessionLabelRow);
                            logger.info("DNS隐蔽隧道告警——dip:{}——",dIp);
                        }else{
                            String dIp = row.getFieldAs(1);
                            Row dnsServerRow = getLabelEdgeRow(dIp,ThreatTypeEnum.DNS_ILLEG_SERVER.getCode());//合法DNS服务器
                            collector.collect(dnsServerRow);
                            logger.info("DNS合法服务器告警——dip:{}——",row.getField(1));
                        }
                    }
                })
                .name("窗口函数获取DNS隧道服务器IP标签").setParallelism(PARALLELISM_4)
                .process(new ProcessFunction<Row, Row>() {
                    @Override
                    public void processElement(Row row, ProcessFunction<Row, Row>.Context context, Collector<Row> collector) throws Exception {
                        if ("会话打标".equals(row.getFieldAs(0))){
                            context.output(SessionOutPutTag.Session_DNS_Tunnel_info,row);
                        }else if (row.getField(1).equals(ThreatTypeEnum.DNS_TUNN_SERVER.getCode())){
//                            logger.info("DNS隧道服务器分流");
                            context.output(NebulaEdgeOutPutTag.Nebula_DNS_Tunnel_Row,row);
                        }else if (row.getField(1).equals(ThreatTypeEnum.DNS_LEG_SERVER.getCode())){
//                            logger.info("非法DNS服务器分流");
                            context.output(NebulaEdgeOutPutTag.Nebula_DNS_Server_Row,row);
                        }else if (row.getField(1).equals(ThreatTypeEnum.DNS_ILLEG_SERVER.getCode())){
//                            logger.info("合法DNS服务器分流");
                            context.output(NebulaEdgeOutPutTag.Nebula_DNS_LeServer_Row,row);
                        }else {
                            context.output(AlarmOutPutTag.Alarm_DNS_Tunnel_Row,row);
                        }
                    }
                }).name("ES告警和Nebula打标分流").setParallelism(1);


        return dns_tunnel_LabelRow;
   }
    public static SingleOutputStreamOperator<Row> todeskReduceFunction(DataStream<Row> ToDeskRow){
        long delay = 3000;//30秒
        int timeStampPosition = 3;
        MyWatermarkStrategy myWatermarkStrategy = new MyWatermarkStrategy();
        myWatermarkStrategy.setMyEventTimeTimeStamp(timeStampPosition);
        myWatermarkStrategy.setMyDelay(delay);
        DataStream<Row> ToDeskRowWithTimeStamp = ToDeskRow.assignTimestampsAndWatermarks(myWatermarkStrategy).name("生成事件时间戳和水印").setParallelism(2);//打上时间戳和水印都打

        SingleOutputStreamOperator<Row> toDesk_LabelRow = ToDeskRowWithTimeStamp.keyBy((KeySelector<Row, String>) row -> {
                    String sIp = row.getFieldAs(1);
                    return sIp;})
                .window(TumblingEventTimeWindows.of(Time.seconds(60)))//1m为窗口大小
                .reduce(new ReduceFunction<Row>() {
                    @Override
                    public Row reduce(Row row, Row t1) throws Exception {
                        Row ToDeskRow = new Row(9);
                        boolean isToDesk1 = row.getFieldAs(4);
                        boolean isSTUN1 = row.getFieldAs(5);
                        boolean isToDesk2 = t1.getFieldAs(4);
                        boolean isSTUN2 = t1.getFieldAs(5);
                        String SessionId = row.getFieldAs(6);
                        if (isToDesk2){
                            SessionId = t1.getFieldAs(6);
                        }
                        ToDeskRow.setField(0,t1.getField(0));//"ToDeskRow"
                        ToDeskRow.setField(1,t1.getField(1));//sIp
                        ToDeskRow.setField(2,t1.getField(2));//dIp
                        ToDeskRow.setField(3,t1.getFieldAs(3));//start time
                        ToDeskRow.setField(4,isToDesk1 || isToDesk2);//query_type need map
                        ToDeskRow.setField(5,isSTUN1 || isSTUN2);//answer_name not need map
                        ToDeskRow.setField(6,SessionId);//sessionid
                        ToDeskRow.setField(7,row.getFieldAs(7));//EsKey
                        ToDeskRow.setField(8,row.getFieldAs(8));

                        return ToDeskRow;
                    }
                }).name("reduce函数聚合ToDesk特征信息").setParallelism(PARALLELISM_4)
                .process(new ProcessFunction<Row, Row>() {
                    @Override
                    public void processElement(Row row, ProcessFunction<Row, Row>.Context context, Collector<Row> collector) throws Exception {
                        boolean isToDesk = row.getFieldAs(4);
                        boolean isSTUN = row.getFieldAs(5);
                        if (isToDesk && !isSTUN) {
                            Row SessionLabelRow = new Row(4);
                            SessionLabelRow.setField(0, "会话打标");
                            SessionLabelRow.setField(1, row.getFieldAs(6));
                            SessionLabelRow.setField(2, Collections.singleton(ThreatTypeEnum.TDJJ.getCode()));
                            SessionLabelRow.setField(3, row.getFieldAs(7));
                            collector.collect(SessionLabelRow);
                            logger.info("ToDesk极简版会话打标");
                        }
                        if (isToDesk && isSTUN) {
                            Row SessionLabelRow = new Row(4);
                            SessionLabelRow.setField(0, "会话打标");
                            SessionLabelRow.setField(1, row.getFieldAs(6));
                            SessionLabelRow.setField(2, Collections.singleton(ThreatTypeEnum.TDXZK.getCode()));
                            SessionLabelRow.setField(3, row.getFieldAs(7));
                            collector.collect(SessionLabelRow);
                            logger.info("ToDesk正式版会话打标");
                        }
                    }
                })
                .name("窗口函数识别ToDesk远控行为").setParallelism(PARALLELISM_4);


        return toDesk_LabelRow;
    }

    public static Map<Object,Integer> add_map_merge(Map<Object,Integer> mapA, Map<Object,Integer> mapB){
        Map<Object,Integer> map_tmp = new HashMap<>(mapB);
        mapA.forEach((key,value)->map_tmp.merge(key,value, Integer::sum));
        return map_tmp;
    }

    public static Map<Object,Integer> add_map(Map<Object, Integer> mapA,Map<Object,Integer> mapB){//B大A小
        Set<Object> key_b_set = mapB.keySet();
        Set<Object> key_a_set = mapA.keySet();
        for (Object key_a: key_a_set){
            if (key_b_set.contains(key_a)){
                mapB.put(key_a,mapA.get(key_a)+mapB.get(key_a));
            }else{
                mapB.put(key_a,mapA.get(key_a));
            }
        }
        return mapB;
    }

    public static Map<String,Integer> get_new_time_info(Map<String,Integer> mapA,Map<String,Integer> mapB){
        Map<String,Integer> new_time_info = new HashMap<>();
        new_time_info.put("min_Start_time", Math.min(mapA.get("min_Start_time"),mapB.get("min_Start_time")));
        new_time_info.put("Max_Start_time", Math.max(mapA.get("Max_Start_time"),mapB.get("Max_Start_time")));
        return new_time_info;
    }

    /**
     * 提取DNS隧道特征
     *
     * @param row DNS数据行
     * @return 特征映射
     */
    private static Map<String, Object> extractDnsTunnelFeatures(Row row) {
        Map<String, Object> features = new HashMap<>();

        // 基本特征提取
        features.put("domain_count", ((Map<?, ?>) row.getField(3)).size());
        features.put("query_type_count", ((Map<?, ?>) row.getField(4)).size());
        features.put("answer_name_count", ((List<?>) row.getField(5)).size());
        features.put("answer_type_count", ((List<?>) row.getField(6)).size());
        features.put("answer_value_count", ((Map<?, ?>) row.getField(7)).size());

        List<Integer> ttlInfo = row.getFieldAs(8);
        features.put("ttl_total", ttlInfo.get(0));
        features.put("ttl_count", ttlInfo.get(1));
        features.put("ttl_avg", ttlInfo.get(1) > 0 ? (double) ttlInfo.get(0) / ttlInfo.get(1) : 0.0);

        List<Integer> domainIpInfo = row.getFieldAs(9);
        features.put("nxdomain_count", domainIpInfo.get(0));
        features.put("domain_ip_count", domainIpInfo.get(1));

        features.put("total_queries", row.getFieldAs(10));
        features.put("source_ip_count", ((Set<?>) row.getField(12)).size());

        return features;
    }
}
