package com.geeksec.threatdetector.model.event.ssl;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.extern.slf4j.Slf4j;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * X509 证书信息。
 *
 * @param cert 原始证书字节数组
 * @param certId 证书ID
 * @param subject 主题信息
 * @param Version 版本号
 * @param issuer 颁发者信息
 * @param NotBefore 有效期起始
 * @param NotAfter 有效期结束
 * @param Duration 有效期时长 (可能为秒)
 * @param SerialNumber 序列号
 * @param CN Common Name
 * @param ASN1MD5 ASN1编码的MD5哈希
 * @param ASN1SHA1 ASN1编码的SHA1哈希
 * <AUTHOR>
 */
@Slf4j
public record X509Cert(
    @JSONField(name = "Cert") byte[] cert,
    @JSONField(name = "CertID") String certId,
    @JSONField(name = "Subject") LinkedHashMap<String, String> subject,
    @JSONField(name = "Version") int Version,
    @JSONField(name = "Issuer") LinkedHashMap<String, String> issuer,
    @JSONField(name = "NotBefore") String NotBefore,
    @JSONField(name = "NotAfter") String NotAfter,
    @JSONField(name = "Duration") Long Duration,
    @JSONField(name = "SerialNumber") String SerialNumber,
    @JSONField(name = "CN") Object CN,
    @JSONField(name = "ASN1MD5") String ASN1MD5,
    @JSONField(name = "ASN1SHA1") String ASN1SHA1
) {

    /**
     * 使用证书的原始字节数组构造 X509Cert 对象，其他字段使用默认值。
     *
     * @param cert 证书的原始字节数组
     */
    public X509Cert(byte[] cert) {
        this(
            cert,          // cert
            null,          // certId
            null,          // subject
            0,             // Version (default for int)
            null,          // issuer
            null,          // NotBefore
            null,          // NotAfter
            0L,            // Duration
            null,          // SerialNumber
            null,          // CN
            null,          // ASN1MD5
            null           // ASN1SHA1
        );
    }

    /**
     * 紧凑构造函数，确保集合的不可变性。
     * 对于 byte[] cert, Record 默认提供浅拷贝，如果需要深拷贝以保证完全不可变性，
     * 需要在此处或规范构造函数中进行 clone 操作，并在访问器中也进行 clone。
     * 当前保持 Record 的默认行为。
     */
    public X509Cert {
        // 如果传入的 subject 不为 null，则创建一个不可变的副本
        if (subject != null) {
            this.subject = new LinkedHashMap<>(Map.copyOf(subject)); // Map.copyOf 返回的是不可修改Map，如果需要LinkedHashMap特性，需要再包装
        } else {
            this.subject = null; // 保持 null
        }
        // 如果传入的 issuer 不为 null，则创建一个不可变的副本
        if (issuer != null) {
            this.issuer = new LinkedHashMap<>(Map.copyOf(issuer)); // Map.copyOf 返回的是不可修改Map
        } else {
            this.issuer = null; // 保持 null
        }
        // 对于 byte[] cert，如果需要深拷贝以保证完全不可变性:
        // this.cert = (cert == null) ? null : cert.clone();
        // 当前版本遵循Record的默认浅拷贝行为 for arrays.
    }

    // @JSONField 注解已直接应用于 Record 组件，FastJSON 应能识别它们。
    // Record 会自动生成所有组件的 public accessor 方法 (例如 cert(), certId(), subject() 等)。
    // 以及 equals(), hashCode(), 和 toString() 方法。
}
