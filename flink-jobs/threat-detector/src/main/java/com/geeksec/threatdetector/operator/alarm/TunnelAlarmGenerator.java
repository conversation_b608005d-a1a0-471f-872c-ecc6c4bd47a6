package com.geeksec.threatdetector.operator.alarm;

import com.geeksec.threatdetector.model.event.alarm.AlarmEvent;
import com.geeksec.threatdetector.model.event.protocol.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.streaming.api.datastream.DataStream;

import java.util.*;

/**
 * 隧道通信告警生成器
 * 负责将隧道通信检测事件转换为告警事件
 *
 * <AUTHOR>
 * @since 2023/01/01
 */
@Slf4j
public class TunnelAlarmGenerator extends AbstractThreatAlarmGenerator<ProtocolEvent> {


    private static final String ALERT_TYPE = "TUNNEL_DETECTED";
    private static final String ALERT_NAME = "隧道通信检测";
    private static final String ALERT_LEVEL = "HIGH";

    // 隧道类型
    public enum TunnelType {
        HTTP("HTTP隧道"),
        SSL("SSL隧道"),
        TCP("TCP隧道"),
        DNS("DNS隧道"),
        NTP("NTP隧道"),
        ICMP("ICMP隧道"),
        UNKNOWN("未知隧道");

        private final String description;

        TunnelType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    private final TunnelType tunnelType;

    /**
     * 构造函数
     *
     * @param tunnelType 隧道类型
     */
    public TunnelAlarmGenerator(TunnelType tunnelType) {
        this.tunnelType = tunnelType;
    }

    @Override
    protected boolean detectThreat(ProtocolEvent event) {
        // 根据隧道类型和事件类型检查是否需要生成告警
        switch (tunnelType) {
            case HTTP:
                return isHttpTunnel(event);
            case SSL:
                return isSslTunnel(event);
            case TCP:
                return isTcpTunnel(event);
            case DNS:
                return isDnsTunnel(event);
            case NTP:
                return isNtpTunnel(event);
            case ICMP:
                // return isIcmpTunnel(event); // IcmpEvent not found
                return false;
            default:
                return false;
        }
    }

    @Override
    protected AlarmEvent convertToAlarmEvent(ProtocolEvent event) {
        // 创建告警描述和解决方案
        String description = String.format("检测到%s通信行为，可能存在数据泄露或远程控制风险",
                tunnelType.getDescription());

        String solution = "1. 立即阻断相关IP之间的通信\n" +
                "2. 检查源主机是否存在恶意软件或未授权程序\n" +
                "3. 分析隧道通信内容，确定是否存在数据泄露\n" +
                "4. 加强网络边界防护，限制非标准协议通信\n" +
                "5. 部署深度包检测设备，识别和阻断隧道通信";

        // 创建基础告警事件
        AlarmEvent alertEvent = createBaseAlertEvent(
                ALERT_TYPE + "_" + tunnelType.name(),
                ALERT_NAME + " - " + tunnelType.getDescription(),
                ALERT_LEVEL,
                description,
                solution,
                event.getSourceIp(),
                event.getSourcePort(),
                event.getDestinationIp(),
                event.getDestinationPort()
        );

        // 添加详细信息
        Map<String, Object> details = alertEvent.getDetails();
        details.put("tunnelType", tunnelType.name());
        details.put("tunnelDescription", tunnelType.getDescription());
        details.put("protocol", event.getProtocol());
        details.put("sessionId", event.getSessionId());

        // 添加隧道特征信息
        Map<String, Object> tunnelFeatures = extractTunnelFeatures(event);
        details.putAll(tunnelFeatures);

        // 添加攻击家族信息
        List<Map<String, String>> attackFamily = new ArrayList<>();
        Map<String, String> family = new HashMap<>();
        family.put("key", "隧道通信");
        family.put("value", tunnelType.getDescription());
        attackFamily.add(family);
        details.put("attack_family", attackFamily);

        // 添加攻击目标信息
        List<Map<String, Object>> targets = new ArrayList<>();
        Map<String, Object> target = new HashMap<>();
        target.put("target_type", "网络服务器");
        target.put("target_ip", event.getDestinationIp());
        targets.add(target);
        details.put("targets", targets);

        return alertEvent;
    }

    /**
     * 提取隧道特征信息
     */
    private Map<String, Object> extractTunnelFeatures(ProtocolEvent event) {
        Map<String, Object> features = new HashMap<>();

        // 根据隧道类型提取特征
        switch (tunnelType) {
            case HTTP:
                extractHttpTunnelFeatures(event, features);
                break;
            case SSL:
                extractSslTunnelFeatures(event, features);
                break;
            case TCP:
                extractTcpTunnelFeatures(event, features);
                break;
            case DNS:
                extractDnsTunnelFeatures(event, features);
                break;
            case NTP:
                extractNtpTunnelFeatures(event, features);
                break;
            case ICMP:
                // extractIcmpTunnelFeatures(event, features); // IcmpEvent not found
                break;
            default:
                break;
        }

        return features;
    }

    /**
     * 提取HTTP隧道特征
     */
    private void extractHttpTunnelFeatures(ProtocolEvent event, Map<String, Object> features) {
        // 如果事件是HTTP事件，提取HTTP相关特征
        if (event instanceof HttpEvent) {
            HttpEvent httpEvent = (HttpEvent) event;

            features.put("httpMethod", httpEvent.getMethod());
            features.put("httpUri", httpEvent.getUri());
            features.put("httpHost", httpEvent.getHost());
            features.put("httpUserAgent", httpEvent.getUserAgent());
            features.put("httpContentType", httpEvent.getContentType());
            features.put("httpReferer", httpEvent.getReferer());
            features.put("httpCookie", httpEvent.getCookie());

            // 检查是否包含Base64编码数据
            String payload = httpEvent.getPayload();
            if (payload != null && !payload.isEmpty()) {
                features.put("containsBase64Data", isBase64Encoded(payload));
                features.put("payloadLength", payload.length());
            }
        }
    }

    /**
     * 提取SSL隧道特征
     */
    private void extractSslTunnelFeatures(ProtocolEvent event, Map<String, Object> features) {
        // 如果事件是SSL事件，提取SSL相关特征
        if (event instanceof SslEvent) {
            SslEvent sslEvent = (SslEvent) event;

            features.put("sslServerName", sslEvent.getServerName());
            features.put("sslCipher", sslEvent.getCipher());
            features.put("sslVersion", sslEvent.getVersion());
            features.put("sslJa3", sslEvent.getJa3());
            features.put("sslJa3s", sslEvent.getJa3s());

            // 检查是否为自签名证书
            if (sslEvent.getIssuer() != null && sslEvent.getIssuer().equals(sslEvent.getSubject())) {
                features.put("isSelfSignedCertificate", true);
            }
        }
    }

    /**
     * 提取TCP隧道特征
     */
    private void extractTcpTunnelFeatures(ProtocolEvent event, Map<String, Object> features) {
        // 检查是否使用非标准端口
        if (isNonStandardPort(event.getDestinationPort())) {
            features.put("isNonStandardPort", true);
        }

        // 检查TCP标志位
        if (event instanceof TcpEvent) {
            TcpEvent tcpEvent = (TcpEvent) event;
            features.put("tcpFlagsSyn", tcpEvent.isSyn());
            features.put("tcpFlagsAck", tcpEvent.isAck());
            features.put("tcpFlagsFin", tcpEvent.isFin());
            features.put("tcpFlagsRst", tcpEvent.isRst());
            features.put("tcpFlagsPsh", tcpEvent.isPsh());
            features.put("tcpFlagsUrg", tcpEvent.isUrg());
        }
    }

    /**
     * 提取DNS隧道特征
     */
    private void extractDnsTunnelFeatures(ProtocolEvent event, Map<String, Object> features) {
        // 如果事件是DNS事件，提取DNS相关特征
        if (event instanceof DnsEvent) {
            DnsEvent dnsEvent = (DnsEvent) event;

            features.put("dnsQueryName", dnsEvent.getQueryName());
            features.put("dnsQueryType", dnsEvent.getQueryType());
            features.put("dnsRcodeName", dnsEvent.getRcodeName());

            // 检查是否包含异常长度的域名
            String queryName = dnsEvent.getQueryName();
            if (queryName != null && queryName.length() > 50) {
                features.put("isLongQueryName", true);
            }

            // 检查是否包含Base64编码数据
            if (queryName != null && isBase64Encoded(queryName)) {
                features.put("containsBase64QueryName", true);
            }

            // 检查是否包含大量子域名
            if (queryName != null && queryName.split("\\.").length > 5) {
                features.put("hasManySubdomains", true);
            }
        }
    }

    /**
     * 提取NTP隧道特征
     */
    private void extractNtpTunnelFeatures(ProtocolEvent event, Map<String, Object> features) {
        // 简单记录NTP协议和端口
        features.put("ntpProtocol", event.getProtocol());
        features.put("ntpDestinationPort", event.getDestinationPort());
    }

    // /**
// * 提取ICMP隧道特征
// */
// private void extractIcmpTunnelFeatures(ProtocolEvent event, Map<String, Object> features) {
//    // 简单记录ICMP协议
//    features.put("icmpProtocol", event.getProtocol());
//
//    // 如果事件是ICMP事件，可以提取更多ICMP特定信息
//    if (event instanceof IcmpEvent) {
//        IcmpEvent icmpEvent = (IcmpEvent) event;
//        features.put("icmpType", icmpEvent.getType());
//        features.put("icmpCode", icmpEvent.getCode());
//        features.put("icmpPayloadLength", icmpEvent.getPayload() != null ? icmpEvent.getPayload().length() : 0);
//    }
// }

    /**
     * 检查是否为HTTP隧道
     */
    private boolean isHttpTunnel(ProtocolEvent event) {
        // 检查是否使用非标准端口
        if (isNonStandardPort(event.getDestinationPort())) {
            return true;
        }

        // 如果事件是HTTP事件，检查是否包含隧道特征
        if (event instanceof HttpEvent) {
            HttpEvent httpEvent = (HttpEvent) event;

            // 检查是否包含CONNECT方法
            if ("CONNECT".equalsIgnoreCase(httpEvent.getMethod())) {
                return true;
            }

            // 检查是否包含Base64编码数据
            String payload = httpEvent.getPayload();
            if (payload != null && isBase64Encoded(payload)) {
                return true;
            }
        }

        return false;
    }

    /**
    * 检查是否为SSL隧道
    */
private boolean isSslTunnel(ProtocolEvent event) {
    // 检查是否使用非标准端口
    if (isNonStandardPort(event.getDestinationPort())) {
    return true;
    }

    if (event instanceof SslEvent) {
        SslEvent sslEvent = (SslEvent) event;
    // 检查是否存在已知的SSL/TLS隧道特征
    // 检查空的加密套件
    if (sslEvent.getCipherSuite() != null && sslEvent.getCipherSuite().toLowerCase().contains("null")) {
        // logger.debug("SSL Tunnel detected: Null cipher suite for event {}", event.getSessionId());
            return true;
        }
        // 检查SNI域名是否为 .onion (Tor)
        if (sslEvent.getSniDomain() != null && sslEvent.getSniDomain().matches("(?i).*\\.onion($|\\.)")) {
            // logger.debug("SSL Tunnel detected: Tor .onion SNI for event {}", event.getSessionId());
            return true;
        }
        // 检查是否为自签名证书 (issuer 和 subject 相同) - 这可能过于宽泛，需要谨慎使用或添加更具体的检查
        /*
        Map<String, String> cert = sslEvent.getCertificate();
        if (cert != null) {
            String issuer = cert.get("issuer");
            String subject = cert.get("subject");
            if (issuer != null && subject != null && issuer.equals(subject)) {
                // logger.debug("SSL Tunnel detected: Self-signed certificate pattern for event {}", event.getSessionId());
                // return true;
            }
        }
        */
    }
    return false;
}

    /**
    * 检查是否为TCP隧道
    */
private boolean isTcpTunnel(ProtocolEvent event) {
    // 检查是否使用非标准端口
    if (isNonStandardPort(event.getDestinationPort())) {
    return true;
    }

    if (event instanceof TcpEvent) {
        TcpEvent tcpEvent = (TcpEvent) event;
        // 检查是否存在已知的TCP隧道特征
        // 例如，检测特定端口上的SYN标志 (这是一个非常基础的检查，可能需要更多上下文)
        if (tcpEvent.getFlags() != null && tcpEvent.getFlags().toUpperCase().contains("SYN")) {
            Integer destPort = tcpEvent.getDestinationPort();
            // 示例：到常见隧道/代理端口的SYN可能可疑
            if (destPort != null && (destPort == 8080 || destPort == 4433 || destPort == 5353 || destPort == 22 || destPort == 443)) {
                // 这是一个非常宽泛的条件，请考虑添加更多检查，如负载大小、连接持续时间等。
                // logger.debug("Potential TCP Tunnel detected: SYN to port {} for event {}", destPort, event.getSessionId());
                // return true; // 谨慎启用此选项，可能过于通用
            }
        }
        // 进一步的检查可能涉及分析负载模式、连接持续时间、字节计数等。
    }
    return false;
}

    /**
     * 检查是否为DNS隧道
     */
    private boolean isDnsTunnel(ProtocolEvent event) {
        // 如果事件是DNS事件，检查是否包含DNS隧道特征
        if (event instanceof DnsEvent) {
            DnsEvent dnsEvent = (DnsEvent) event;

            // 检查查询名称
            String queryName = dnsEvent.getQueryName();
            if (queryName == null || queryName.isEmpty()) {
                return false;
            }

            // 检查是否包含异常长度的域名
            if (queryName.length() > 50) {
                return true;
            }

            // 检查是否包含Base64编码数据
            if (isBase64Encoded(queryName)) {
                return true;
            }

            // 检查是否包含大量子域名
            if (queryName.split("\\.").length > 5) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查是否为NTP隧道
     */
    private boolean isNtpTunnel(ProtocolEvent event) {
        // 简单检查是否为NTP协议且目标端口为123
        return "NTP".equals(event.getProtocol()) && event.getDestinationPort() != null && event.getDestinationPort() == 123;
    }

    // /**
// * 检查是否为ICMP隧道
// */
// private boolean isIcmpTunnel(ProtocolEvent event) {
//    // 简单检查是否为ICMP协议
//    return "ICMP".equals(event.getProtocol());
// }

    /**
     * 检查是否为非标准端口
     */
    private boolean isNonStandardPort(Integer port) {
        if (port == null) {
            return false;
        }

        // 常见标准端口列表
        List<Integer> standardPorts = Arrays.asList(
                21, 22, 23, 25, 53, 80, 110, 119, 123, 143, 161, 162, 389, 443, 465, 587, 636, 989, 990, 993, 995, 1433, 1521, 3306, 3389, 5432, 8080, 8443
        );

        return !standardPorts.contains(port);
    }

    /**
     * 检查字符串是否为Base64编码
     */
    private boolean isBase64Encoded(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }

        // Base64编码的特征
        String base64Pattern = "^[A-Za-z0-9+/]*={0,2}$";
        return str.matches(base64Pattern) && str.length() % 4 == 0;
    }

    /**
     * 创建隧道告警生成数据流
     */
    public static DataStream<AlarmEvent> generateTunnelAlarms(DataStream<ProtocolEvent> events, TunnelType tunnelType) {
        return events
                .flatMap(new TunnelAlarmGenerator(tunnelType)) // Corrected to TunnelAlarmGenerator
                .name(tunnelType.getDescription() + "告警生成")
                .setParallelism(2);
    }
}
