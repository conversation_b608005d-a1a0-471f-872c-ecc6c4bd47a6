package com.geeksec.threatdetector.operator.analysis.encrypted;

import com.geeksec.threatdetector.model.event.alarm.AlarmEvent;
import com.geeksec.threatdetector.model.event.protocol.ProtocolEvent;
import com.geeksec.threatdetector.model.event.protocol.SslEvent;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 加密工具检测函数
 *
 * <AUTHOR>
 */
/**
 * 加密工具检测器
 * 用于检测各种加密工具和框架的流量特征
 *
 * <AUTHOR>
 * @since 2023/01/01
 */
public class EncryptedToolDetector extends RichFlatMapFunction<ProtocolEvent, AlarmEvent> {
    
    private static final Logger logger = LoggerFactory.getLogger(EncryptedToolDetector.class);
    private static final String ENCRYPTED_TOOL_ALERT_TYPE = "ENCRYPTED_TOOL_DETECTED";
    private static final String ENCRYPTED_TOOL_ALERT_NAME = "加密工具检测";
    private static final String ENCRYPTED_TOOL_ALERT_LEVEL = "HIGH";
    
    @Override
    public void flatMap(ProtocolEvent event, Collector<AlarmEvent> out) {
        try {
            // 只处理SSL事件
            if (!(event instanceof SslEvent)) {
                return;
            }
            
            SslEvent sslEvent = (SslEvent) event;
            
            // 检测各种加密工具
            if (isMetasploit(sslEvent)) {
                AlarmEvent alert = createAlertEvent(sslEvent, "METASPLOIT", "Metasploit框架检测");
                out.collect(alert);
                logger.warn("检测到Metasploit工具: {}:{} -> {}:{}", 
                        sslEvent.getSourceIp(), sslEvent.getSourcePort(),
                        sslEvent.getDestinationIp(), sslEvent.getDestinationPort());
            }
            
            if (isCobaltStrike(sslEvent)) {
                AlarmEvent alert = createAlertEvent(sslEvent, "COBALT_STRIKE", "CobaltStrike框架检测");
                out.collect(alert);
                logger.warn("检测到CobaltStrike工具: {}:{} -> {}:{}", 
                        sslEvent.getSourceIp(), sslEvent.getSourcePort(),
                        sslEvent.getDestinationIp(), sslEvent.getDestinationPort());
            }
            
            if (isEmpire(sslEvent)) {
                AlarmEvent alert = createAlertEvent(sslEvent, "EMPIRE", "Empire框架检测");
                out.collect(alert);
                logger.warn("检测到Empire工具: {}:{} -> {}:{}", 
                        sslEvent.getSourceIp(), sslEvent.getSourcePort(),
                        sslEvent.getDestinationIp(), sslEvent.getDestinationPort());
            }
            
            if (isQuasar(sslEvent)) {
                AlarmEvent alert = createAlertEvent(sslEvent, "QUASAR", "Quasar RAT检测");
                out.collect(alert);
                logger.warn("检测到Quasar RAT工具: {}:{} -> {}:{}", 
                        sslEvent.getSourceIp(), sslEvent.getSourcePort(),
                        sslEvent.getDestinationIp(), sslEvent.getDestinationPort());
            }
            
            if (isRemcos(sslEvent)) {
                AlarmEvent alert = createAlertEvent(sslEvent, "REMCOS", "Remcos RAT检测");
                out.collect(alert);
                logger.warn("检测到Remcos RAT工具: {}:{} -> {}:{}", 
                        sslEvent.getSourceIp(), sslEvent.getSourcePort(),
                        sslEvent.getDestinationIp(), sslEvent.getDestinationPort());
            }
            
            if (isMerlin(sslEvent)) {
                AlarmEvent alert = createAlertEvent(sslEvent, "MERLIN", "Merlin C2检测");
                out.collect(alert);
                logger.warn("检测到Merlin C2工具: {}:{} -> {}:{}", 
                        sslEvent.getSourceIp(), sslEvent.getSourcePort(),
                        sslEvent.getDestinationIp(), sslEvent.getDestinationPort());
            }
            
            if (isPyFud(sslEvent)) {
                AlarmEvent alert = createAlertEvent(sslEvent, "PYFUD", "PyFud检测");
                out.collect(alert);
                logger.warn("检测到PyFud工具: {}:{} -> {}:{}", 
                        sslEvent.getSourceIp(), sslEvent.getSourcePort(),
                        sslEvent.getDestinationIp(), sslEvent.getDestinationPort());
            }
            
            if (isGhost(sslEvent)) {
                AlarmEvent alert = createAlertEvent(sslEvent, "GHOST", "Ghost框架检测");
                out.collect(alert);
                logger.warn("检测到Ghost框架工具: {}:{} -> {}:{}", 
                        sslEvent.getSourceIp(), sslEvent.getSourcePort(),
                        sslEvent.getDestinationIp(), sslEvent.getDestinationPort());
            }
            
            if (isNanoCore(sslEvent)) {
                AlarmEvent alert = createAlertEvent(sslEvent, "NANOCORE", "NanoCore RAT检测");
                out.collect(alert);
                logger.warn("检测到NanoCore RAT工具: {}:{} -> {}:{}", 
                        sslEvent.getSourceIp(), sslEvent.getSourcePort(),
                        sslEvent.getDestinationIp(), sslEvent.getDestinationPort());
            }
            
            if (isTHCSSLDOS(sslEvent)) {
                AlarmEvent alert = createAlertEvent(sslEvent, "THC_SSL_DOS", "THC-SSL-DOS检测");
                out.collect(alert);
                logger.warn("检测到THC-SSL-DOS工具: {}:{} -> {}:{}", 
                        sslEvent.getSourceIp(), sslEvent.getSourcePort(),
                        sslEvent.getDestinationIp(), sslEvent.getDestinationPort());
            }
            
        } catch (Exception e) {
            logger.error("加密工具检测错误: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 检测是否为Metasploit框架
     */
    private boolean isMetasploit(SslEvent sslEvent) {
        Map<String, String> certificate = sslEvent.getCertificate();
        if (certificate == null) {
            return false;
        }
        
        // 检查证书字段
        String country = certificate.get("country");
        String state = certificate.get("state");
        String organization = certificate.get("organization");
        String organizationalUnit = certificate.get("organizationalUnit");
        
        // 检查国家代码是否为 "US"
        if (!"US".equals(country)) {
            return false;
        }
        
        // 检查州代码是否在预定义的列表中
        if (!isInFakeStateList(state)) {
            return false;
        }
        
        // 检查组织单位是否在预定义的列表中
        if (!isInFakeOUList(organizationalUnit)) {
            return false;
        }
        
        // 检查组织名称是否符合要求
        return checkMetasploitOrganization(organization);
    }
    
    /**
     * 检查组织名称是否符合Metasploit特征
     */
    private boolean checkMetasploitOrganization(String organization) {
        if (organization == null) {
            return false;
        }
        
        // 检查组织名称是否以预定义的后缀结束
        String[] fakeSuffixes = {"Inc.", "Corp.", "LLC", "Ltd.", "Co."};
        for (String suffix : fakeSuffixes) {
            if (organization.trim().endsWith(suffix)) {
                // 检查组织名称是否包含预定义的姓氏
                String[] parts = organization.split(",| |and ");
                for (String part : parts) {
                    if (isInFakeLastNameList(part.trim())) {
                        return true;
                    }
                }
            }
        }
        return false;
    }
    
    /**
     * 检查州名是否在伪造州名列表中
     */
    private boolean isInFakeStateList(String state) {
        if (state == null) {
            return false;
        }
        
        String[] fakeStates = {"CA", "NY", "TX", "FL", "IL", "PA", "OH", "MI", "GA", "NC"};
        for (String fakeState : fakeStates) {
            if (fakeState.equals(state)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 检查组织单位是否在伪造组织单位列表中
     */
    private boolean isInFakeOUList(String ou) {
        if (ou == null) {
            return false;
        }
        
        String[] fakeOUs = {"IT", "Security", "Operations", "Development", "Engineering"};
        for (String fakeOU : fakeOUs) {
            if (fakeOU.equals(ou)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 检查姓氏是否在伪造姓氏列表中
     */
    private boolean isInFakeLastNameList(String lastName) {
        if (lastName == null) {
            return false;
        }
        
        String[] fakeLastNames = {"Smith", "Johnson", "Williams", "Jones", "Brown", "Davis", "Miller", "Wilson"};
        for (String fakeName : fakeLastNames) {
            if (fakeName.equals(lastName)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 检测是否为CobaltStrike框架
     */
    private boolean isCobaltStrike(SslEvent sslEvent) {
        Map<String, String> certificate = sslEvent.getCertificate();
        if (certificate == null) {
            return false;
        }
        
        // 检查证书序列号
        String serialNumber = certificate.get("serialNumber");
        if (!"8bb00ee".equals(serialNumber)) {
            return false;
        }
        
        // 检查证书其他字段是否为空
        String country = certificate.get("country");
        String state = certificate.get("state");
        String organization = certificate.get("organization");
        String organizationalUnit = certificate.get("organizationalUnit");
        String commonName = certificate.get("commonName");
        String locality = certificate.get("locality");
        
        return "".equals(country) && "".equals(state) && "".equals(organization)
                && "".equals(organizationalUnit) && "".equals(commonName) && "".equals(locality);
    }
    
    /**
     * 检测是否为Empire框架
     */
    private boolean isEmpire(SslEvent sslEvent) {
        Map<String, String> certificate = sslEvent.getCertificate();
        if (certificate == null) {
            return false;
        }
        
        // 检查证书序列号
        String serialNumber = certificate.get("serialNumber");
        if (!"8ac729b92c4d6af64225faa43ebf9612238bc97".equals(serialNumber)) {
            return false;
        }
        
        // 检查指纹特征
        String clientFingerprint = sslEvent.getClientFingerprint();
        String serverFingerprint = sslEvent.getServerFingerprint();
        
        return "4418072496022778490".equals(clientFingerprint) && "8829655996777896182".equals(serverFingerprint);
    }
    
    /**
     * 检测是否为Quasar RAT
     */
    private boolean isQuasar(SslEvent sslEvent) {
        Map<String, String> certificate = sslEvent.getCertificate();
        if (certificate == null) {
            return false;
        }
        
        // 检查证书主题和颁发者
        String commonName = certificate.get("commonName");
        String issuerCommonName = certificate.get("issuerCommonName");
        
        if (!"Quasar Server CA".equals(commonName) || !"Quasar Server CA".equals(issuerCommonName)) {
            return false;
        }
        
        // 检查心跳包特征
        return hasHeartbeatWithSize(sslEvent, 33);
    }
    
    /**
     * 检测是否为Remcos RAT
     */
    private boolean isRemcos(SslEvent sslEvent) {
        Map<String, String> certificate = sslEvent.getCertificate();
        if (certificate == null) {
            return false;
        }
        
        // 检查证书主题和颁发者
        String commonName = certificate.get("commonName");
        String issuerCommonName = certificate.get("issuerCommonName");
        
        boolean certMatch = "Cloudflare Inc ECC CA-3".equals(commonName) && "Baltimore CyberTrust Root".equals(issuerCommonName);
        
        // 检查指纹特征
        String clientFingerprint = sslEvent.getClientFingerprint();
        String serverFingerprint = sslEvent.getServerFingerprint();
        
        boolean fingerMatch = "7498398509702323821".equals(clientFingerprint) && "7899447458571054003".equals(serverFingerprint);
        
        return certMatch || fingerMatch;
    }
    
    /**
     * 检测是否为Merlin C2
     */
    private boolean isMerlin(SslEvent sslEvent) {
        // 检查指纹特征
        String clientFingerprint = sslEvent.getClientFingerprint();
        String serverFingerprint = sslEvent.getServerFingerprint();
        
        boolean fingerMatch = "3703381180726290438".equals(clientFingerprint) && "1142710092471348808".equals(serverFingerprint);
        
        // 检查心跳包特征
        boolean heartbeatMatch = hasHeartbeatWithSize(sslEvent, 44) || hasHeartbeatWithSize(sslEvent, 54);
        
        return fingerMatch && heartbeatMatch;
    }
    
    /**
     * 检测是否为PyFud
     */
    private boolean isPyFud(SslEvent sslEvent) {
        // 检查负载特征
        String payload = sslEvent.getPayload();
        if (payload == null || payload.isEmpty()) {
            return false;
        }
        
        // 检查负载是否包含MAC地址和三个逗号分隔的字段
        String[] parts = payload.split(",");
        if (parts.length != 3) {
            return false;
        }
        
        // 检查负载是否包含源MAC地址
        String sourceMac = sslEvent.getSourceMac();
        return payload.contains(sourceMac);
    }
    
    /**
     * 检测是否为Ghost框架
     */
    private boolean isGhost(SslEvent sslEvent) {
        // 检查心跳包特征
        return hasClientHeartbeatWithSize(sslEvent, 1) && hasServerHeartbeatWithSize(sslEvent, -1);
    }
    
    /**
     * 检测是否为NanoCore RAT
     */
    private boolean isNanoCore(SslEvent sslEvent) {
        // 检查心跳包特征
        return hasClientHeartbeatWithSize(sslEvent, 12) && hasServerHeartbeatWithSize(sslEvent, -1234);
    }
    
    /**
     * 检测是否为THC-SSL-DOS
     */
    private boolean isTHCSSLDOS(SslEvent sslEvent) {
        // 检查指纹特征
        String clientFingerprint = sslEvent.getClientFingerprint();
        return "8033014089335809447".equals(clientFingerprint);
    }
    
    /**
     * 检查是否存在指定大小的心跳包
     */
    private boolean hasHeartbeatWithSize(SslEvent sslEvent, int size) {
        return hasClientHeartbeatWithSize(sslEvent, size) || hasServerHeartbeatWithSize(sslEvent, size);
    }
    
    /**
     * 检查是否存在指定大小的客户端心跳包
     */
    private boolean hasClientHeartbeatWithSize(SslEvent sslEvent, int size) {
        Integer clientHeartbeatSize = sslEvent.getClientHeartbeatSize();
        return clientHeartbeatSize != null && clientHeartbeatSize == size;
    }
    
    /**
     * 检查是否存在指定大小的服务器心跳包
     */
    private boolean hasServerHeartbeatWithSize(SslEvent sslEvent, int size) {
        Integer serverHeartbeatSize = sslEvent.getServerHeartbeatSize();
        return serverHeartbeatSize != null && serverHeartbeatSize == size;
    }
    
    /**
     * 创建加密工具告警事件
     */
    private AlarmEvent createAlertEvent(SslEvent sslEvent, String subType, String subName) {
        AlarmEvent alert = new AlarmEvent();
        
        // 设置基本属性
        alert.setId(UUID.randomUUID().toString());
        alert.setType(ENCRYPTED_TOOL_ALERT_TYPE + "_" + subType);
        alert.setName(ENCRYPTED_TOOL_ALERT_NAME + " - " + subName);
        alert.setSeverity(ENCRYPTED_TOOL_ALERT_LEVEL);
        alert.setDescription("检测到可能的加密攻击工具通信行为");
        alert.setSolution("1. 检查源IP和目标IP之间的SSL通信\n2. 分析SSL证书和加密套件是否异常\n3. 检查是否存在未授权的通信通道");
        
        // 设置源IP和端口
        alert.setSourceIp(sslEvent.getSourceIp());
        alert.setSourcePort(sslEvent.getSourcePort());
        alert.setDestinationIp(sslEvent.getDestinationIp());
        alert.setDestinationPort(sslEvent.getDestinationPort());
        
        // 设置时间戳
        alert.setTimestamp(System.currentTimeMillis());
        
        // 设置详细信息
        Map<String, Object> details = new HashMap<>();
        details.put("sslVersion", sslEvent.getSslVersion());
        details.put("cipherSuite", sslEvent.getCipherSuite());
        details.put("certificate", sslEvent.getCertificate());
        details.put("clientFingerprint", sslEvent.getClientFingerprint());
        details.put("serverFingerprint", sslEvent.getServerFingerprint());
        details.put("clientHeartbeatSize", sslEvent.getClientHeartbeatSize());
        details.put("serverHeartbeatSize", sslEvent.getServerHeartbeatSize());
        alert.setDetails(details);
        
        return alert;
    }
    
    /**
     * 创建加密工具检测数据流
     */
    public static DataStream<AlarmEvent> createEncryptedToolDetectionStream(DataStream<ProtocolEvent> inputStream) {
        // 提取SSL事件
        SingleOutputStreamOperator<SslEvent> sslEvents = inputStream
                .filter(event -> event instanceof SslEvent)
                .map(event -> (SslEvent) event)
                .name("提取SSL事件");
        
        // 加密工具检测 - 只处理SSL事件
        return sslEvents
                .map(sslEvent -> (ProtocolEvent) sslEvent) // 转回ProtocolEvent类型以匹配flatMap函数参数
                .flatMap(new EncryptedToolDetector())
                .name("加密工具检测")
                .setParallelism(2);
    }
}
