package com.geeksec.threatdetector.operator.alarm.builder;

import com.alibaba.fastjson.JSONObject;
import org.apache.flink.types.Row;
import redis.clients.jedis.Jedis;

/**
 * 指纹随机化告警构建器
 * 用于构建指纹随机化相关的告警信息
 *
 * <AUTHOR>
 * @since 2023/01/01
 */
public class FingerprintRandomAlarmBuilder extends BaseAlarmBuilder {
    
    /**
     * 单例实例
     */
    private static final FingerprintRandomAlarmBuilder INSTANCE = new FingerprintRandomAlarmBuilder();
    
    /**
     * 私有构造函数
     */
    private FingerprintRandomAlarmBuilder() {}
    
    /**
     * 获取单例实例
     *
     * @return 单例实例
     */
    public static FingerprintRandomAlarmBuilder getInstance() {
        return INSTANCE;
    }
    
    @Override
    public JSONObject getAlarmJson(Row alarmRow, Jedis jedis) {
        JSONObject alarmJson = createBaseAlarmJson("指纹随机化告警", null);
        
        // Extract fields from the Row and add to the JSON object
        alarmJson.put("source_ip", getStringFieldSafely(alarmRow, 2));
        alarmJson.put("source_port", getStringFieldSafely(alarmRow, 3));
        alarmJson.put("destination_ip", getStringFieldSafely(alarmRow, 4));
        alarmJson.put("destination_port", getStringFieldSafely(alarmRow, 5));
        alarmJson.put("fingerprint", getStringFieldSafely(alarmRow, 6));
        alarmJson.put("timestamp", getStringFieldSafely(alarmRow, 7));
        
        return alarmJson;
    }
    
    /**
     * Static helper method to build a fingerprint randomization alarm JSON.
     * @param alarmRow The input Row containing alarm data
     * @param jedis Redis client for additional data lookup
     * @return JSONObject containing the alarm data
     */
    public static JSONObject buildFingerRandomAlarmJson(Row alarmRow, Jedis jedis) {
        return getInstance().getAlarmJson(alarmRow, jedis);
    }
}
