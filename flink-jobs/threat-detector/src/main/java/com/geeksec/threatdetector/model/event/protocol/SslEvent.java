package com.geeksec.threatdetector.model.event.protocol;

import java.util.Map;

/**
 * SSL事件模型
 *
 * <AUTHOR>
 */
public class SslEvent extends ProtocolEvent {

    /**
     * SSL版本
     */
    private String sslVersion;

    /**
     * 加密套件
     */
    private String cipherSuite;

    /**
     * 证书信息
     */
    private Map<String, String> certificate;

    /**
     * 客户端指纹
     */
    private String clientFingerprint;

    /**
     * 服务器指纹
     */
    private String serverFingerprint;

    /**
     * 客户端心跳包大小
     */
    private Integer clientHeartbeatSize;

    /**
     * 服务器心跳包大小
     */
    private Integer serverHeartbeatSize;

    /**
     * SNI域名
     */
    private String sniDomain;

    /**
     * 会话ID
     */
    private String sslSessionId;

    /**
     * 是否使用压缩
     */
    private Boolean compression;

    /**
     * 是否重用会话
     */
    private Boolean sessionReuse;

    public String getSslVersion() {
        return sslVersion;
    }

    public void setSslVersion(String sslVersion) {
        this.sslVersion = sslVersion;
    }

    public String getCipherSuite() {
        return cipherSuite;
    }

    public void setCipherSuite(String cipherSuite) {
        this.cipherSuite = cipherSuite;
    }

    public Map<String, String> getCertificate() {
        return certificate;
    }

    public void setCertificate(Map<String, String> certificate) {
        this.certificate = certificate;
    }

    public String getClientFingerprint() {
        return clientFingerprint;
    }

    public void setClientFingerprint(String clientFingerprint) {
        this.clientFingerprint = clientFingerprint;
    }

    public String getServerFingerprint() {
        return serverFingerprint;
    }

    public void setServerFingerprint(String serverFingerprint) {
        this.serverFingerprint = serverFingerprint;
    }

    public Integer getClientHeartbeatSize() {
        return clientHeartbeatSize;
    }

    public void setClientHeartbeatSize(Integer clientHeartbeatSize) {
        this.clientHeartbeatSize = clientHeartbeatSize;
    }

    public Integer getServerHeartbeatSize() {
        return serverHeartbeatSize;
    }

    public void setServerHeartbeatSize(Integer serverHeartbeatSize) {
        this.serverHeartbeatSize = serverHeartbeatSize;
    }

    public String getSniDomain() {
        return sniDomain;
    }

    public void setSniDomain(String sniDomain) {
        this.sniDomain = sniDomain;
    }

    public String getSslSessionId() {
        return sslSessionId;
    }

    public void setSslSessionId(String sslSessionId) {
        this.sslSessionId = sslSessionId;
    }

    public Boolean getCompression() {
        return compression;
    }

    public void setCompression(Boolean compression) {
        this.compression = compression;
    }

    public Boolean getSessionReuse() {
        return sessionReuse;
    }

    public void setSessionReuse(Boolean sessionReuse) {
        this.sessionReuse = sessionReuse;
    }

    /**
     * 客户端Hello消息
     */
    private String clientHello;

    /**
     * 服务端Hello消息
     */
    private String serverHello;

    /**
     * 服务器名称
     */
    private String serverName;

    public String getClientHello() {
        return clientHello;
    }

    public void setClientHello(String clientHello) {
        this.clientHello = clientHello;
    }

    public String getServerHello() {
        return serverHello;
    }

    public void setServerHello(String serverHello) {
        this.serverHello = serverHello;
    }

    public String getServerName() {
        return serverName;
    }

    public void setServerName(String serverName) {
        this.serverName = serverName;
    }

    @Override
    public String toString() {
        return "SslEvent{" +
                "sslVersion='" + sslVersion + '\'' +
                ", cipherSuite='" + cipherSuite + '\'' +
                ", clientFingerprint='" + clientFingerprint + '\'' +
                ", serverFingerprint='" + serverFingerprint + '\'' +
                ", sniDomain='" + sniDomain + '\'' +
                ", sourceIp='" + getSourceIp() + '\'' +
                ", destinationIp='" + getDestinationIp() + '\'' +
                '}';
    }
}
