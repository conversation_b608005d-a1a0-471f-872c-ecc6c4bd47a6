package com.geeksec.threatdetector.operator.label.ssl;

import com.geeksec.common.utils.net.DomainUtils;
import com.geeksec.threatdetector.common.util.FingerprintRepository;
import com.geeksec.threatdetector.model.nebula.BaseEdge;
import com.geeksec.threatdetector.model.nebula.FingerInfo;
import com.geeksec.threatdetector.model.nebula.SSLFingerInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.netty4.io.netty.util.internal.StringUtil;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.springframework.util.ObjectUtils;

import java.util.*;

/**
 * SSL指纹映射器
 * 负责处理SSL/TLS指纹信息，包括客户端和服务器端的SSL指纹提取和映射
 *
 * <AUTHOR>
 */
@Slf4j
public class Ssl<PERSON>ingerprintMapper extends RichFlatMapFunction<Map<String, Object>, Map<String, Object>> {
    public HashMap<String, FingerInfo> FINGER_JA3_MAP = null;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        List<FingerInfo> fingerInfoList = FingerprintRepository.getMysqlFingerInfo();
        if (fingerInfoList != null) {
            FINGER_JA3_MAP = FingerprintRepository.getFingerJa3Map(fingerInfoList);
            log.info("FINGER_JA3_MAP 信息加载成功");
        } else {
            FINGER_JA3_MAP = new HashMap<>();
            log.info("FINGER_JA3_MAP 信息加载失败");
        }
    }

    @Override
    public void flatMap(Map<String, Object> pbMap, Collector<Map<String, Object>> collector) throws Exception {
        SSLFingerInfo sSSLFingerInfo = getSSLFinger(pbMap, "client");
        SSLFingerInfo dSSLFingerInfo = getSSLFinger(pbMap, "server");
        BaseEdge clientSslConnectDomainEdge = getSslConnectDomainEdge(pbMap, "client");
        BaseEdge serverSslConnectDomainEdge = getSslConnectDomainEdge(pbMap, "server");
        Row dipdSSLFingerSipRow = getDipdSSLFingerSipRow(pbMap);

        HashMap<String, Object> resultMap = new HashMap<>();
        resultMap.put("sSSLFingerInfoTag", sSSLFingerInfo);
        resultMap.put("dSSLFingerInfoTag", dSSLFingerInfo);
        if (dipdSSLFingerSipRow!=null){
            resultMap.put("SIP_DIP_FINGER_ROW",dipdSSLFingerSipRow);
        }
        resultMap.put("clientSslConnectDomainEdge", clientSslConnectDomainEdge);
        resultMap.put("serverSslConnectDomainEdge", serverSslConnectDomainEdge);

        collector.collect(resultMap);
    }

    private BaseEdge getSslConnectDomainEdge(Map<String, Object> pbMap, String type) {
        String clientIp = (String) pbMap.get("sIp");
        String serverIp = (String) pbMap.get("dIp");
        String sni = (String) pbMap.get("CH_ServerName");

        BaseEdge baseEdge = new BaseEdge();

        if (DomainUtils.isValidDomain(sni) && !sni.equals(serverIp)) {
            if (type.equals("client")) {
                baseEdge.setSrcId(clientIp);
            } else {
                baseEdge.setSrcId(serverIp);
            }
            if (sni.length() > 200) {
                baseEdge.setDstId(sni.hashCode() + "");
            } else {
                baseEdge.setDstId(sni);
            }
            baseEdge.setFirstSeen((Integer) pbMap.get("StartTime"));
            baseEdge.setLastSeen((Integer) pbMap.get("StartTime"));
            baseEdge.setSessionCnt(0L);
        } else {
            return null;
        }



        return baseEdge;
    }

    private SSLFingerInfo getSSLFinger(Map<String, Object> pbMap, String type) {
        SSLFingerInfo sslFingerTagInfo = new SSLFingerInfo();
        String sSSLFinger = (String) pbMap.get("sSSLFinger");
        String dSSLFinger = (String) pbMap.get("dSSLFinger");
        String sni = (String) pbMap.get("CH_ServerName");
        String serverIp = (String) pbMap.get("dIp");
        if (type.equals("client")) {
            if (ObjectUtils.isEmpty(sSSLFinger)) {
                return null;
            }
            sslFingerTagInfo.setFingerId(sSSLFinger);
            FingerInfo fingerInfo = FINGER_JA3_MAP.get(sSSLFinger);
            sslFingerTagInfo.setJa3Hash(fingerInfo != null ? fingerInfo.getJa3Hash() : StringUtils.EMPTY);
            sslFingerTagInfo.setDesc(StringUtil.EMPTY_STRING);
            sslFingerTagInfo.setType("client");
        }

        if (type.equals("server")) {
            if (ObjectUtils.isEmpty(dSSLFinger)) {
                return null;
            }
            sslFingerTagInfo.setFingerId(dSSLFinger);
            FingerInfo serverFingerInfo = FINGER_JA3_MAP.get(dSSLFinger);
            sslFingerTagInfo.setJa3Hash(serverFingerInfo != null ? serverFingerInfo.getJa3Hash() : StringUtils.EMPTY);
            sslFingerTagInfo.setDesc(StringUtil.EMPTY_STRING);
            sslFingerTagInfo.setType("server");
        }
        if (DomainUtils.isValidDomain(sni) && !sni.equals(serverIp)){
                sslFingerTagInfo.setServer_domain(sni);
        }else{
            sslFingerTagInfo.setServer_domain(null);
        }
        sslFingerTagInfo.setDIp((String) pbMap.get("dIp"));
        sslFingerTagInfo.setSIp((String) pbMap.get("sIp"));
        sslFingerTagInfo.setSessionId((String) pbMap.get("SessionId"));
        sslFingerTagInfo.setEsKey((String) pbMap.get("es_key"));



        return sslFingerTagInfo;
    }

    public Row getDipdSSLFingerSipRow(Map<String, Object> sslPbMap){
        List<String> dipSipList = Arrays.asList(sslPbMap.get("dIp").toString(),sslPbMap.get("sIp").toString());
        Row dipdSSLFingerSipRow = new Row(7);
        Collection<String> SessionId_list = new HashSet<>();
        SessionId_list.add((String) sslPbMap.get("SessionId"));
        Collection<String> fingerSet = new HashSet<>();
        String finger = (String) sslPbMap.get("sSSLFinger");
        if (finger.equals("0")){
            return null;
        }
        fingerSet.add(finger);



        dipdSSLFingerSipRow.setField(0,"SIP_DIP_FINGER_ROW");
        dipdSSLFingerSipRow.setField(1,dipSipList);
        dipdSSLFingerSipRow.setField(2,fingerSet);
        dipdSSLFingerSipRow.setField(3,sslPbMap.get("StartTime"));
        dipdSSLFingerSipRow.setField(4,SessionId_list);
        dipdSSLFingerSipRow.setField(5,sslPbMap.get("es_key"));
        dipdSSLFingerSipRow.setField(6, null);
        return dipdSSLFingerSipRow;
    }
}
