package com.geeksec.threatdetector.io.sink.es;

import com.geeksec.common.utils.json.JsonUtils;
import com.geeksec.threatdetector.model.event.alarm.AlarmEvent;
import org.apache.flink.connector.elasticsearch.sink.Elasticsearch7SinkBuilder;
import org.apache.flink.connector.elasticsearch.sink.ElasticsearchSink;
import org.apache.flink.connector.elasticsearch.sink.FlushBackoffType;
import org.apache.http.HttpHost;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.client.Requests;
import org.elasticsearch.common.xcontent.XContentType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * Elasticsearch告警接收器 - 使用Flink 1.15+的新API
 *
 * <AUTHOR>
 */
public class ElasticsearchAlertSink {
    private static final Logger logger = LoggerFactory.getLogger(ElasticsearchAlertSink.class);
    private static final DateTimeFormatter INDEX_DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy.MM.dd");
    
    /**
     * 私有构造函数，防止实例化
     */
    private ElasticsearchAlertSink() {
        // 私有构造函数，防止实例化
    }
    
    /**
     * 创建Elasticsearch接收器
     *
     * @param hosts Elasticsearch主机列表，格式为 ["host1:port1", "host2:port2", ...]
     * @param indexPrefix 索引前缀，实际索引名称为 {indexPrefix}-{yyyy.MM.dd}
     * @param bulkSize 批量大小
     * @param bulkFlushInterval 批量刷新间隔（毫秒）
     * @return 配置好的ElasticsearchSink实例
     */
    public static ElasticsearchSink<AlarmEvent> createElasticsearchSink(
            String[] hostStrings, String indexPrefix, int bulkSize, int bulkFlushInterval) {
        
        // 1. 转换主机列表为HttpHost数组
        List<HttpHost> httpHosts = new ArrayList<>();
        for (String host : hostStrings) {
            String[] parts = host.split(":");
            String hostname = parts[0];
            int port = parts.length > 1 ? Integer.parseInt(parts[1]) : 9200;
            httpHosts.add(new HttpHost(hostname, port, "http"));
        }

        // 2. 创建ElasticsearchSink
        return new Elasticsearch7SinkBuilder<AlarmEvent>()
                .setHosts(httpHosts.toArray(new HttpHost[0]))
                .setBulkFlushMaxActions(bulkSize)  // 批量大小
                .setBulkFlushInterval(bulkFlushInterval)  // 批量刷新间隔
                .setBulkFlushBackoffStrategy(
                    FlushBackoffType.EXPONENTIAL,
                    3,  // 最大重试次数
                    1000) // 初始重试间隔(ms)
                .setEmitter((alert, context, indexer) -> {
                    try {
                        // 1. 将告警事件转换为JSON
                        String json = JsonUtils.toJsonString(alert);
                        
                        // 2. 创建索引请求
                        // 生成索引名称，格式为: indexPrefix-yyyy.MM.dd
                        String indexSuffix = getIndexSuffix(alert.getTimestamp());
                        String indexName = String.format("%s-%s", indexPrefix, indexSuffix);
                        
                        IndexRequest indexRequest = Requests.indexRequest()
                                .index(indexName)
                                .id(alert.getId())
                                .source(json, XContentType.JSON);
                        
                        // 3. 添加索引请求到批处理
                        indexer.add(indexRequest);
                        logger.debug("已将告警 {} 索引到 {}", alert.getId(), indexName);
                    } catch (Exception e) {
                        // 记录错误并继续处理下一条记录
                        logger.error("处理告警失败: " + alert.getId(), e);
                        throw e; // 重新抛出异常以便Flink的重试机制处理
                    }
                })
                .build();
    }
    
    /**
     * 根据时间戳生成索引后缀，格式为yyyy.MM.dd
     */
    private static String getIndexSuffix(long timestamp) {
        Instant instant = Instant.ofEpochMilli(timestamp);
        LocalDateTime dateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
        return dateTime.format(DateTimeFormatter.ofPattern("yyyy.MM.dd"));
    }
}
