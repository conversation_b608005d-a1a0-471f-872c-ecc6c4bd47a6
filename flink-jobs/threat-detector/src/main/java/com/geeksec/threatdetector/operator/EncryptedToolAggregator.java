package com.geeksec.threatdetector.operator;

import com.geeksec.threatdetector.infra.trigger.ConnectTrigger;
import com.geeksec.threatdetector.model.connection.ConnectBasicInfo;
import com.geeksec.threatdetector.model.encryption.EncryptedToolInfo;
import com.geeksec.threatdetector.model.protocol.SslSimpleInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.functions.AggregateFunction;

import java.util.List;
import java.util.Map;

/**
 * Aggregates connection and SSL information to build an {@link EncryptedToolInfo} object.
 * This aggregator is designed to work with Flink streams.
 *
 * <AUTHOR>
 * @Date 2024/9/13
 */
@Slf4j
public class EncryptedToolAggregator implements AggregateFunction<Map<String, Object>, EncryptedToolInfo, EncryptedToolInfo> {

    @Override
    public EncryptedToolInfo createAccumulator() {
        return new EncryptedToolInfo();
    }

    @Override
    public EncryptedToolInfo add(Map<String, Object> pbMap, EncryptedToolInfo encryptedToolInfo) {
        String type = (String) pbMap.get("type");
        if (ConnectTrigger.CONNECTION_END_TYPE.equals(type)){
            ConnectBasicInfo connectBasicInfo = new ConnectBasicInfo(pbMap);
            encryptedToolInfo.setConnectBasicInfo(connectBasicInfo);
            return encryptedToolInfo;
        }else if (ConnectTrigger.SSL_TYPE.equals(type)){
            SslSimpleInfo sslSimpleInfo = new SslSimpleInfo(pbMap);
            List<SslSimpleInfo> sslSimpleInfos = encryptedToolInfo.getSslSimpleInfos();
            sslSimpleInfos.add(sslSimpleInfo);
            encryptedToolInfo.setSslSimpleInfos(sslSimpleInfos);
            return encryptedToolInfo;
        } else {
            log.error("EncryptedToolAggregator received data of an unexpected type (not SSL or CONNECTION_END), returning accumulator unchanged.");
            return encryptedToolInfo;
        }
    }

    @Override
    public EncryptedToolInfo getResult(EncryptedToolInfo encryptedToolInfo) {
        return encryptedToolInfo;
    }

    @Override
    public EncryptedToolInfo merge(EncryptedToolInfo encryptedToolInfo1, EncryptedToolInfo encryptedToolInfo2) {

        EncryptedToolInfo encryptedToolInfo = new EncryptedToolInfo();
        if (encryptedToolInfo1.getConnectBasicInfo() != null){
            encryptedToolInfo.setConnectBasicInfo(encryptedToolInfo1.getConnectBasicInfo());
        }
        if (encryptedToolInfo2.getConnectBasicInfo() != null){
            encryptedToolInfo.setConnectBasicInfo(encryptedToolInfo2.getConnectBasicInfo());
        }

        List<SslSimpleInfo> sslSimpleInfos1 = encryptedToolInfo1.getSslSimpleInfos();
        List<SslSimpleInfo> sslSimpleInfos2 = encryptedToolInfo2.getSslSimpleInfos();
        sslSimpleInfos1.addAll(sslSimpleInfos2);
        encryptedToolInfo.setSslSimpleInfos(sslSimpleInfos1);
        return encryptedToolInfo;
    }
}
