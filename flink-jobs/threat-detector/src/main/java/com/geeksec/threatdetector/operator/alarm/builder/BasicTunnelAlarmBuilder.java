package com.geeksec.threatdetector.operator.alarm.builder;

import com.alibaba.fastjson.JSONObject;
import org.apache.flink.types.Row;
import redis.clients.jedis.Jedis;

/**
 * Builder for basic tunnel related alarms.
 * <AUTHOR>
 */
public class BasicTunnelAlarmBuilder extends BaseAlarmBuilder {
    
    private static final BasicTunnelAlarmBuilder INSTANCE = new BasicTunnelAlarmBuilder();
    
    private BasicTunnelAlarmBuilder() {}
    
    public static BasicTunnelAlarmBuilder getInstance() {
        return INSTANCE;
    }
    
    @Override
    public JSONObject getAlarmJson(Row alarmRow, Jedis jedis) {
        String tunnelType = getStringFieldSafely(alarmRow, 1);
        JSONObject alarmJson = createBaseAlarmJson("基础隧道告警", tunnelType);
        
        // Extract fields from the Row and add to the JSON object
        alarmJson.put("source_ip", getStringFieldSafely(alarmRow, 2));
        alarmJson.put("source_port", getStringFieldSafely(alarmRow, 3));
        alarmJson.put("destination_ip", getStringFieldSafely(alarmRow, 4));
        alarmJson.put("destination_port", getStringFieldSafely(alarmRow, 5));
        alarmJson.put("protocol", tunnelType);
        alarmJson.put("timestamp", getStringFieldSafely(alarmRow, 6));
        
        return alarmJson;
    }
    
    /**
     * Static helper method to build a basic tunnel alarm JSON.
     * @param alarmRow The input Row containing alarm data
     * @param jedis Redis client for additional data lookup
     * @return JSONObject containing the alarm data
     */
    public static JSONObject buildBasicTunnelAlarmJson(Row alarmRow, Jedis jedis) {
        return getInstance().getAlarmJson(alarmRow, jedis);
    }
}
