package com.geeksec.threatdetector.operator.analysis.tunnel;

import com.geeksec.threatdetector.model.event.alarm.AlarmEvent;
import com.geeksec.threatdetector.model.event.protocol.TcpEvent;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * TCP隧道检测函数
 *
 * <AUTHOR>
 */
/**
 * TCP隧道检测器
 * 用于检测基于TCP协议的隧道流量特征
 *
 * <AUTHOR>
 * @since 2023/01/01
 */
public class TcpTunnelDetector extends RichFlatMapFunction<TcpEvent, AlarmEvent> {
    
    private static final Logger logger = LoggerFactory.getLogger(TcpTunnelDetector.class);
    private static final String TCP_TUNNEL_ALERT_TYPE = "TCP_TUNNEL_DETECTED";
    private static final String TCP_TUNNEL_ALERT_NAME = "TCP隧道检测";
    private static final String TCP_TUNNEL_ALERT_LEVEL = "HIGH";
    
    // 可疑的TCP端口
    private static final int[] SUSPICIOUS_PORTS = {
        22, 23, 25, 53, 80, 443, 8080, 8443
    };
    
    @Override
    public void flatMap(TcpEvent tcpEvent, Collector<AlarmEvent> out) throws Exception {
        try {
            // 检测TCP隧道特征
            if (isTcpTunnel(tcpEvent)) {
                // 创建告警事件
                AlarmEvent alert = createAlertEvent(tcpEvent);
                out.collect(alert);
                
                logger.warn("检测到TCP隧道: {}:{} -> {}:{}", 
                        tcpEvent.getSourceIp(), tcpEvent.getSourcePort(),
                        tcpEvent.getDestinationIp(), tcpEvent.getDestinationPort());
            }
        } catch (Exception e) {
            logger.error("TCP隧道检测错误: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 检测TCP连接是否包含隧道特征
     */
    private boolean isTcpTunnel(TcpEvent tcpEvent) {
        // 1. 检查端口是否可疑
        int destPort = tcpEvent.getDestinationPort();
        boolean isSuspiciousPort = false;
        for (int port : SUSPICIOUS_PORTS) {
            if (destPort == port) {
                isSuspiciousPort = true;
                break;
            }
        }
        
        if (!isSuspiciousPort) {
            return false;
        }
        
        // 2. 检查数据包特征
        byte[] payload = tcpEvent.getPayloadBytes();
        if (payload != null && payload.length > 0) {
            // 检查数据包是否包含异常模式
            if (containsAbnormalPattern(payload)) {
                return true;
            }
            
            // 检查数据包是否包含加密数据
            if (isLikelyEncrypted(payload)) {
                return true;
            }
        }
        
        // 3. 检查连接持续时间
        long duration = tcpEvent.getEndTime() - tcpEvent.getStartTime();
        if (duration > 3600000) { // 1小时
            // 长时间连接可能是隧道
            return true;
        }
        
        // 4. 检查数据包大小分布
        // 这部分需要状态管理，这里简化处理
        
        // 5. 检查数据包间隔
        // 这部分需要状态管理，这里简化处理
        
        return false;
    }
    
    /**
     * 检查是否包含异常模式
     */
    private boolean containsAbnormalPattern(byte[] data) {
        if (data.length < 10) {
            return false;
        }
        
        // 检查是否包含重复模式
        int repeatedCount = 0;
        byte prevByte = data[0];
        for (int i = 1; i < data.length; i++) {
            if (data[i] == prevByte) {
                repeatedCount++;
                if (repeatedCount > 20) {
                    return true;
                }
            } else {
                repeatedCount = 0;
                prevByte = data[i];
            }
        }
        
        return false;
    }
    
    /**
     * 检查数据是否可能是加密的
     */
    private boolean isLikelyEncrypted(byte[] data) {
        if (data.length < 20) {
            return false;
        }
        
        // 计算字节分布
        int[] byteCounts = new int[256];
        for (byte b : data) {
            byteCounts[b & 0xFF]++;
        }
        
        // 计算熵值
        double entropy = 0;
        for (int count : byteCounts) {
            if (count > 0) {
                double probability = (double) count / data.length;
                entropy -= probability * Math.log(probability) / Math.log(2);
            }
        }
        
        // 熵值高表示数据可能是加密的或压缩的
        return entropy > 7.0;
    }
    
    /**
     * 创建TCP隧道告警事件
     */
    private AlarmEvent createAlertEvent(TcpEvent tcpEvent) {
        AlarmEvent alert = new AlarmEvent();
        
        // 设置基本属性
        alert.setId(UUID.randomUUID().toString());
        alert.setType(TCP_TUNNEL_ALERT_TYPE);
        alert.setName(TCP_TUNNEL_ALERT_NAME);
        alert.setSeverity(TCP_TUNNEL_ALERT_LEVEL);
        alert.setDescription("检测到可能的TCP隧道通信行为");
        alert.setSolution("1. 检查源IP和目标IP之间的TCP通信\n2. 分析TCP数据包内容是否存在数据泄露\n3. 检查是否存在未授权的通信通道");
        
        // 设置源IP和端口
        alert.setSourceIp(tcpEvent.getSourceIp());
        alert.setSourcePort(tcpEvent.getSourcePort());
        alert.setDestinationIp(tcpEvent.getDestinationIp());
        alert.setDestinationPort(tcpEvent.getDestinationPort());
        
        // 设置时间戳
        alert.setTimestamp(System.currentTimeMillis());
        
        // 设置详细信息
        Map<String, Object> details = new HashMap<>();
        details.put("startTime", tcpEvent.getStartTime());
        details.put("endTime", tcpEvent.getEndTime());
        details.put("packetCount", tcpEvent.getPacketCount());
        details.put("byteCount", tcpEvent.getByteCount());
        details.put("flags", tcpEvent.getFlags());
        alert.setDetails(details);
        
        return alert;
    }
    
    /**
     * 创建TCP隧道检测数据流
     */
    public static DataStream<AlarmEvent> detectTcpTunnel(DataStream<TcpEvent> tcpEvents) {
        return tcpEvents
                .flatMap(new TcpTunnelDetector())
                .name("TCP隧道检测")
                .setParallelism(2);
    }
}
