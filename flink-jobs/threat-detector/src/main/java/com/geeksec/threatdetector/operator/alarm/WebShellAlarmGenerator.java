package com.geeksec.threatdetector.operator.alarm;

import com.geeksec.threatdetector.model.event.alarm.AlarmEvent;
import com.geeksec.threatdetector.model.event.protocol.HttpEvent;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.streaming.api.datastream.DataStream;

import java.util.*;

/**
 * WebShell攻击告警生成器
 * 负责将WebShell攻击事件转换为告警事件
 *
 * <AUTHOR>
 * @since 2023/01/01
 */
@Slf4j
public class WebShellAlarmGenerator extends AbstractThreatAlarmGenerator<HttpEvent> {

    private static final String ALERT_TYPE = "WEBSHELL_DETECTED";
    private static final String ALERT_NAME = "WebShell攻击检测";
    private static final String ALERT_LEVEL = "HIGH";

    // WebShell类型列表 - 保留供后续功能扩展使用
    private static final List<String> UNENCRYPTED_WEBSHELL_TYPES = Arrays.asList(
            "中国菜刀", "<PERSON><PERSON>", "蚁剑", "WeBaco<PERSON>", "Webshell-Sniper",
            "开山斧", "Altman", "QuasiBot", "WebshellManager",
            "w8ay", "cknife", "WebKnife", "K8飞刀", "Hatchet", "小李飞刀");

    private static final List<String> ENCRYPTED_WEBSHELL_TYPES = Arrays.asList(
            "冰蝎", "哥斯拉", "SharPyShell", "Weevely", "天蝎", "jspmaster", "b374k");

    @Override
    protected boolean detectThreat(HttpEvent event) {
        // 检查是否包含WebShell特征
        return isWebShellRequest(event);
    }

    @Override
    protected AlarmEvent convertToAlarmEvent(HttpEvent event) {
        // 确定WebShell类型
        String webShellType = determineWebShellType(event);
        boolean isEncrypted = ENCRYPTED_WEBSHELL_TYPES.contains(webShellType);

        // 创建告警描述和解决方案
        String description = String.format("检测到%s类型的WebShell攻击行为，攻击者可能已获取服务器控制权限",
                isEncrypted ? "加密" : "非加密");

        String solution = "1. 立即隔离受影响的服务器\n" +
                "2. 检查Web服务器日志，查找可疑的上传或执行操作\n" +
                "3. 扫描服务器文件系统，查找并删除WebShell文件\n" +
                "4. 检查并修复可能被利用的Web应用漏洞\n" +
                "5. 更新Web应用程序并加强访问控制";

        // 创建基础告警事件
        AlarmEvent alertEvent = createBaseAlertEvent(
                ALERT_TYPE,
                ALERT_NAME,
                ALERT_LEVEL,
                description,
                solution,
                event.getSourceIp(),
                event.getSourcePort(),
                event.getDestinationIp(),
                event.getDestinationPort());

        // 添加详细信息
        Map<String, Object> details = alertEvent.getDetails();
        details.put("webShellType", webShellType);
        details.put("isEncrypted", isEncrypted);
        details.put("httpMethod", event.getMethod());
        details.put("httpUri", event.getUri());
        details.put("httpHost", event.getHost());
        details.put("httpUserAgent", event.getUserAgent());
        details.put("httpContentType", event.getContentType());
        details.put("httpReferer", event.getReferer());
        details.put("httpCookie", event.getCookie());
        details.put("httpPayload", event.getPayload());

        // 添加攻击家族信息
        List<Map<String, String>> attackFamily = new ArrayList<>();
        Map<String, String> family = new HashMap<>();
        family.put("key", isEncrypted ? "加密webshell" : "非加密webshell");
        family.put("value", webShellType);
        attackFamily.add(family);
        details.put("attack_family", attackFamily);

        // 添加攻击目标信息
        List<Map<String, Object>> targets = new ArrayList<>();
        Map<String, Object> target = new HashMap<>();
        target.put("target_type", "Web服务器");
        target.put("target_ip", event.getDestinationIp());
        targets.add(target);
        details.put("targets", targets);

        return alertEvent;
    }

    /**
     * 检测是否为WebShell请求
     */
    private boolean isWebShellRequest(HttpEvent event) {
        // 检查HTTP方法
        String method = event.getMethod();
        if (method == null || (!method.equals("POST") && !method.equals("GET"))) {
            return false;
        }

        // 检查URI
        String uri = event.getUri();
        if (uri == null) {
            return false;
        }

        // 检查是否包含常见WebShell文件扩展名
        if (uri.endsWith(".php") || uri.endsWith(".jsp") || uri.endsWith(".aspx") || uri.endsWith(".asp")) {
            // 检查负载
            String payload = event.getPayload();
            if (payload != null && !payload.isEmpty()) {
                // 检查是否包含WebShell特征
                return containsWebShellFeatures(payload);
            }
        }

        return false;
    }

    /**
     * 检查是否包含WebShell特征
     */
    private boolean containsWebShellFeatures(String payload) {
        // 非加密WebShell特征
        List<String> unencryptedFeatures = Arrays.asList(
                "eval(", "system(", "exec(", "shell_exec(", "passthru(", "popen(",
                "proc_open(", "pcntl_exec(", "assert(", "preg_replace", "create_function",
                "include(", "require(", "include_once(", "require_once(", "ReflectionFunction",
                "call_user_func", "call_user_func_array", "array_map", "array_filter",
                "usort", "uasort", "uksort", "array_walk", "array_reduce",
                "base64_decode", "gzinflate", "gzuncompress", "gzdecode",
                "str_rot13", "strrev", "chr(", "fromCharCode", "String.fromCharCode");

        // 加密WebShell特征
        List<String> encryptedFeatures = Arrays.asList(
                "AES", "DES", "TripleDES", "Blowfish", "RC4",
                "RSA", "DSA", "ECDSA", "HMAC", "MD5",
                "SHA", "SHA1", "SHA256", "SHA512", "CryptoJS",
                "encrypt", "decrypt", "cipher", "decipher");

        // 检查是否包含非加密WebShell特征
        for (String feature : unencryptedFeatures) {
            if (payload.contains(feature)) {
                return true;
            }
        }

        // 检查是否包含加密WebShell特征
        for (String feature : encryptedFeatures) {
            if (payload.contains(feature)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 确定WebShell类型
     */
    private String determineWebShellType(HttpEvent event) {
        String payload = event.getPayload();
        String userAgent = event.getUserAgent();

        // 根据特征确定WebShell类型
        if (payload != null) {
            if (payload.contains("Chopper") || payload.contains("caidao")) {
                return "中国菜刀";
            } else if (payload.contains("antsword") || userAgent != null && userAgent.contains("antsword")) {
                return "蚁剑";
            } else if (payload.contains("Behinder") || payload.contains("rebeyond")) {
                return "冰蝎";
            } else if (payload.contains("godzilla")) {
                return "哥斯拉";
            } else if (payload.contains("SharPyShell")) {
                return "SharPyShell";
            } else if (payload.contains("weevely")) {
                return "Weevely";
            } else if (payload.contains("Altman")) {
                return "Altman";
            } else if (payload.contains("QuasiBot")) {
                return "QuasiBot";
            } else if (payload.contains("WebshellManager")) {
                return "WebshellManager";
            } else if (payload.contains("w8ay")) {
                return "w8ay";
            } else if (payload.contains("cknife")) {
                return "cknife";
            } else if (payload.contains("WebKnife")) {
                return "WebKnife";
            } else if (payload.contains("K8")) {
                return "K8飞刀";
            } else if (payload.contains("Hatchet")) {
                return "Hatchet";
            } else if (payload.contains("b374k")) {
                return "b374k";
            } else if (payload.contains("jspmaster")) {
                return "jspmaster";
            }
        }

        // 默认返回未知类型
        return "未知WebShell";
    }

    /**
     * 创建WebShell告警生成数据流
     */
    public static DataStream<AlarmEvent> generateWebShellAlarms(DataStream<HttpEvent> httpEvents) {
        return httpEvents
                .flatMap(new WebShellToAlarmFunction())
                .name("WebShell告警生成")
                .setParallelism(2);
    }
}
