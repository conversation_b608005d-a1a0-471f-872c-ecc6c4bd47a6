package com.geeksec.threatdetector.operator.alarm.builder;

/**
 * Centralized access to all alarm builder instances.
 * <AUTHOR>
 */
public class AlarmBuilders {
    
    private static final MineAlarmBuilder MINE_ALARM_BUILDER = MineAlarmBuilder.getInstance();
    private static final DnsMineAlarmBuilder DNS_MINE_ALARM_BUILDER = DnsMineAlarmBuilder.getInstance();
    private static final FingerprintDetectAlarmBuilder FINGERPRINT_DETECT_ALARM_BUILDER = FingerprintDetectAlarmBuilder.getInstance();
    private static final ScanActivityAlarmBuilder SCAN_ACTIVITY_ALARM_BUILDER = ScanActivityAlarmBuilder.getInstance();
    private static final FingerprintRandomAlarmBuilder FINGERPRINT_RANDOM_ALARM_BUILDER = FingerprintRandomAlarmBuilder.getInstance();
    private static final DnsTunnelAlarmBuilder DNS_TUNNEL_ALARM_BUILDER = DnsTunnelAlarmBuilder.getInstance();
    private static final BasicTunnelAlarmBuilder BASIC_TUNNEL_ALARM_BUILDER = BasicTunnelAlarmBuilder.getInstance();
    private static final EncryptedTunnelAlarmBuilder ENCRYPTED_TUNNEL_ALARM_BUILDER = EncryptedTunnelAlarmBuilder.getInstance();
    private static final EncryptedTunnelAttackAlarmBuilder ENCRYPTED_TUNNEL_ATTACK_ALARM_BUILDER = EncryptedTunnelAttackAlarmBuilder.getInstance();
    private static final HackToolAlarmBuilder HACK_TOOL_ALARM_BUILDER = HackToolAlarmBuilder.getInstance();

    public static MineAlarmBuilder getMineAlarmBuilder() {
        return MINE_ALARM_BUILDER;
    }

    public static DnsMineAlarmBuilder getDnsMineAlarmBuilder() {
        return DNS_MINE_ALARM_BUILDER;
    }

    public static FingerprintDetectAlarmBuilder getFingerprintDetectAlarmBuilder() {
        return FINGERPRINT_DETECT_ALARM_BUILDER;
    }

    public static ScanActivityAlarmBuilder getScanActivityAlarmBuilder() {
        return SCAN_ACTIVITY_ALARM_BUILDER;
    }

    public static FingerprintRandomAlarmBuilder getFingerprintRandomAlarmBuilder() {
        return FINGERPRINT_RANDOM_ALARM_BUILDER;
    }

    public static DnsTunnelAlarmBuilder getDnsTunnelAlarmBuilder() {
        return DNS_TUNNEL_ALARM_BUILDER;
    }

    public static BasicTunnelAlarmBuilder getBasicTunnelAlarmBuilder() {
        return BASIC_TUNNEL_ALARM_BUILDER;
    }

    public static EncryptedTunnelAlarmBuilder getEncryptedTunnelAlarmBuilder() {
        return ENCRYPTED_TUNNEL_ALARM_BUILDER;
    }

    public static EncryptedTunnelAttackAlarmBuilder getEncryptedTunnelAttackAlarmBuilder() {
        return ENCRYPTED_TUNNEL_ATTACK_ALARM_BUILDER;
    }

    public static HackToolAlarmBuilder getHackToolAlarmBuilder() {
        return HACK_TOOL_ALARM_BUILDER;
    }
}
