package com.geeksec.threatdetector.util;

import com.geeksec.common.utils.db.DatabaseConnectionManager;
import com.geeksec.threatdetector.model.nebula.FingerInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Repository class for MySQL database operations related to fingerprint
 * information.
 *
 * <AUTHOR>
 */
public class FingerprintRepository {
    private static final Logger LOGGER = LoggerFactory.getLogger(FingerprintRepository.class);
    private static final String DRIVER = "com.mysql.jdbc.Driver";

    /**
     * Retrieves fingerprint information from MySQL database.
     * 
     * @return List of FingerInfo objects
     */
    public static List<FingerInfo> getMysqlFingerInfo() {
        List<FingerInfo> fingerInfoList = new ArrayList<>();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;

        try {
            conn = DatabaseConnectionManager.getConnection();
            String sql = "SELECT id, finger_id, ja3_hash, `desc`, `type` FROM finger_info";
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();

            while (rs.next()) {
                FingerInfo fingerInfo = new FingerInfo();
                fingerInfo.setId(rs.getLong("id"));
                fingerInfo.setFingerId(rs.getString("finger_id"));
                fingerInfo.setJa3Hash(rs.getString("ja3_hash"));
                fingerInfo.setDesc(rs.getString("desc"));
                fingerInfo.setType(rs.getString("type"));
                fingerInfoList.add(fingerInfo);
            }
        } catch (SQLException e) {
            LOGGER.error("Error retrieving fingerprint information from MySQL", e);
        } catch (Exception e) {
            LOGGER.error("Error getting database connection", e);
        } finally {
            closeResources(rs, ps, conn);
        }

        return fingerInfoList;
    }

    /**
     * Creates a mapping of fingerprint IDs to their types.
     *
     * @param fingerInfoList List of FingerInfo objects
     * @return Map of fingerprint ID to type
     */
    public static HashMap<String, String> getFingerTypeMap(List<FingerInfo> fingerInfoList) {
        if (fingerInfoList == null || fingerInfoList.isEmpty()) {
            return new HashMap<>();
        }

        HashMap<String, String> fingerTypeMap = new HashMap<>(fingerInfoList.size());
        for (FingerInfo fingerInfo : fingerInfoList) {
            if (fingerInfo.getFingerId() != null && fingerInfo.getType() != null) {
                fingerTypeMap.put(fingerInfo.getFingerId(), fingerInfo.getType());
            }
        }

        return fingerTypeMap;
    }

    /**
     * Creates a mapping of JA3 hashes to their corresponding FingerInfo objects.
     *
     * @param fingerInfoList List of FingerInfo objects
     * @return Map of JA3 hash to FingerInfo
     */
    public static HashMap<String, FingerInfo> getFingerJa3Map(List<FingerInfo> fingerInfoList) {
        if (fingerInfoList == null || fingerInfoList.isEmpty()) {
            return new HashMap<>();
        }

        HashMap<String, FingerInfo> fingerJa3Map = new HashMap<>(fingerInfoList.size());
        for (FingerInfo fingerInfo : fingerInfoList) {
            if (fingerInfo.getJa3Hash() != null) {
                fingerJa3Map.put(fingerInfo.getJa3Hash(), fingerInfo);
            }
        }

        return fingerJa3Map;
    }

    /**
     * Closes database resources using try-with-resources pattern.
     */
    private static void closeResources(ResultSet rs, Statement stmt, Connection conn) {
        try {
            if (rs != null) {
                rs.close();
            }
            if (stmt != null) {
                stmt.close();
            }
            if (conn != null) {
                conn.close();
            }
        } catch (SQLException e) {
            LOGGER.error("Error closing database resources", e);
        }
    }
}
