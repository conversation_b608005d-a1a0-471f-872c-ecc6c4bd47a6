package com.geeksec.threatdetector.operator.analysis.webshell;

import com.geeksec.threatdetector.model.event.BaseEvent;
import com.geeksec.threatdetector.model.event.alarm.AlarmEvent;
import com.geeksec.threatdetector.model.event.protocol.HttpEvent;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * WebShell检测函数
 *
 * <AUTHOR>
 */
/**
 * WebShell检测器
 * 用于检测WebShell攻击流量特征
 *
 * <AUTHOR>
 * @since 2023/01/01
 */
public class WebShellDetector extends RichFlatMapFunction<BaseEvent, AlarmEvent> {
    
    private static final Logger logger = LoggerFactory.getLogger(WebShellDetector.class);
    private static final String WEBSHELL_ALERT_TYPE = "WEBSHELL_DETECTED";
    private static final String WEBSHELL_ALERT_NAME = "WebShell攻击检测";
    private static final String WEBSHELL_ALERT_LEVEL = "HIGH";
    
    @Override
    public void flatMap(BaseEvent event, Collector<AlarmEvent> out) {
        try {
            // 只处理HTTP事件
            if (!(event instanceof HttpEvent)) {
                return;
            }
            
            HttpEvent httpEvent = (HttpEvent) event;
            
            // 检测WebShell特征
            if (isWebShellRequest(httpEvent)) {
                // 创建告警事件
                AlarmEvent alert = createAlertEvent(httpEvent);
                out.collect(alert);
                
                logger.warn("检测到WebShell访问: {} {}", httpEvent.getMethod(), httpEvent.getUri());
            }
            
        } catch (Exception e) {
            logger.error("WebShell检测错误: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 检测HTTP请求是否包含WebShell特征
     */
    private boolean isWebShellRequest(HttpEvent httpEvent) {
        // 1. 检查可疑的URI和参数
        String uri = httpEvent.getUri() != null ? httpEvent.getUri().toLowerCase() : "";
        String query = httpEvent.getUri() != null && httpEvent.getUri().contains("?") 
                ? httpEvent.getUri().substring(httpEvent.getUri().indexOf('?') + 1) 
                : "";
        
        // 常见WebShell特征
        String[] suspiciousPatterns = {
                "cmd=", "system(", "exec(", "passthru(", "shell_exec(",
                "eval(", "base64_", "assert(", "preg_replace(", "create_function(",
                "call_user_func(", "call_user_func_array(", "array_map(", "ob_start(",
                "phpinfo", "wscript.shell", "shell.application"
        };
        
        // 检查URI和查询参数
        for (String pattern : suspiciousPatterns) {
            if (uri.contains(pattern) || query.contains(pattern)) {
                return true;
            }
        }
        
        // 2. 检查请求头中的可疑内容
        if (httpEvent.getUserAgent() != null) {
            String userAgent = httpEvent.getUserAgent().toLowerCase();
            if (userAgent.contains("webshell") || userAgent.contains("hack") || userAgent.contains("exploit")) {
                return true;
            }
        }
        
        // 3. 检查请求体中的可疑内容
        if (httpEvent.getRequestBody() != null) {
            String requestBody = httpEvent.getRequestBody().toLowerCase();
            for (String pattern : suspiciousPatterns) {
                if (requestBody.contains(pattern)) {
                    return true;
                }
            }
        }
        
        return false;
    }
    
    /**
     * 创建WebShell告警事件
     */
    private AlarmEvent createAlertEvent(HttpEvent httpEvent) {
        AlarmEvent alert = new AlarmEvent();
        
        // 设置基本属性
        alert.setId(UUID.randomUUID().toString());
        alert.setType(WEBSHELL_ALERT_TYPE);
        alert.setName(WEBSHELL_ALERT_NAME);
        alert.setSeverity(WEBSHELL_ALERT_LEVEL);
        alert.setDescription("检测到可能的WebShell访问行为");
        alert.setSolution("1. 检查服务器上是否存在可疑文件\n2. 更新Web应用程序到最新版本\n3. 检查服务器日志以获取更多信息");
        
        // 设置源IP和端口
        alert.setSourceIp(httpEvent.getSourceIp());
        alert.setSourcePort(httpEvent.getSourcePort());
        alert.setDestinationIp(httpEvent.getDestinationIp());
        alert.setDestinationPort(httpEvent.getDestinationPort());
        
        // 设置时间戳
        alert.setTimestamp(System.currentTimeMillis());
        
        // 设置详细信息
        Map<String, Object> details = new HashMap<>();
        details.put("method", httpEvent.getMethod());
        details.put("uri", httpEvent.getUri());
        details.put("userAgent", httpEvent.getUserAgent());
        details.put("host", httpEvent.getHost());
        details.put("requestHeaders", httpEvent.getHeaders());
        details.put("requestBody", httpEvent.getRequestBody());
        alert.setDetails(details);
        
        return alert;
    }
    
    /**
     * 创建WebShell检测数据流
     * 整合基础WebShell检测、加密WebShell检测和非加密WebShell检测
     *
     * @param inputStream 输入数据流
     * @return WebShell告警数据流
     */
    public static DataStream<AlarmEvent> createWebShellDetectionStream(DataStream<BaseEvent> inputStream) {
        // 提取HTTP事件
        SingleOutputStreamOperator<HttpEvent> httpEvents = inputStream
                .filter(event -> event instanceof HttpEvent)
                .map(event -> (HttpEvent) event);
        
        // 基础WebShell检测
        DataStream<AlarmEvent> basicDetection = inputStream
                .flatMap(new WebShellDetector());
        
        // 加密WebShell检测
        DataStream<AlarmEvent> encryptedDetection = EncryptedWebShellDetector.detectEncryptedWebShell(httpEvents);
        
        // 非加密WebShell检测
        DataStream<AlarmEvent> unencryptedDetection = PlainTextWebShellDetector.detectUnencryptedWebShell(httpEvents);
        
        // 合并所有检测结果
        return basicDetection
                .union(encryptedDetection)
                .union(unencryptedDetection);
    }
}
