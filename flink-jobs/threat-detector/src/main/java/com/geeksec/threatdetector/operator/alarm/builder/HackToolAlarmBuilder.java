package com.geeksec.threatdetector.operator.alarm.builder;

import com.alibaba.fastjson.JSONObject;
import org.apache.flink.types.Row;
import redis.clients.jedis.Jedis;

/**
 * Builder for hack tool related alarms.
 * <AUTHOR>
 */
public class HackToolAlarmBuilder extends BaseAlarmBuilder {
    
    private static final HackToolAlarmBuilder INSTANCE = new HackToolAlarmBuilder();
    
    private HackToolAlarmBuilder() {}
    
    public static HackToolAlarmBuilder getInstance() {
        return INSTANCE;
    }
    
    @Override
    public JSONObject getAlarmJson(Row alarmRow, Jedis jedis) {
        JSONObject alarmJson = createBaseAlarmJson("黑客工具告警", null);
        
        // Extract fields from the Row and add to the JSON object
        alarmJson.put("source_ip", getStringFieldSafely(alarmRow, 2));
        alarmJson.put("source_port", getStringFieldSafely(alarmRow, 3));
        alarmJson.put("destination_ip", getStringFieldSafely(alarmRow, 4));
        alarmJson.put("destination_port", getStringFieldSafely(alarmRow, 5));
        alarmJson.put("tool_name", getStringFieldSafely(alarmRow, 6));
        alarmJson.put("timestamp", getStringFieldSafely(alarmRow, 7));
        
        return alarmJson;
    }
    
    /**
     * Static helper method to build a hack tool alarm JSON.
     * @param alarmRow The input Row containing alarm data
     * @param jedis Redis client for additional data lookup
     * @return JSONObject containing the alarm data
     */
    public static JSONObject buildHackToolAlarmJson(Row alarmRow, Jedis jedis) {
        return getInstance().getAlarmJson(alarmRow, jedis);
    }
}
