package com.geeksec.threatdetector.util.constant;

/**
 * <AUTHOR>
 * 配置常量类
 */
public final class ConfigConstants {

    private ConfigConstants() {
        // 防止实例化
    }

    /** ES任务ID配置键 */
    public static final String ES_TASK_ID = "es.task.id";

    /** ES批次ID配置键 */
    public static final String ES_BATCH_ID = "es.batch.id";

    /** ES WebSocket主机配置键 */
    public static final String ES_WS_HOST = "es.ws.host";

    /** Kafka引导服务器配置键 */
    public static final String KAFKA_BOOTSTRAP_SERVERS = "kafka.bootstrap.servers";

    /** Kafka消费者组ID配置键 */
    public static final String KAFKA_GROUP_ID = "kafka.group.id";

    /** Kafka主题配置键 */
    public static final String KAFKA_TOPIC = "kafka.topic";

    /** 检查点目录配置键 */
    public static final String CHECKPOINT_DIR = "checkpoint.dir";

    /** 配置描述符配置键 */
    public static final String CONFIG_DESCRIPTOR = "config.descriptor";

    // MySQL数据库相关配置常量
    /** MySQL数据库主机配置键 */
    public static final String MYSQL_DATABASE_HOST = "mysql.database.host";

    /** MySQL数据库用户名配置键 */
    public static final String MYSQL_DATABASE_USER = "mysql.database.user";

    /** MySQL数据库密码配置键 */
    public static final String MYSQL_DATABASE_PASSWORD = "mysql.database.password";

    // Elasticsearch相关配置常量
    /** Elasticsearch主机配置键 */
    public static final String ELASTICSEARCH_HOST = "elasticsearch.host";

    /** Elasticsearch端口配置键 */
    public static final String ELASTICSEARCH_PORT = "elasticsearch.port";

    // Nebula Graph相关配置常量
    /** Nebula Graph地址配置键 */
    public static final String NEBULA_GRAPH_ADDR = "nebula.graph.addr";

    /** Nebula Meta地址配置键 */
    public static final String NEBULA_META_ADDR = "nebula.meta.addr";

    /** Nebula空间名称配置键 */
    public static final String NEBULA_SPACE_NAME = "nebula.space.name";

    // HBase相关配置常量
    /** HBase Quorum配置键 */
    public static final String HBASE_QUORUM = "hbase.quorum";

    /** HBase客户端端口配置键 */
    public static final String HBASE_CLIENT_PORT = "hbase.client.port";

    /** HBase Master配置键 */
    public static final String HBASE_MASTER = "hbase.master";

    // Kafka输出相关配置常量
    /** Kafka输出主题配置键 */
    public static final String KAFKA_OUTPUT_TOPIC = "kafka.output.topic";
}
