package com.geeksec.threatdetector.operator;

import org.apache.flink.api.common.eventtime.Watermark;
import org.apache.flink.api.common.eventtime.WatermarkGenerator;
import org.apache.flink.api.common.eventtime.WatermarkOutput;
import org.apache.flink.types.Row;

/**
 * Generates watermarks for {@link Row} elements based on event time.
 * This generator assumes that timestamps are monotonically increasing per parallel instance.
 *
 * <AUTHOR>
 * @Date 2022/11/4
 */
public class EventTimeRowWatermarkGenerator implements WatermarkGenerator<Row> {
    private long maxTimestamp;
    private long outOfOrdernessDelay;
    @Override
    public void onEvent(Row row, long eventTimestamp, WatermarkOutput watermarkOutput) {
        // row 参数在此实现中未使用，但保留以符合接口定义
        // watermarkOutput 参数在此实现中未使用，因为水印是在 onPeriodicEmit 中发出的
        this.maxTimestamp = Math.max(this.maxTimestamp, eventTimestamp);
    }

    @Override
    public void onPeriodicEmit(WatermarkOutput watermarkOutput) {
        // Emit the watermark as current highest timestamp minus the out-of-orderness delay
        watermarkOutput.emitWatermark(new Watermark(this.maxTimestamp - this.outOfOrdernessDelay));
    }

    public void setOutOfOrdernessDelay(long outOfOrdernessDelay) {
        this.outOfOrdernessDelay = outOfOrdernessDelay;
    }
}
