package com.geeksec.threatdetector.io.sink;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.common.config.ConfigurationManager;
import com.geeksec.threatdetector.common.constant.ConfigConstants;
import com.geeksec.threatdetector.common.util.EsUtils;
import org.apache.commons.pool.impl.GenericObjectPool;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.support.IndicesOptions;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.action.update.UpdateResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/12/7
 */

public class SessionSink extends RichSinkFunction<JSONObject> {
    private final static Logger logger = LoggerFactory.getLogger(SessionSink.class);
    private static final ParameterTool CONFIG = ConfigurationManager.getConfig();
    public static final String WS_HOST = CONFIG.get(ConfigConstants.ES_WS_HOST);
    public static final String ES_HOST = CONFIG.get(ConfigConstants.ELASTICSEARCH_HOST);
    public static final String ES_PORT = CONFIG.get(ConfigConstants.ELASTICSEARCH_PORT);
    // es连接客户端
    private static transient GenericObjectPool<RestHighLevelClient> EsPool = null;
    private List<JSONObject> session_label_list = new ArrayList<>(20);
    private Integer alarm_time = 0;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        // ES 初始化
        EsPool = EsUtils.initEsPool();
        logger.info("生成EsPool成功! {}", EsPool.getNumIdle(), EsPool.hashCode());
    }

    @Override
    public void invoke(JSONObject Json_Info, Context context) throws Exception {
        session_label_list.add(Json_Info);
        if (session_label_list.size() == 1) {
            alarm_time = Integer.valueOf((String) Json_Info.get("Time"));
        }
        Integer now_time = Integer.valueOf((String) Json_Info.get("Time"));
        int time_delay = now_time - alarm_time;
        //当缓存的数据超过10条或者时间延迟过去60s，就批量写入会话标签
        if (session_label_list.size() > 10 || time_delay >= 60) {
            JSONObject send_json = new JSONObject();
            send_json.put("type", "DAN_TAG_ES_SESSION_INSERT");
            send_json.put("Bulk", session_label_list);
//            HttpUtils.sendPost_only(WS_HOST,send_json);
            updadeDoc(session_label_list);
//            JSONObject responseData = HttpUtils.sendPost(WS_HOST,send_json);
//            logger.info("写入ES返回结果{}",responseData);
            logger.info("批量写入ES——会话——，写入数量——{}——，间隔时间——{}s——", session_label_list.size(), time_delay);
            session_label_list.clear();
        }
    }

    @Override
    public void close() throws Exception {
        super.close();
    }

    public void updadeDoc(List<JSONObject> jsonObjectList) throws Exception {
        Map<String, Set<Integer>> stringSetMap = removeDuplicates(jsonObjectList);
        // 以会话ID为条件，查询出当前需要修改目标ES元数据信息
        // 验证该URL是否被访问过
        RestHighLevelClient client = null;
        try {
            client = EsUtils.getClient(EsPool);
            LocalDate currentDate = LocalDate.now();
            DateTimeFormatter anotherFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            String anotherFormattedDate = currentDate.format(anotherFormatter);
            // 创建更新请求
            for (String key : stringSetMap.keySet()) {
                // 以ID为条件，查询出当前需要修改目标ES元数据信息
                String[] split = key.split("&");
                String esKey = split[0];
                String SessionId = split[1];
//                boolQueryBuilder.must(QueryBuilders.termQuery("SessionId", SessionId));
//                searchSourceBuilder.query(boolQueryBuilder);
                int hitsFlag = 0;
                while (hitsFlag < 20) {
                    hitsFlag = hitsFlag + 1;
                    SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
                    searchSourceBuilder.query(QueryBuilders.boolQuery()
                            .must(QueryBuilders.termQuery("SessionId", SessionId)));

                    SearchRequest searchRequest = new SearchRequest("connectinfo_"+esKey.split("_")[1]+"_"+esKey.split("_")[2]+"_"+anotherFormattedDate+"_100000001");
                    searchRequest.indicesOptions(IndicesOptions.fromOptions(true, true, true, false));
                    searchRequest.source(searchSourceBuilder);

                    SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);
                    SearchHit[] hits = searchResponse.getHits().getHits();
                    logger.info("查询ES数据长度：{}", hits.length);
                    if (hits.length > 0) {
                        Map<String, Object> esDataMap = hits[0].getSourceAsMap();
                        String esId = hits[0].getId();

                        // 将labels字段转换为列表，如果它还不是列表
                        List<Integer> labels = (List<Integer>) esDataMap.get("Labels");
                        if (labels == null) {
                            labels = new ArrayList<>();
                        }
                        // 在labels字段中添加新的值
                        Set<Integer> addLabels = stringSetMap.get(key);
                        for (Integer addLabel : addLabels) {
                            if (!labels.contains(addLabel)){
                                labels.add(addLabel);
                            }
                        }
                        esDataMap.put("Labels", labels);
                        // 构建更新请求
                        UpdateRequest updateRequest = new UpdateRequest("connectinfo_"+esKey.split("_")[1]+"_"+esKey.split("_")[2]+"_"+anotherFormattedDate+"_100000001", "_doc", esId);
                        updateRequest.fetchSource(true);
                        updateRequest.doc(esDataMap);
                        updateRequest.detectNoop(true); // 如果文档没有变化，则不执行更新
                        // 执行更新操作
                        UpdateResponse updateResponse = client.update(updateRequest, RequestOptions.DEFAULT);
                        Thread.sleep(100);
                        logger.info("Document " + esId + " SessionId: " + SessionId + " EsKey:" + esKey + " TagId:" + stringSetMap.get(key).toString() + " Labels:" + labels.toString() + " updated status:" + updateResponse.status());
                        hitsFlag = 21;
                    }else {
                        Thread.sleep(5000);
                    }
                }
//                logger.info("Document " + esId + " updated: " + updateResponse.toString());
            }
        } catch (Exception e) {
            logger.error("获取EsClient失败，error--->{}", e);
        } finally {
            if (client != null) {
                EsUtils.returnClient(client, EsPool);
            }
        }
    }

    private Map<String, Set<Integer>> removeDuplicates(List<JSONObject> list) {
        Map<String, Set<Integer>> map = new HashMap<>();
        for (JSONObject jsonObject : list) {
            String targetName = jsonObject.getString("TargetName");
            String esKey = jsonObject.getString("es_key");
            Set<String> tagid = (Set<String>) jsonObject.get("TagId");
            Set<Integer> integerSet = tagid.stream()
                    .map(Integer::parseInt) // 将每个字符串转换为整数
                    .collect(Collectors.toSet());
            if (map.get(esKey + "&" + targetName) != null) {
                map.get(esKey + "&" + targetName).addAll(integerSet);
            } else {
                Set<Integer> tagList = new HashSet<>(integerSet);
                map.put(esKey + "&" + targetName, tagList);
            }
        }
        logger.info("去重后数据：{}", map);
        return map;
    }
}
