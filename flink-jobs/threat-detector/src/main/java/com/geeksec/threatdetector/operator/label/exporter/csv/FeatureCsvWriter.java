package com.geeksec.threatdetector.operator.label.exporter.csv;

import java.io.BufferedWriter;
import java.io.FileNotFoundException;
import java.io.FileWriter;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 特征数据CSV写入器，用于将特征数据写入CSV文件
 *
 * <AUTHOR>
 * <AUTHOR>
 * @since 2022/12/27
 */
public class FeatureCsvWriter {

    /**
     * 将特征数据写入CSV文件
     *
     * @param featureMap 特征数据映射
     */
    public static void writeFeatureToCsv(Map<String, Object> featureMap) {
        List<String> feature_list = Arrays.asList("key","cnt","NX_percent","distinct_que_type_cnt",
                "que_A_percent","que_AAAA_percent","que_CNAME_percent","que_MX_percent",
                "que_NSEC_percent","que_PTR_percent","que_TXT_percent","que_NS_percent",
                "ttl_avg","pre_entropy_mean","pre_cnt","pre_entropy","domain_session_std",
                "ans_entropy_mean","ans_entropy","ans_session_std","start_time","end_time");
        try {
            BufferedWriter write_csv = new BufferedWriter(new FileWriter("white_dns_servers.csv",true));
//            BufferedWriter write_csv = new BufferedWriter(new FileWriter("black_dns_servers.csv",true));
            String write_String = "";
            for (String feature:feature_list){
                if (feature.equals("key")){
                    write_String+=featureMap.get(feature).toString();
                }else {
                    write_String=write_String+","+featureMap.get(feature).toString();
                }
            }
            write_csv.write(write_String);
            write_csv.newLine();
            write_csv.flush();
            write_csv.close();
        } catch (FileNotFoundException e) {
            System.out.println("没有找到指定文件");
        } catch (IOException e) {
            System.out.println("文件读写出错");
        }
    }
}
