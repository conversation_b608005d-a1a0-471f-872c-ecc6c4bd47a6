package com.geeksec.threatdetector.io.sink;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.common.config.ConfigurationManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import org.graylog2.syslog4j.Syslog;
import org.graylog2.syslog4j.SyslogConfigIF;
import org.graylog2.syslog4j.SyslogIF;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/6/5
 */
@Slf4j
public class SyslogOutputSink extends RichSinkFunction<JSONObject>{
    private transient SyslogIF syslog;
    private final Map<String,Object> syslogConfig;

    /**
     * 获取当前环境配置
     */
    private static final ParameterTool CONFIG = ConfigurationManager.getConfig(); // CONFIG 未在此类中使用，但暂时保留
    private final String outputSyslogHost;
    private final int outputPort;

    public SyslogOutputSink(Map<String, Object> objectMap) {
        this.syslogConfig = objectMap;
        this.outputSyslogHost = (String) this.syslogConfig.get("ip");
        this.outputPort = Integer.parseInt((String) this.syslogConfig.get("port"));
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        this.syslog = Syslog.getInstance("udp");
        SyslogConfigIF syslogConfigIF = syslog.getConfig();
        syslogConfigIF.setHost(this.outputSyslogHost);
        syslogConfigIF.setPort(this.outputPort);
        syslogConfigIF.setFacility(SyslogIF.FACILITY_USER);
        syslogConfigIF.setLocalName("gk_alarm");
        syslogConfigIF.setSendLocalTimestamp(false);
        super.open(parameters);
    }



    @Override
    public void invoke(JSONObject value, Context context) throws Exception {
        log.info("Sending to syslog: {}", value.toString()); // 移除了 Level.WARN，因为SLF4J的info级别通常不接受Log4j的Level对象。如果需要特定级别，应配置SLF4J后端。
        SyslogUtils.send_msg(value.toString());
    }

    @Override
    public void close() throws Exception {
        super.close();
        syslog.shutdown();
    }

}
