package com.geeksec.threatdetector.model.protocol;

import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/9/9
 *
 */

@Data
public class HttpSimpleInfo {
    /**
     * Url
     */
    private String url;

    /**
     * Act,请求类型
     */
    private String act;

    /**
     * Host
     */
    private String host;

    /**
     * sHTTPFinger
     */
    private String sHttpFinger;

    /**
     * dHTTPFinger
     */
    private String dHttpFinger;

    /**
     * clientInfoMap, 请求头
     */
    private Map<String, Object> clientInfoMap;

    /**
     * serverInfoMap, 响应头
     */
    private Map<String, Object> serverInfoMap;

    public HttpSimpleInfo(Map<String,Object> pbMap) {
        this.url = (String) pbMap.get("Url");
        this.act = (String) pbMap.get("Act");
        this.host = (String) pbMap.get("Host");
        this.sHttpFinger = (String) pbMap.get("sHttpFinger");
        this.dHttpFinger = (String) pbMap.get("dHTTPFinger");
        this.clientInfoMap = (Map<String, Object>) pbMap.get("Client");
        this.serverInfoMap = (Map<String, Object>) pbMap.get("Server");
    }
}
