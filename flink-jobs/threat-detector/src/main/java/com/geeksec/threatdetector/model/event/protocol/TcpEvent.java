package com.geeksec.threatdetector.model.event.protocol;

/**
 * TCP事件模型
 *
 * <AUTHOR>
 */
public class TcpEvent extends ProtocolEvent {
    
    /**
     * TCP标志
     */
    private String flags;
    
    /**
     * 序列号
     */
    private Long sequenceNumber;
    
    /**
     * 确认号
     */
    private Long acknowledgmentNumber;
    
    /**
     * 窗口大小
     */
    private Integer windowSize;
    
    /**
     * 紧急指针
     */
    private Integer urgentPointer;
    
    /**
     * 选项
     */
    private String options;
    
    /**
     * 数据偏移
     */
    private Integer dataOffset;
    
    /**
     * 是否建立连接
     */
    private Boolean established;
    
    /**
     * 重传次数
     */
    private Integer retransmissionCount;
    
    /**
     * 往返时间(RTT)
     */
    private Long roundTripTime;

    public String getFlags() {
        return flags;
    }

    public void setFlags(String flags) {
        this.flags = flags;
    }

    public Long getSequenceNumber() {
        return sequenceNumber;
    }

    public void setSequenceNumber(Long sequenceNumber) {
        this.sequenceNumber = sequenceNumber;
    }

    public Long getAcknowledgmentNumber() {
        return acknowledgmentNumber;
    }

    public void setAcknowledgmentNumber(Long acknowledgmentNumber) {
        this.acknowledgmentNumber = acknowledgmentNumber;
    }

    public Integer getWindowSize() {
        return windowSize;
    }

    public void setWindowSize(Integer windowSize) {
        this.windowSize = windowSize;
    }

    public Integer getUrgentPointer() {
        return urgentPointer;
    }

    public void setUrgentPointer(Integer urgentPointer) {
        this.urgentPointer = urgentPointer;
    }

    public String getOptions() {
        return options;
    }

    public void setOptions(String options) {
        this.options = options;
    }

    public Integer getDataOffset() {
        return dataOffset;
    }

    public void setDataOffset(Integer dataOffset) {
        this.dataOffset = dataOffset;
    }

    public Boolean getEstablished() {
        return established;
    }

    public void setEstablished(Boolean established) {
        this.established = established;
    }

    public Integer getRetransmissionCount() {
        return retransmissionCount;
    }

    public void setRetransmissionCount(Integer retransmissionCount) {
        this.retransmissionCount = retransmissionCount;
    }

    public Long getRoundTripTime() {
        return roundTripTime;
    }

    public void setRoundTripTime(Long roundTripTime) {
        this.roundTripTime = roundTripTime;
    }
    
    /**
     * 开始时间
     */
    private Long startTime;
    
    /**
     * 结束时间
     */
    private Long endTime;
    
    /**
     * 数据包数量
     */
    private Integer packetCount;
    
    /**
     * 字节数
     */
    private Long byteCount;
    
    public Long getStartTime() {
        return startTime;
    }
    
    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }
    
    public Long getEndTime() {
        return endTime;
    }
    
    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }
    
    public Integer getPacketCount() {
        return packetCount;
    }
    
    public void setPacketCount(Integer packetCount) {
        this.packetCount = packetCount;
    }
    
    public Long getByteCount() {
        return byteCount;
    }
    
    public void setByteCount(Long byteCount) {
        this.byteCount = byteCount;
    }
    
    /**
     * 获取负载数据的字节数组
     * 
     * @return 负载数据的字节数组
     */
    public byte[] getPayloadBytes() {
        if (getPayload() == null) {
            return new byte[0];
        }
        return getPayload().getBytes();
    }


    // Helper methods for TCP flags
    public boolean isSyn() {
        String flags = getFlags();
        return flags != null && flags.contains("SYN");
    }

    public boolean isAck() {
        String flags = getFlags();
        return flags != null && flags.contains("ACK");
    }

    public boolean isFin() {
        String flags = getFlags();
        return flags != null && flags.contains("FIN");
    }

    public boolean isRst() {
        String flags = getFlags();
        return flags != null && flags.contains("RST");
    }

    public boolean isPsh() {
        String flags = getFlags();
        return flags != null && flags.contains("PSH");
    }

    public boolean isUrg() {
        String flags = getFlags();
        return flags != null && flags.contains("URG");
    }

    @Override
    public String toString() {
        return "TcpEvent{" +
                "flags='" + flags + '\'' +
                ", sequenceNumber=" + sequenceNumber +
                ", acknowledgmentNumber=" + acknowledgmentNumber +
                ", windowSize=" + windowSize +
                ", sourceIp='" + getSourceIp() + '\'' +
                ", destinationIp='" + getDestinationIp() + '\'' +
                '}';
    }
}
