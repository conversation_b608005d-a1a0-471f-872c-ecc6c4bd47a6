package com.geeksec.threatdetector.operator.alarm.builder;

import com.alibaba.fastjson.JSONObject;
import org.apache.flink.types.Row;
import redis.clients.jedis.Jedis;

/**
 * Builder for DNS mining-related alarms.
 * <AUTHOR>
 */
public class DnsMineAlarmBuilder extends BaseAlarmBuilder {
    
    private static final DnsMineAlarmBuilder INSTANCE = new DnsMineAlarmBuilder();
    
    private DnsMineAlarmBuilder() {}
    
    public static DnsMineAlarmBuilder getInstance() {
        return INSTANCE;
    }
    
    @Override
    public JSONObject getAlarmJson(Row alarmRow, Jedis jedis) {
        JSONObject alarmJson = createBaseAlarmJson("DNS挖矿检测", null);
        
        // Extract fields from the Row and add to the JSON object
        // The actual field indices should be adjusted based on the actual data structure
        alarmJson.put("source_ip", getStringFieldSafely(alarmRow, 2));
        alarmJson.put("source_port", getStringFieldSafely(alarmRow, 3));
        alarmJson.put("destination_ip", getStringFieldSafely(alarmRow, 4));
        alarmJson.put("destination_port", getStringFieldSafely(alarmRow, 5));
        alarmJson.put("protocol", getStringFieldSafely(alarmRow, 6));
        alarmJson.put("timestamp", getStringFieldSafely(alarmRow, 7));
        
        // Add DNS-specific fields
        alarmJson.put("query", getStringFieldSafely(alarmRow, 8));
        alarmJson.put("answer", getStringFieldSafely(alarmRow, 9));
        alarmJson.put("query_type", getStringFieldSafely(alarmRow, 10));
        
        return alarmJson;
    }
    
    /**
     * Static helper method to build a DNS mine alarm JSON.
     * @param alarmRow The input Row containing alarm data
     * @param jedis Redis client for additional data lookup
     * @return JSONObject containing the alarm data
     */
    public static JSONObject buildDnsMineAlarmJson(Row alarmRow, Jedis jedis) {
        return getInstance().getAlarmJson(alarmRow, jedis);
    }
}
