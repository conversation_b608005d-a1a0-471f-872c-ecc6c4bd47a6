package com.geeksec.threatdetector.operator.analysis.encrypted;

import com.geeksec.threatdetector.model.event.alarm.AlarmEvent;
import com.geeksec.threatdetector.model.event.protocol.SslEvent;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * CobaltStrike框架检测函数
 *
 * <AUTHOR>
 */
/**
 * CobaltStrike框架检测器
 * 用于检测CobaltStrike C2框架的SSL/TLS流量特征
 *
 * <AUTHOR>
 * @since 2023/01/01
 */
public class CobaltStrikeDetector extends RichFlatMapFunction<SslEvent, AlarmEvent> {
    
    private static final Logger logger = LoggerFactory.getLogger(CobaltStrikeDetector.class);
    private static final String COBALTSTRIKE_ALERT_TYPE = "COBALTSTRIKE_DETECTED";
    private static final String COBALTSTRIKE_ALERT_NAME = "CobaltStrike框架检测";
    private static final String COBALTSTRIKE_ALERT_LEVEL = "HIGH";
    
    // CobaltStrike特征证书序列号
    private static final String CS_SERIAL_NUMBER = "8bb00ee";
    
    @Override
    public void flatMap(SslEvent sslEvent, Collector<AlarmEvent> out) throws Exception {
        try {
            // 检测CobaltStrike特征
            if (isCobaltStrike(sslEvent)) {
                // 创建告警事件
                AlarmEvent alert = createAlertEvent(sslEvent);
                out.collect(alert);
                
                logger.warn("检测到CobaltStrike框架: {}:{} -> {}:{}", 
                        sslEvent.getSourceIp(), sslEvent.getSourcePort(),
                        sslEvent.getDestinationIp(), sslEvent.getDestinationPort());
            }
        } catch (Exception e) {
            logger.error("CobaltStrike检测错误: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 检测是否为CobaltStrike框架
     */
    private boolean isCobaltStrike(SslEvent sslEvent) {
        Map<String, String> certificate = sslEvent.getCertificate();
        if (certificate == null) {
            return false;
        }
        
        // 检查证书序列号
        String serialNumber = certificate.get("serialNumber");
        if (!CS_SERIAL_NUMBER.equals(serialNumber)) {
            return false;
        }
        
        // 检查证书其他字段是否为空
        String country = certificate.get("country");
        String state = certificate.get("state");
        String organization = certificate.get("organization");
        String organizationalUnit = certificate.get("organizationalUnit");
        String commonName = certificate.get("commonName");
        String locality = certificate.get("locality");
        
        // CobaltStrike的证书通常这些字段都是空的
        return isEmpty(country) && isEmpty(state) && isEmpty(organization)
                && isEmpty(organizationalUnit) && isEmpty(commonName) && isEmpty(locality);
    }
    
    /**
     * 检查字符串是否为空
     */
    private boolean isEmpty(String str) {
        return str == null || str.isEmpty();
    }
    
    /**
     * 创建CobaltStrike告警事件
     */
    private AlarmEvent createAlertEvent(SslEvent sslEvent) {
        AlarmEvent alert = new AlarmEvent();
        
        // 设置基本属性
        alert.setId(UUID.randomUUID().toString());
        alert.setType(COBALTSTRIKE_ALERT_TYPE);
        alert.setName(COBALTSTRIKE_ALERT_NAME);
        alert.setSeverity(COBALTSTRIKE_ALERT_LEVEL);
        alert.setDescription("检测到可能的CobaltStrike框架通信行为");
        alert.setSolution("1. 立即隔离涉及的主机\n2. 检查源IP和目标IP之间的SSL通信\n3. 分析系统是否存在其他入侵迹象");
        
        // 设置源IP和端口
        alert.setSourceIp(sslEvent.getSourceIp());
        alert.setSourcePort(sslEvent.getSourcePort());
        alert.setDestinationIp(sslEvent.getDestinationIp());
        alert.setDestinationPort(sslEvent.getDestinationPort());
        
        // 设置时间戳
        alert.setTimestamp(System.currentTimeMillis());
        
        // 设置详细信息
        Map<String, Object> details = new HashMap<>();
        details.put("sslVersion", sslEvent.getSslVersion());
        details.put("cipherSuite", sslEvent.getCipherSuite());
        details.put("certificate", sslEvent.getCertificate());
        details.put("clientFingerprint", sslEvent.getClientFingerprint());
        details.put("serverFingerprint", sslEvent.getServerFingerprint());
        details.put("serialNumber", sslEvent.getCertificate() != null ? sslEvent.getCertificate().get("serialNumber") : null);
        alert.setDetails(details);
        
        return alert;
    }
    
    /**
     * 创建CobaltStrike检测数据流
     */
    public static DataStream<AlarmEvent> detectCobaltStrike(DataStream<SslEvent> sslEvents) {
        return sslEvents
                .flatMap(new CobaltStrikeDetector())
                .name("CobaltStrike框架检测")
                .setParallelism(2);
    }
}
