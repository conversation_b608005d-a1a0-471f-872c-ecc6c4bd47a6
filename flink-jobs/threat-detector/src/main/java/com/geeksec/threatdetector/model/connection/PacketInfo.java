package com.geeksec.threatdetector.model.connection;

import lombok.extern.slf4j.Slf4j;

/**
 * 记录一个会话中的一个单包的信息。
 *
 * @param direction 方向：0:客户端到服务端, 1:服务端到客户端
 * @param byteNum 单包的字节数 (原注释：单包的包数量都是1，此字段为包大小)
 * @param singleTime 本次单包的时间 (秒级时间戳)
 * @param nSingleTime 本次单包的时间 (纳秒部分或高精度时间戳的纳秒部分)
 * @param count 该包在会话中的序列位置
 * <AUTHOR>
 */
@Slf4j
public record PacketInfo(
    int direction,
    int byteNum,
    int singleTime,
    int nSingleTime,
    int count
) {
}
