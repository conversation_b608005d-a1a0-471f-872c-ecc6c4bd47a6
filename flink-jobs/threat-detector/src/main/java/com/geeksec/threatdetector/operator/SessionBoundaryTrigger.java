package com.geeksec.threatdetector.operator;

import lombok.extern.slf4j.Slf4j;
import org.apache.flink.streaming.api.windowing.triggers.Trigger;
import org.apache.flink.streaming.api.windowing.triggers.TriggerResult;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;

import java.util.Map;

/**
 * A Flink {@link org.apache.flink.streaming.api.windowing.triggers.Trigger} that fires when a specific "connect" event is received,
 * signifying a session boundary. It can also fire based on event time reaching the window's max timestamp.
 * This trigger is used to process session data in Flink's windowing operations.
 *
 * <AUTHOR>
 * @Date 2024/09/05
 */
@Slf4j
public class SessionBoundaryTrigger extends Trigger<Map<String, Object>, TimeWindow> {

    private static final long serialVersionUID = 1L;

    public static final String CONNECTION_END_TYPE = "connect";
    public static final String SSL_TYPE = "ssl";
    public static final String HTTP_TYPE = "http";

    @Override
    public TriggerResult onElement(Map<String, Object> pbMap, long l, TimeWindow timeWindow, TriggerContext triggerContext) throws Exception {
        String type = (String) pbMap.get("type");
        if (CONNECTION_END_TYPE.equals(type)){
            log.debug("Connect event received for window {}, firing and purging.", timeWindow);
            return TriggerResult.FIRE_AND_PURGE;
        } else {
            log.debug("Non-connect event ({}) received for window {}, continuing.", type, timeWindow);
            return TriggerResult.CONTINUE;
        }
    }

    @Override
    public TriggerResult onProcessingTime(long l, TimeWindow timeWindow, TriggerContext triggerContext) throws Exception {
        return TriggerResult.CONTINUE;
    }

    @Override
    public TriggerResult onEventTime(long l, TimeWindow timeWindow, TriggerContext triggerContext) throws Exception {
        // 通常在事件时间处理中，我们可能会根据水印决定是否触发
        // 但在这个特定场景下，主要依赖 onElement 中的 connect 事件
        // 如果窗口结束时间到达且水印已通过，也可以触发
        if (l == timeWindow.maxTimestamp()) {
            log.debug("Event time reached max timestamp for window {}, firing and purging.", timeWindow);
            return TriggerResult.FIRE_AND_PURGE;
        }
        return TriggerResult.CONTINUE;
    }

    @Override
    public void clear(TimeWindow timeWindow, TriggerContext ctx) throws Exception {
        // 清理操作，例如清除与此窗口相关的任何状态
        // 在这个简单的触发器中，可能不需要特别的清理逻辑，因为 FIRE_AND_PURGE 会处理窗口状态
        log.debug("Clearing trigger for window {}.", timeWindow);
    }
}
