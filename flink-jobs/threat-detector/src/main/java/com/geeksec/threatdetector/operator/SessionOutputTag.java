package com.geeksec.threatdetector.operator;

import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

/**
 * Defines a collection of {@link OutputTag} instances used for routing different types of session-related data in Flink jobs.
 * Each tag represents a specific category of session information, such as web login, various tool activities, or tunnel types.
 *
 * <AUTHOR>
 * @Date 2022/12/29
 */

public class SessionOutputTag {
    public static final OutputTag<Row> SESSION_HTTP_WEB_LOGIN_INFO = new OutputTag<>("SESSION_HTTP_WEB_LOGIN_INFO", TypeInformation.of(Row.class));
    public static final OutputTag<Row> SESSION_NEOREGEO_INFO = new OutputTag<>("SESSION_NEOREGEO_INFO", TypeInformation.of(Row.class));

    public static final OutputTag<Row> SESSION_SUO5_INFO = new OutputTag<>("SESSION_SUO5_INFO", TypeInformation.of(Row.class));
    public static final OutputTag<Row> SESSION_BEHINDER_INFO = new OutputTag<>("SESSION_BEHINDER_INFO", TypeInformation.of(Row.class));

    public static final OutputTag<Row> SESSION_RANDOM_FINGER_INFO = new OutputTag<>("SESSION_RANDOM_FINGER_INFO", TypeInformation.of(Row.class));

    public static final OutputTag<Row> SESSION_TUNNEL_INFO = new OutputTag<>("SESSION_TUNNEL_INFO", TypeInformation.of(Row.class));
    public static final OutputTag<Row> SESSION_DNS_TUNNEL_INFO = new OutputTag<>("SESSION_DNS_TUNNEL_INFO", TypeInformation.of(Row.class));
    public static final OutputTag<Row> SESSION_SRCPC2_INFO = new OutputTag<>("SESSION_SRCPC2_INFO", TypeInformation.of(Row.class));
    public static final OutputTag<Row> SESSION_URCP_INFO = new OutputTag<>("SESSION_URCP_INFO", TypeInformation.of(Row.class));
    public static final OutputTag<Row> SESSION_TODESK_INFO = new OutputTag<>("SESSION_TODESK_INFO", TypeInformation.of(Row.class));
    public static final OutputTag<Row> SESSION_WEB_SHELL_INFO = new OutputTag<>("SESSION_WEB_SHELL_INFO", TypeInformation.of(Row.class));
    public static final OutputTag<Row> SESSION_ENCRYPTED_TOOL_INFO = new OutputTag<>("SESSION_ENCRYPTED_TOOL_INFO", TypeInformation.of(Row.class));
    public static final OutputTag<Row> SESSION_RAT_INFO = new OutputTag<>("SESSION_RAT_INFO", TypeInformation.of(Row.class));
}
