package com.geeksec.threatdetector.operator.analysis.webshell;

import com.geeksec.threatdetector.common.enums.ThreatTypeEnum;
import com.geeksec.threatdetector.model.event.alarm.AlarmEvent;
import com.geeksec.threatdetector.model.event.protocol.HttpEvent;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 非加密webshell协议识别
 *
 * <AUTHOR>
 */
/**
 * 明文WebShell检测器
 * 用于检测非加密的WebShell攻击流量特征
 *
 * <AUTHOR>
 * @since 2023/01/01
 */
public class PlainTextWebShellDetector extends RichFlatMapFunction<HttpEvent, AlarmEvent> {
    private static final Logger logger = LoggerFactory.getLogger(PlainTextWebShellDetector.class);
    static final String K8_STR = "K8=%40eval%01%28base64_decode%28%24_POST%5Bz0%5D%29%29%3B";
    static final String CNCK_STR = "cmd=%40eval%01%28base64_decode%28%24_POST%5Bz0%5D%29%29%3B";
    static final String KAISHANFU_STR = "pass=@eval(base64_decode($_POST[z0]));";
    static final String ANT_SWORD_STR = "chopper=function%20HexAsciiConvert(hex%3AString)%20%7Bvar%20sb%3";
    static final String ALTMAN_STR = "caidao=%40ini_set(%22display_errors%22%2c%220%22)%3b%0d%0a%40set";
    static final String SNIPER_STR = "siDpnjjJ=%40ob_start%28%27ob_gzip%27%29%3Becho";
    static final String WEBSHELLMANAGER_STR = "pass=%40eval%01%28base64_decode%28%24_POST%5Bz0%5D%29%29%3B";
    static final String CKNIFE_STR = "Cknife=@eval\u0001(base64_decode($_POST[z0]));";
    static final String WEBKNIFE_STR = "pass=eval%28base64_decode%28%27";
    static final String QUASIBOT_STR1 = "<!--{:|Linux\n{:|2040c5270dacc9281ee43f2ecd3d3e89{:|Linux{:|-->";
    static final String QUASIBOT_STR2 = "<br />\n<font size='1'><table class='xdebug-error xe-notice' dir=";
    static final String HATCHET_STR = "pass=@eval(stripcslashes(base64_decode(stripcslashes($_POST[z0])";

    @Override
    public void flatMap(HttpEvent httpEvent, Collector<AlarmEvent> collector) throws Exception {
        try {
            // 检测基于负载的非加密WebShell
            if (detectPayloadWebShell(httpEvent)) {
                AlarmEvent alert = createAlertEvent(httpEvent, "UNENCRYPTED_WEBSHELL", "非加密WebShell检测", "HIGH");
                collector.collect(alert);
                logger.warn("检测到非加密WebShell访问: {} {}", httpEvent.getMethod(), httpEvent.getUri());
            }
            
            // 检测基于URL的非加密WebShell
            if (detectUrlWebShell(httpEvent)) {
                AlarmEvent alert = createAlertEvent(httpEvent, "UNENCRYPTED_WEBSHELL", "非加密WebShell检测", "HIGH");
                collector.collect(alert);
                logger.warn("检测到非加密WebShell访问: {} {}", httpEvent.getMethod(), httpEvent.getUri());
            }
        } catch (Exception e) {
            logger.error("非加密WebShell检测错误: {}", e.getMessage(), e);
        }
    }

    /**
     * 检测基于负载的非加密WebShell
     */
    private boolean detectPayloadWebShell(HttpEvent httpEvent) {
        String requestBody = httpEvent.getRequestBody();
        if (requestBody == null || requestBody.isEmpty()) {
            return false;
        }
        
        // 分割负载内容
        String[] payloadSeq = requestBody.split("&");
        if (payloadSeq.length == 0) {
            return false;
        }
        
        // 第一段负载，一般是登录语句和密码
        String firstSeq = payloadSeq[0];
        
        // 检测K8菜刀
        if (K8_STR.equals(firstSeq)) {
            String uri = httpEvent.getUri();
            if (uri != null && uri.contains("?t=")) {
                logger.info("识别到k8菜刀");
                return true;
            }
        }
        
        // 检测开山斧
        if (KAISHANFU_STR.equals(firstSeq)) {
            logger.info("识别到开山斧");
            return true;
        }
        
        // 检测中国菜刀
        if (CNCK_STR.equals(firstSeq)) {
            logger.info("识别到中国菜刀");
            return true;
        }
        
        // 检测WebKnife
        if (firstSeq.startsWith(WEBKNIFE_STR)) {
            logger.info("识别到WebKnife");
            return true;
        }
        
        // 检测Altman
        if (ALTMAN_STR.equals(firstSeq)) {
            logger.info("识别到Altman");
            return true;
        }
        
        // 检测Hatchet
        if (HATCHET_STR.equals(firstSeq)) {
            logger.info("识别到Hatchet");
            return true;
        }
        
        // 检测WebshellManager
        if (WEBSHELLMANAGER_STR.equals(firstSeq)) {
            logger.info("识别到WebshellManager");
            return true;
        }
        
        // 检测蚁剑
        if (firstSeq.equals(ANT_SWORD_STR)) {
            logger.info("识别到蚁剑");
            return true;
        }
        
        // 检测Cknife
        if (firstSeq.equals(CKNIFE_STR)) {
            logger.info("识别到Cknife");
            return true;
        }
        
        // 检测小李飞刀
        if (firstSeq.startsWith("pass=echo \"")) {
            logger.info("识别到小李飞刀");
            return true;
        }
        
        // 检测Sniper
        if (firstSeq.startsWith(SNIPER_STR)) {
            logger.info("识别到sniper");
            return true;
        }
        
        // 检测Weevely
        String responseBody = httpEvent.getResponseBody();
        if (requestBody.length() > 28 && responseBody != null && responseBody.length() > 28) {
            String requestSubstring = requestBody.substring(17, 28);
            String responseSubstring = responseBody.substring(17, 28);
            if (requestSubstring.equals(responseSubstring) && "0f1b6a831c3".equals(requestSubstring)) {
                logger.info("识别到Weevely");
                return true;
            }
        }
        
        return false;
    }

    /**
     * 检测基于URL的非加密WebShell
     */
    private boolean detectUrlWebShell(HttpEvent httpEvent) {
        String uri = httpEvent.getUri();
        if (uri == null || !uri.contains("?")) {
            return false;
        }
        
        int queryIndex = uri.indexOf("?");
        String queryString = uri.substring(queryIndex + 1);
        String[] params = queryString.split("&");
        if (params.length == 0) {
            return false;
        }
        
        String firstParam = params[0].split("=")[0];
        
        // 检测QuasiBot
        if ("_".equals(firstParam) || "___".equals(firstParam)) {
            logger.info("识别到QuasiBot");
            return true;
        }
        
        // 检测Behinder
        if ("pass".equals(firstParam) && params[0].length() > 10) {
            String responseBody = httpEvent.getResponseBody();
            if (responseBody != null && 
                (responseBody.contains(QUASIBOT_STR1) || responseBody.contains(QUASIBOT_STR2))) {
                logger.info("识别到Behinder");
                return true;
            }
        }
        
        return false;
    }

    /**
     * 创建WebShell告警事件
     */
    private AlarmEvent createAlertEvent(HttpEvent httpEvent, String alertType, String alertName, String alertLevel) {
        AlarmEvent alert = new AlarmEvent();
        
        // 设置基本属性
        alert.setId(UUID.randomUUID().toString());
        alert.setType(ThreatTypeEnum.WEBSHELL_UNENCRYPT.getCode());
        alert.setName(alertName);
        alert.setSeverity(alertLevel);
        alert.setDescription("检测到可能的非加密WebShell访问行为");
        alert.setSolution("1. 检查服务器上是否存在可疑文件\n2. 更新Web应用程序到最新版本\n3. 检查服务器日志以获取更多信息");
        
        // 设置源IP和端口
        alert.setSourceIp(httpEvent.getSourceIp());
        alert.setSourcePort(httpEvent.getSourcePort());
        alert.setDestinationIp(httpEvent.getDestinationIp());
        alert.setDestinationPort(httpEvent.getDestinationPort());
        
        // 设置时间戳
        alert.setTimestamp(System.currentTimeMillis());
        
        // 设置详细信息
        Map<String, Object> details = new HashMap<>();
        details.put("method", httpEvent.getMethod());
        details.put("uri", httpEvent.getUri());
        details.put("userAgent", httpEvent.getUserAgent());
        details.put("host", httpEvent.getHost());
        details.put("requestHeaders", httpEvent.getHeaders());
        details.put("requestBody", httpEvent.getRequestBody());
        alert.setDetails(details);
        
        return alert;
    }
    
    /**
     * 创建WebShell检测数据流
     */
    public static DataStream<AlarmEvent> detectUnencryptedWebShell(DataStream<HttpEvent> httpEvents) {
        return httpEvents
                .flatMap(new PlainTextWebShellDetector())
                .name("非加密WebShell检测")
                .setParallelism(2);
    }
}
