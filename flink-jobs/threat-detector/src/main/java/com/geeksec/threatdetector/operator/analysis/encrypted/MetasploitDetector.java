package com.geeksec.threatdetector.operator.analysis.encrypted;

import com.geeksec.threatdetector.model.event.alarm.AlarmEvent;
import com.geeksec.threatdetector.model.event.protocol.SslEvent;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * Metasploit框架检测函数
 *
 * <AUTHOR>
 */
/**
 * Metasploit框架检测器
 * 用于检测Metasploit渗透测试框架的SSL/TLS流量特征
 *
 * <AUTHOR>
 * @since 2023/01/01
 */
public class MetasploitDetector extends RichFlatMapFunction<SslEvent, AlarmEvent> {
    
    private static final Logger logger = LoggerFactory.getLogger(MetasploitDetector.class);
    private static final String METASPLOIT_ALERT_TYPE = "METASPLOIT_DETECTED";
    private static final String METASPLOIT_ALERT_NAME = "Metasploit框架检测";
    private static final String METASPLOIT_ALERT_LEVEL = "HIGH";
    
    // 伪造的州名列表
    private static final List<String> FAKE_STATE_ABBR_LIST = Arrays.asList(
            "CA", "NY", "TX", "FL", "IL", "PA", "OH", "MI", "GA", "NC"
    );
    
    // 伪造的组织单位列表
    private static final List<String> FAKE_OU_LIST = Arrays.asList(
            "IT", "Security", "Operations", "Development", "Engineering"
    );
    
    // 伪造的姓氏列表
    private static final List<String> FAKE_LAST_NAME_LIST = Arrays.asList(
            "Smith", "Johnson", "Williams", "Jones", "Brown", "Davis", "Miller", "Wilson"
    );
    
    // 伪造的组织后缀列表
    private static final List<String> FAKE_SUFFIX_LIST = Arrays.asList(
            "Inc.", "Corp.", "LLC", "Ltd.", "Co."
    );
    
    @Override
    public void flatMap(SslEvent sslEvent, Collector<AlarmEvent> out) throws Exception {
        try {
            // 检测Metasploit特征
            if (isMetasploit(sslEvent)) {
                // 创建告警事件
                AlarmEvent alert = createAlertEvent(sslEvent);
                out.collect(alert);
                
                logger.warn("检测到Metasploit框架: {}:{} -> {}:{}", 
                        sslEvent.getSourceIp(), sslEvent.getSourcePort(),
                        sslEvent.getDestinationIp(), sslEvent.getDestinationPort());
            }
        } catch (Exception e) {
            logger.error("Metasploit检测错误: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 检测是否为Metasploit框架
     */
    private boolean isMetasploit(SslEvent sslEvent) {
        Map<String, String> certificate = sslEvent.getCertificate();
        if (certificate == null) {
            return false;
        }
        
        // 检查证书字段
        String country = certificate.get("country");
        String state = certificate.get("state");
        String organization = certificate.get("organization");
        String organizationalUnit = certificate.get("organizationalUnit");
        
        // 检查国家代码是否为 "US"
        if (!"US".equals(country)) {
            return false;
        }
        
        // 检查州代码是否在预定义的列表中
        if (!FAKE_STATE_ABBR_LIST.contains(state)) {
            return false;
        }
        
        // 检查组织单位是否在预定义的列表中
        if (!FAKE_OU_LIST.contains(organizationalUnit)) {
            return false;
        }
        
        // 检查组织名称是否符合要求
        return checkOrganization(organization);
    }
    
    /**
     * 检查组织名称是否符合Metasploit特征
     */
    private boolean checkOrganization(String organization) {
        if (organization == null) {
            return false;
        }
        
        // 检查组织名称是否以预定义的后缀结束
        for (String suffix : FAKE_SUFFIX_LIST) {
            if (organization.trim().endsWith(suffix)) {
                // 检查组织名称是否包含预定义的姓氏
                String[] parts = organization.split(",| |and ");
                for (String part : parts) {
                    if (FAKE_LAST_NAME_LIST.contains(part.trim())) {
                        return true;
                    }
                }
            }
        }
        return false;
    }
    
    /**
     * 创建Metasploit告警事件
     */
    private AlarmEvent createAlertEvent(SslEvent sslEvent) {
        AlarmEvent alert = new AlarmEvent();
        
        // 设置基本属性
        alert.setId(UUID.randomUUID().toString());
        alert.setType(METASPLOIT_ALERT_TYPE);
        alert.setName(METASPLOIT_ALERT_NAME);
        alert.setSeverity(METASPLOIT_ALERT_LEVEL);
        alert.setDescription("检测到可能的Metasploit框架通信行为");
        alert.setSolution("1. 检查源IP和目标IP之间的SSL通信\n2. 分析SSL证书是否为Metasploit特征\n3. 检查是否存在未授权的渗透测试活动");
        
        // 设置源IP和端口
        alert.setSourceIp(sslEvent.getSourceIp());
        alert.setSourcePort(sslEvent.getSourcePort());
        alert.setDestinationIp(sslEvent.getDestinationIp());
        alert.setDestinationPort(sslEvent.getDestinationPort());
        
        // 设置时间戳
        alert.setTimestamp(System.currentTimeMillis());
        
        // 设置详细信息
        Map<String, Object> details = new HashMap<>();
        details.put("sslVersion", sslEvent.getSslVersion());
        details.put("cipherSuite", sslEvent.getCipherSuite());
        details.put("certificate", sslEvent.getCertificate());
        details.put("clientFingerprint", sslEvent.getClientFingerprint());
        details.put("serverFingerprint", sslEvent.getServerFingerprint());
        alert.setDetails(details);
        
        return alert;
    }
    
    /**
     * 创建Metasploit检测数据流
     */
    public static DataStream<AlarmEvent> detectMetasploit(DataStream<SslEvent> sslEvents) {
        return sslEvents
                .flatMap(new MetasploitDetector())
                .name("Metasploit框架检测")
                .setParallelism(2);
    }
}
