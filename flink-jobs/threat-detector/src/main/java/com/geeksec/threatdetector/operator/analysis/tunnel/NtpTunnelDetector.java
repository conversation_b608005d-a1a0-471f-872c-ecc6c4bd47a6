package com.geeksec.threatdetector.operator.analysis.tunnel;

import com.geeksec.threatdetector.model.event.alarm.AlarmEvent;
import com.geeksec.threatdetector.model.event.protocol.NtpEvent;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * NTP隧道检测器
 * 用于检测基于NTP协议的隧道流量特征
 *
 * <AUTHOR>
 * @since 2023/01/01
 */
public class NtpTunnelDetector extends RichFlatMapFunction<NtpEvent, AlarmEvent> {
    
    private static final Logger logger = LoggerFactory.getLogger(NtpTunnelDetector.class);
    private static final String NTP_TUNNEL_ALERT_TYPE = "NTP_TUNNEL_DETECTED";
    private static final String NTP_TUNNEL_ALERT_NAME = "NTP隧道检测";
    private static final String NTP_TUNNEL_ALERT_LEVEL = "HIGH";
    
    @Override
    public void flatMap(NtpEvent ntpEvent, Collector<AlarmEvent> out) throws Exception {
        try {
            // 检测NTP隧道特征
            if (isNtpTunnel(ntpEvent)) {
                // 创建告警事件
                AlarmEvent alert = createAlertEvent(ntpEvent);
                out.collect(alert);
                
                logger.warn("检测到NTP隧道: {}:{} -> {}:{}", 
                        ntpEvent.getSourceIp(), ntpEvent.getSourcePort(),
                        ntpEvent.getDestinationIp(), ntpEvent.getDestinationPort());
            }
        } catch (Exception e) {
            logger.error("NTP隧道检测错误: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 检测NTP连接是否包含隧道特征
     */
    private boolean isNtpTunnel(NtpEvent ntpEvent) {
        // 1. 检查NTP版本是否异常
        int version = ntpEvent.getVersion();
        if (version < 1 || version > 4) {
            return true;
        }
        
        // 2. 检查NTP模式是否异常
        int mode = ntpEvent.getMode();
        if (mode < 1 || mode > 7) {
            return true;
        }
        
        // 3. 检查NTP请求和响应的时间戳是否异常
        long originateTimestamp = ntpEvent.getOriginateTimestamp();
        long receiveTimestamp = ntpEvent.getReceiveTimestamp();
        long transmitTimestamp = ntpEvent.getTransmitTimestamp();
        
        // 检查时间戳是否为0
        if (originateTimestamp == 0 || receiveTimestamp == 0 || transmitTimestamp == 0) {
            return true;
        }
        
        // 检查时间戳是否不合理
        long currentTime = System.currentTimeMillis();
        if (Math.abs(transmitTimestamp - currentTime) > 86400000) { // 1天
            return true;
        }
        
        // 4. 检查NTP请求的频率是否异常
        // 这部分需要状态管理，这里简化处理
        
        // 5. 检查NTP请求的参考标识符是否异常
        String refId = ntpEvent.getReferenceId();
        if (refId != null && refId.length() > 0) {
            // 检查参考标识符是否为私有IP
            if (refId.startsWith("10.") || refId.startsWith("192.168.") || refId.startsWith("172.16.")) {
                return true;
            }
            
            // 检查参考标识符是否为异常值
            if (refId.equals("0.0.0.0") || refId.equals("***************")) {
                return true;
            }
        }
        
        // 6. 检查数据包特征
        byte[] payload = ntpEvent.getPayloadBytes();
        if (payload != null && payload.length > 0) {
            // 检查扩展字段是否包含异常模式
            if (containsAbnormalPattern(payload)) {
                return true;
            }
            
            // 检查扩展字段是否包含加密数据
            if (isLikelyEncrypted(payload)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 检查是否包含异常模式
     */
    private boolean containsAbnormalPattern(byte[] data) {
        if (data.length < 10) {
            return false;
        }
        
        // 检查是否包含重复模式
        int repeatedCount = 0;
        byte prevByte = data[0];
        for (int i = 1; i < data.length; i++) {
            if (data[i] == prevByte) {
                repeatedCount++;
                if (repeatedCount > 20) {
                    return true;
                }
            } else {
                repeatedCount = 0;
                prevByte = data[i];
            }
        }
        
        return false;
    }
    
    /**
     * 检查数据是否可能是加密的
     */
    private boolean isLikelyEncrypted(byte[] data) {
        if (data.length < 20) {
            return false;
        }
        
        // 计算字节分布
        int[] byteCounts = new int[256];
        for (byte b : data) {
            byteCounts[b & 0xFF]++;
        }
        
        // 计算熵值
        double entropy = 0;
        for (int count : byteCounts) {
            if (count > 0) {
                double probability = (double) count / data.length;
                entropy -= probability * Math.log(probability) / Math.log(2);
            }
        }
        
        // 熵值高表示数据可能是加密的或压缩的
        return entropy > 7.0;
    }
    
    /**
     * 创建NTP隧道告警事件
     */
    private AlarmEvent createAlertEvent(NtpEvent ntpEvent) {
        AlarmEvent alert = new AlarmEvent();
        
        // 设置基本属性
        alert.setId(UUID.randomUUID().toString());
        alert.setType(NTP_TUNNEL_ALERT_TYPE);
        alert.setName(NTP_TUNNEL_ALERT_NAME);
        alert.setSeverity(NTP_TUNNEL_ALERT_LEVEL);
        alert.setDescription("检测到可能的NTP隧道通信行为");
        alert.setSolution("1. 检查源IP和目标IP之间的NTP通信\n2. 分析NTP数据包内容是否存在数据泄露\n3. 检查是否存在未授权的通信通道");
        
        // 设置源IP和端口
        alert.setSourceIp(ntpEvent.getSourceIp());
        alert.setSourcePort(ntpEvent.getSourcePort());
        alert.setDestinationIp(ntpEvent.getDestinationIp());
        alert.setDestinationPort(ntpEvent.getDestinationPort());
        
        // 设置时间戳
        alert.setTimestamp(System.currentTimeMillis());
        
        // 设置详细信息
        Map<String, Object> details = new HashMap<>();
        details.put("version", ntpEvent.getVersion());
        details.put("mode", ntpEvent.getMode());
        details.put("stratum", ntpEvent.getStratum());
        details.put("pollInterval", ntpEvent.getPollInterval());
        details.put("precision", ntpEvent.getPrecision());
        details.put("rootDelay", ntpEvent.getRootDelay());
        details.put("rootDispersion", ntpEvent.getRootDispersion());
        details.put("referenceId", ntpEvent.getReferenceId());
        details.put("referenceTimestamp", ntpEvent.getReferenceTimestamp());
        details.put("originateTimestamp", ntpEvent.getOriginateTimestamp());
        details.put("receiveTimestamp", ntpEvent.getReceiveTimestamp());
        details.put("transmitTimestamp", ntpEvent.getTransmitTimestamp());
        alert.setDetails(details);
        
        return alert;
    }
    
    /**
     * 创建NTP隧道检测数据流
     */
    public static DataStream<AlarmEvent> detectNtpTunnel(DataStream<NtpEvent> ntpEvents) {
        return ntpEvents
                .flatMap(new NtpTunnelDetector())
                .name("NTP隧道检测")
                .setParallelism(2);
    }
}
