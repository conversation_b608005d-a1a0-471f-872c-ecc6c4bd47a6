package com.geeksec.threatdetector.model.nebula;

// import lombok.Data; // Removed

/**
 * <AUTHOR>
 * @Description：
 */
// @Data // Removed
public class DnsQueryInfo extends BaseEdge {
    /**
     * 请求类型
     */
    private Integer queryType;

    /**
     * 返回结果(错误0/成功1/不存在2)
     */
    private Integer answerType;

    public Integer getQueryType() {
        return queryType;
    }

    public void setQueryType(Integer queryType) {
        this.queryType = queryType;
    }

    public Integer getAnswerType() {
        return answerType;
    }

    public void setAnswerType(Integer answerType) {
        this.answerType = answerType;
    }
}
