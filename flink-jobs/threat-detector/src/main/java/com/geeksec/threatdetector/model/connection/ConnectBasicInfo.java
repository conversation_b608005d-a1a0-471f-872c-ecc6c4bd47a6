package com.geeksec.threatdetector.model.connection;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

// 假设 PacketInfo 和 SuspiciousHeartbeat 类在当前包或已导入的包中是可访问的
// import com.geeksec.threatdetector.model.PacketInfo;
// import com.geeksec.threatdetector.model.SuspiciousHeartbeat;


public class ConnectBasicInfo {

    public String getSrcIp() {
        return srcIp;
    }

    public void setSrcIp(String srcIp) {
        this.srcIp = srcIp;
    }

    public String getDstIp() {
        return dstIp;
    }

    public void setDstIp(String dstIp) {
        this.dstIp = dstIp;
    }

    public String getSrcMac() {
        return srcMac;
    }

    public void setSrcMac(String srcMac) {
        this.srcMac = srcMac;
    }

    public String getDstMac() {
        return dstMac;
    }

    public void setDstMac(String dstMac) {
        this.dstMac = dstMac;
    }

    public String getSrcPort() {
        return srcPort;
    }

    public void setSrcPort(String srcPort) {
        this.srcPort = srcPort;
    }

    public String getDstPort() {
        return dstPort;
    }

    public void setDstPort(String dstPort) {
        this.dstPort = dstPort;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public List<PacketInfo> getPacketInfoList() {
        return packetInfoList;
    }

    public void setPacketInfoList(List<PacketInfo> packetInfoList) {
        this.packetInfoList = packetInfoList;
    }

    public Integer getStartTime() {
        return startTime;
    }

    public void setStartTime(Integer startTime) {
        this.startTime = startTime;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public List<String> getLabels() {
        return labels;
    }

    public void setLabels(List<String> labels) {
        this.labels = labels;
    }

    public SuspiciousHeartbeat getcSuspiciousHeartbeat() {
        return cSuspiciousHeartbeat;
    }

    public void setcSuspiciousHeartbeat(SuspiciousHeartbeat cSuspiciousHeartbeat) {
        this.cSuspiciousHeartbeat = cSuspiciousHeartbeat;
    }

    public SuspiciousHeartbeat getsSuspiciousHeartbeat() {
        return sSuspiciousHeartbeat;
    }

    public void setsSuspiciousHeartbeat(SuspiciousHeartbeat sSuspiciousHeartbeat) {
        this.sSuspiciousHeartbeat = sSuspiciousHeartbeat;
    }

    public boolean isActivation() {
        return isActivation;
    }

    public void setActivation(boolean activation) {
        isActivation = activation;
    }

    public boolean isControl() {
        return isControl;
    }

    public void setControl(boolean control) {
        isControl = control;
    }

    public boolean isHeartbeat() {
        return isHeartbeat;
    }

    public void setHeartbeat(boolean heartbeat) {
        isHeartbeat = heartbeat;
    }

    public List<String> getAnalysisLabelList() {
        return analysisLabelList;
    }

    public void setAnalysisLabelList(List<String> analysisLabelList) {
        this.analysisLabelList = analysisLabelList;
    }

    public List<String> getClient4PayloadList() {
        return client4PayloadList;
    }

    public void setClient4PayloadList(List<String> client4PayloadList) {
        this.client4PayloadList = client4PayloadList;
    }

    public List<String> getServer4PayloadList() {
        return server4PayloadList;
    }

    public void setServer4PayloadList(List<String> server4PayloadList) {
        this.server4PayloadList = server4PayloadList;
    }

    public String getEsKey() {
        return esKey;
    }

    public void setEsKey(String esKey) {
        this.esKey = esKey;
    }

    public int getTotalPacketNum() {
        return totalPacketNum;
    }

    public void setTotalPacketNum(int totalPacketNum) {
        this.totalPacketNum = totalPacketNum;
    }

    public int getDuration() {
        return duration;
    }

    public void setDuration(int duration) {
        this.duration = duration;
    }

    private String srcIp;
    private String dstIp;
    private String srcMac;
    private String dstMac;
    private String srcPort;
    private String dstPort;
    private String appName;
    private List<PacketInfo> packetInfoList;
    private Integer startTime;
    private String sessionId;
    private List<String> labels;

    private SuspiciousHeartbeat cSuspiciousHeartbeat = null;
    private SuspiciousHeartbeat sSuspiciousHeartbeat = null;

    public static String cHeartbeatTag = "SUS_CLIENT_HEARTBEAT_TEMP_TAG"; // ThreatTypeEnum.SUS_CLIENT_HEARTBEAT.getCode();
    public static String sHeartbeatTag = "SUS_SERVER_HEARTBEAT_TEMP_TAG"; // ThreatTypeEnum.SUS_SERVER_HEARTBEAT.getCode();
    public static String controlTag = "SUS_CONTROL_TEMP_TAG";    // ThreatTypeEnum.SUS_CONTROL.getCode();
    public static String activationTag = "SUS_ACTIVATION_TEMP_TAG"; // ThreatTypeEnum.SUS_ACTIVATION.getCode();

    private boolean isActivation;
    private boolean isControl;
    private boolean isHeartbeat;
    private List<String> analysisLabelList = new ArrayList<>();
    private List<String> client4PayloadList;
    private List<String> server4PayloadList;
    private String esKey;
    private int totalPacketNum;
    private int duration;

    @SuppressWarnings("unchecked")
    public ConnectBasicInfo(Map<String, Object> pbMap) {
        this.srcIp = (String) pbMap.get("sIp");
        this.dstIp = (String) pbMap.get("dIp");
        this.srcMac = (String) pbMap.get("sMac");
        this.dstMac = (String) pbMap.get("dMac");
        this.srcPort = String.valueOf(pbMap.get("sPort"));
        this.dstPort = String.valueOf(pbMap.get("dPort"));
        this.appName = (String) pbMap.get("AppProto");
        this.startTime = (Integer) pbMap.get("StartTime");
        this.sessionId = (String) pbMap.get("SessionId");
        this.esKey = (String) pbMap.get("es_key");
        this.totalPacketNum = pbMap.get("TotalPacketNum") != null ? Integer.parseInt(pbMap.get("TotalPacketNum").toString()) : 0;
        this.duration = pbMap.get("Duration") != null ? Integer.parseInt(pbMap.get("Duration").toString()) : 0;

        if (pbMap.containsKey("Labels")) {
            this.labels = ((List<Integer>) pbMap.get("Labels")).stream()
                    .map(Object::toString)
                    .collect(Collectors.toList());
        } else {
            this.labels = new ArrayList<>();
        }

        // Initialize lists if they are null
        this.packetInfoList = new ArrayList<>(); // Assuming PacketInfo needs to be populated elsewhere or is optional here
        this.client4PayloadList = new ArrayList<>();
        this.server4PayloadList = new ArrayList<>();
        this.analysisLabelList = new ArrayList<>();


        // Populate packetInfoList if data exists in pbMap (example structure)
        if (pbMap.containsKey("packets") && pbMap.get("packets") instanceof List) {
            List<Map<String, Object>> packetsData = (List<Map<String, Object>>) pbMap.get("packets");
            for (Map<String, Object> packetData : packetsData) {
                // Assuming PacketInfo has a constructor or setters to populate from a Map
                // this.packetInfoList.add(new PacketInfo(packetData)); 
            }
        }
        
        if (pbMap.containsKey("client_payload") && pbMap.get("client_payload") instanceof List) {
            this.client4PayloadList.addAll((List<String>)pbMap.get("client_payload"));
        }
        if (pbMap.containsKey("server_payload") && pbMap.get("server_payload") instanceof List) {
            this.server4PayloadList.addAll((List<String>)pbMap.get("server_payload"));
        }
    }

    public void handleControl() {
        // TODO: Implement logic for control handling based on packetInfoList or other attributes
        // For example, identify control patterns and set isControl = true;
        // Add relevant labels to analysisLabelList
        // this.analysisLabelList.add(controlTag);
    }

    // Other methods like handleHeartBeat, detectRemoteControlBehavior etc. would go here
    // For now, they remain as per the original file (potentially commented out or to be implemented)
}
