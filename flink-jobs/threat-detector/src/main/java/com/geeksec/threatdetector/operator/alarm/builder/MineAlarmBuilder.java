package com.geeksec.threatdetector.operator.alarm.builder;

import com.alibaba.fastjson.JSONObject;
import org.apache.flink.types.Row;
import redis.clients.jedis.Jedis;

/**
 * Builder for mine-related alarms.
 * <AUTHOR>
 */
public class MineAlarmBuilder extends BaseAlarmBuilder {
    
    private static final MineAlarmBuilder INSTANCE = new MineAlarmBuilder();
    
    private MineAlarmBuilder() {}
    
    public static MineAlarmBuilder getInstance() {
        return INSTANCE;
    }
    
    @Override
    public JSONObject getAlarmJson(Row alarmRow, Jedis jedis) {
        JSONObject alarmJson = createBaseAlarmJson("挖矿病毒", null);
        
        // Extract fields from the Row and add to the JSON object
        // The actual field indices should be adjusted based on the actual data structure
        alarmJson.put("source_ip", getStringFieldSafely(alarmRow, 2));
        alarmJson.put("source_port", getStringFieldSafely(alarmRow, 3));
        alarmJson.put("destination_ip", getStringFieldSafely(alarmRow, 4));
        alarmJson.put("destination_port", getStringFieldSafely(alarmRow, 5));
        alarmJson.put("protocol", getStringFieldSafely(alarmRow, 6));
        alarmJson.put("timestamp", getStringFieldSafely(alarmRow, 7));
        
        // Add any additional fields specific to mine alarms
        
        return alarmJson;
    }
    
    /**
     * Static helper method to build a mine alarm JSON.
     * @param alarmRow The input Row containing alarm data
     * @param jedis Redis client for additional data lookup
     * @return JSONObject containing the alarm data
     */
    public static JSONObject buildMineAlarmJson(Row alarmRow, Jedis jedis) {
        return getInstance().getAlarmJson(alarmRow, jedis);
    }
}
