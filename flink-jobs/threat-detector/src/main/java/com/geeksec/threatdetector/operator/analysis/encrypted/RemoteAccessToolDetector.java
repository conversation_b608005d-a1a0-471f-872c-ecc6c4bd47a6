package com.geeksec.threatdetector.operator.analysis.encrypted;

import com.geeksec.threatdetector.model.event.alarm.AlarmEvent;
import com.geeksec.threatdetector.model.event.protocol.SslEvent;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 远程访问木马(RAT)检测器
 * 用于检测各种远程访问木马(RAT)的SSL/TLS流量特征
 *
 * <AUTHOR>
 * @since 2023/01/01
 */
public class RemoteAccessToolDetector extends RichFlatMapFunction<SslEvent, AlarmEvent> {
    
    private static final Logger logger = LoggerFactory.getLogger(RemoteAccessToolDetector.class);
    private static final String RAT_ALERT_TYPE = "RAT_DETECTED";
    private static final String RAT_ALERT_NAME = "远程访问木马检测";
    private static final String RAT_ALERT_LEVEL = "HIGH";
    
    @Override
    public void flatMap(SslEvent sslEvent, Collector<AlarmEvent> out) throws Exception {
        try {
            // 检测Quasar RAT
            if (isQuasar(sslEvent)) {
                AlarmEvent alert = createAlertEvent(sslEvent, "QUASAR", "Quasar RAT");
                out.collect(alert);
                logger.warn("检测到Quasar RAT: {}:{} -> {}:{}", 
                        sslEvent.getSourceIp(), sslEvent.getSourcePort(),
                        sslEvent.getDestinationIp(), sslEvent.getDestinationPort());
            }
            
            // 检测Remcos RAT
            if (isRemcos(sslEvent)) {
                AlarmEvent alert = createAlertEvent(sslEvent, "REMCOS", "Remcos RAT");
                out.collect(alert);
                logger.warn("检测到Remcos RAT: {}:{} -> {}:{}", 
                        sslEvent.getSourceIp(), sslEvent.getSourcePort(),
                        sslEvent.getDestinationIp(), sslEvent.getDestinationPort());
            }
            
            // 检测NanoCore RAT
            if (isNanoCore(sslEvent)) {
                AlarmEvent alert = createAlertEvent(sslEvent, "NANOCORE", "NanoCore RAT");
                out.collect(alert);
                logger.warn("检测到NanoCore RAT: {}:{} -> {}:{}", 
                        sslEvent.getSourceIp(), sslEvent.getSourcePort(),
                        sslEvent.getDestinationIp(), sslEvent.getDestinationPort());
            }
            
            // 检测Ghost RAT
            if (isGhost(sslEvent)) {
                AlarmEvent alert = createAlertEvent(sslEvent, "GHOST", "Ghost RAT");
                out.collect(alert);
                logger.warn("检测到Ghost RAT: {}:{} -> {}:{}", 
                        sslEvent.getSourceIp(), sslEvent.getSourcePort(),
                        sslEvent.getDestinationIp(), sslEvent.getDestinationPort());
            }
            
        } catch (Exception e) {
            logger.error("RAT检测错误: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 检测是否为Quasar RAT
     */
    private boolean isQuasar(SslEvent sslEvent) {
        Map<String, String> certificate = sslEvent.getCertificate();
        if (certificate == null) {
            return false;
        }
        
        // 检查证书主题和颁发者
        String commonName = certificate.get("commonName");
        String issuerCommonName = certificate.get("issuerCommonName");
        
        if (!"Quasar Server CA".equals(commonName) || !"Quasar Server CA".equals(issuerCommonName)) {
            return false;
        }
        
        // 检查心跳包特征
        return hasHeartbeatWithSize(sslEvent, 33);
    }
    
    /**
     * 检测是否为Remcos RAT
     */
    private boolean isRemcos(SslEvent sslEvent) {
        Map<String, String> certificate = sslEvent.getCertificate();
        if (certificate == null) {
            return false;
        }
        
        // 检查证书主题和颁发者
        String commonName = certificate.get("commonName");
        String issuerCommonName = certificate.get("issuerCommonName");
        
        boolean certMatch = "Cloudflare Inc ECC CA-3".equals(commonName) && "Baltimore CyberTrust Root".equals(issuerCommonName);
        
        // 检查指纹特征
        String clientFingerprint = sslEvent.getClientFingerprint();
        String serverFingerprint = sslEvent.getServerFingerprint();
        
        boolean fingerMatch = "7498398509702323821".equals(clientFingerprint) && "7899447458571054003".equals(serverFingerprint);
        
        return certMatch || fingerMatch;
    }
    
    /**
     * 检测是否为NanoCore RAT
     */
    private boolean isNanoCore(SslEvent sslEvent) {
        // 检查心跳包特征
        return hasClientHeartbeatWithSize(sslEvent, 12) && hasServerHeartbeatWithSize(sslEvent, -1234);
    }
    
    /**
     * 检测是否为Ghost RAT
     */
    private boolean isGhost(SslEvent sslEvent) {
        // 检查心跳包特征
        return hasClientHeartbeatWithSize(sslEvent, 1) && hasServerHeartbeatWithSize(sslEvent, -1);
    }
    
    /**
     * 检查是否存在指定大小的心跳包
     */
    private boolean hasHeartbeatWithSize(SslEvent sslEvent, int size) {
        return hasClientHeartbeatWithSize(sslEvent, size) || hasServerHeartbeatWithSize(sslEvent, size);
    }
    
    /**
     * 检查是否存在指定大小的客户端心跳包
     */
    private boolean hasClientHeartbeatWithSize(SslEvent sslEvent, int size) {
        Integer clientHeartbeatSize = sslEvent.getClientHeartbeatSize();
        return clientHeartbeatSize != null && clientHeartbeatSize == size;
    }
    
    /**
     * 检查是否存在指定大小的服务器心跳包
     */
    private boolean hasServerHeartbeatWithSize(SslEvent sslEvent, int size) {
        Integer serverHeartbeatSize = sslEvent.getServerHeartbeatSize();
        return serverHeartbeatSize != null && serverHeartbeatSize == size;
    }
    
    /**
     * 创建RAT告警事件
     */
    private AlarmEvent createAlertEvent(SslEvent sslEvent, String subType, String subName) {
        AlarmEvent alert = new AlarmEvent();
        
        // 设置基本属性
        alert.setId(UUID.randomUUID().toString());
        alert.setType(RAT_ALERT_TYPE + "_" + subType);
        alert.setName(RAT_ALERT_NAME + " - " + subName);
        alert.setSeverity(RAT_ALERT_LEVEL);
        alert.setDescription("检测到可能的" + subName + "远程访问木马通信行为");
        alert.setSolution("1. 立即隔离涉及的主机\n2. 检查源IP和目标IP之间的SSL通信\n3. 使用专业安全工具扫描系统是否存在" + subName + "木马");
        
        // 设置源IP和端口
        alert.setSourceIp(sslEvent.getSourceIp());
        alert.setSourcePort(sslEvent.getSourcePort());
        alert.setDestinationIp(sslEvent.getDestinationIp());
        alert.setDestinationPort(sslEvent.getDestinationPort());
        
        // 设置时间戳
        alert.setTimestamp(System.currentTimeMillis());
        
        // 设置详细信息
        Map<String, Object> details = new HashMap<>();
        details.put("ratType", subType);
        details.put("sslVersion", sslEvent.getSslVersion());
        details.put("cipherSuite", sslEvent.getCipherSuite());
        details.put("certificate", sslEvent.getCertificate());
        details.put("clientFingerprint", sslEvent.getClientFingerprint());
        details.put("serverFingerprint", sslEvent.getServerFingerprint());
        details.put("clientHeartbeatSize", sslEvent.getClientHeartbeatSize());
        details.put("serverHeartbeatSize", sslEvent.getServerHeartbeatSize());
        alert.setDetails(details);
        
        return alert;
    }
    
    /**
     * 创建RAT检测数据流
     */
    public static DataStream<AlarmEvent> detectRats(DataStream<SslEvent> sslEvents) {
        return sslEvents
                .flatMap(new RemoteAccessToolDetector())
                .name("远程访问木马检测")
                .setParallelism(2);
    }
}
