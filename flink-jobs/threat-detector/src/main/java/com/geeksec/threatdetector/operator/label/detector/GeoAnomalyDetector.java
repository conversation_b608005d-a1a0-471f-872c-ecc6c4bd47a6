package com.geeksec.threatdetector.operator.label.detector;

import com.geeksec.common.utils.math.MathUtils;
import com.geeksec.threatdetector.common.enums.ThreatTypeEnum;
import com.geeksec.threatdetector.common.util.FileUtil;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.*;

/**
 * 地理位置异常检测器
 * 通过分析HTTP头部信息和熵值计算，检测与地理位置相关的异常行为
 *
 * <AUTHOR>
 * @since 2023/04/24
 */
public class GeoAnomalyDetector extends RichFlatMapFunction<Row, Row> {
    /** 英文单词列表，用于熵值计算 */
    public static List<String> ENGLISH_WORD_LIST = new ArrayList<>();
    private static final Logger logger = LoggerFactory.getLogger(GeoAnomalyDetector.class);

    private static Double ENTROPY_THRESHOLD = Double.valueOf(4.9);
    @Override
    public void open(Configuration parameters) throws Exception {
        InputStream english_stream = this.getClass().getClassLoader().getResourceAsStream("english3.txt");
        BufferedReader english_buffer = new BufferedReader(new InputStreamReader(english_stream));
        //加载配置文件
        try {
            ENGLISH_WORD_LIST = FileUtil.loadEnglishList(english_buffer);
        } catch (Exception e) {
            throw new IOException("file load fail ==>", e);
        }
        super.open(parameters);
    }
    @Override
    public void flatMap(Row row, Collector<Row> collector) throws Exception {
        Map<String,String> abnormal_head = row.getFieldAs(4);
        String SessionID = (String) row.getField(5);
        for (String key:abnormal_head.keySet()){
            //判断值的熵，目前弃用
            //String val = abnormal_head.get(key);
            //if (val.length()>54){
            //   val = val.substring(0,54);
            //}
            //Double val_Entropy = MathUtils.calculateEntropy(val);
            String field = key.split("-")[1];
            String val = abnormal_head.get(key);
            if (MathUtils.calculateEntropy(val) >= ENTROPY_THRESHOLD && !ENGLISH_WORD_LIST.contains(val.toLowerCase())) {
                List<String> alarm_info = Arrays.asList(field,val);
                Row SessionLabelRow = new Row(4);
                SessionLabelRow.setField(0,"会话打标");
                SessionLabelRow.setField(1,SessionID);
                SessionLabelRow.setField(2, Collections.singleton(ThreatTypeEnum.NEOREG.getCode()));//Neoreg代理隧道
                SessionLabelRow.setField(3,row.getFieldAs(6));
//                    AddTagToSession(SessionID,"21001");//web登录爆破
                collector.collect(SessionLabelRow);
                logger.info("Neoregeo代理隧道 ES插入{}",SessionID);
                Row alarm_row =new Row(8);
                String sIp = row.getFieldAs(1);
                String dIp = row.getFieldAs(2);
                String session_id = row.getFieldAs(5);
                alarm_row.setField(0,"加密隐蔽隧道通信");
                alarm_row.setField(1,"Neoregeo代理隧道");
                alarm_row.setField(2,sIp);
                alarm_row.setField(3,dIp);
                alarm_row.setField(4,alarm_info);
                alarm_row.setField(5, ThreatTypeEnum.NEOREG.getCode());
                alarm_row.setField(6,session_id);
                alarm_row.setField(7,row.getFieldAs(6));
                alarm_row.setField(8,row.getFieldAs(7));
                logger.info("Neoregeo代理隧道告警{}",sIp);
                collector.collect(alarm_row);
                break;
            }
        }
    }
}
