package com.geeksec.threatdetector.operator.alarm;

import com.geeksec.common.utils.json.JsonUtils;
import com.geeksec.threatdetector.model.event.alarm.AlarmEvent;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

import java.util.HashMap;
import java.util.Map;

/**
 * 告警去重处理器
 * 用于检测并过滤重复的告警事件
 *
 * <AUTHOR>
 */
public class AlarmDeduplicationProcessor extends ProcessFunction<AlarmEvent, AlarmEvent> {
    
    private static final Logger logger = LoggerFactory.getLogger(AlarmDeduplicationProcessor.class);
    
    // 重复告警的侧输出标签
    public static final OutputTag<AlarmEvent> DUPLICATE_ALERTS = 
            new OutputTag<>("duplicate_alerts", TypeInformation.of(AlarmEvent.class));
    
    // Redis连接池
    private transient JedisPool jedisPool;
    
    // Redis配置
    private final String redisHost;
    private final int redisPort;
    private final String redisPassword;
    
    // 告警去重窗口时间（秒）
    private final int deduplicationWindowSeconds;
    
    /**
     * 构造函数
     *
     * @param redisHost Redis主机地址
     * @param redisPort Redis端口
     * @param redisPassword Redis密码
     * @param deduplicationWindowSeconds 去重窗口时间（秒）
     */
    public AlarmDeduplicationProcessor(String redisHost, int redisPort, String redisPassword, int deduplicationWindowSeconds) {
        this.redisHost = redisHost;
        this.redisPort = redisPort;
        this.redisPassword = redisPassword;
        this.deduplicationWindowSeconds = deduplicationWindowSeconds;
    }
    
    @Override
    public void open(Configuration parameters) throws Exception {
        // 初始化Redis连接池
        JedisPoolConfig poolConfig = new JedisPoolConfig();
        poolConfig.setMaxTotal(10);
        poolConfig.setMaxIdle(5);
        poolConfig.setMinIdle(1);
        
        if (redisPassword != null && !redisPassword.isEmpty()) {
            jedisPool = new JedisPool(poolConfig, redisHost, redisPort, 2000, redisPassword);
        } else {
            jedisPool = new JedisPool(poolConfig, redisHost, redisPort, 2000);
        }
        
        logger.info("Redis连接池初始化完成: {}:{}", redisHost, redisPort);
    }
    
    @Override
    public void processElement(AlarmEvent alert, Context ctx, Collector<AlarmEvent> out) throws Exception {
        // 生成告警的唯一标识
        String alertKey = generateAlertKey(alert);
        
        // 检查是否为重复告警
        if (isDuplicateAlert(alertKey)) {
            // 输出到重复告警的侧输出流
            ctx.output(DUPLICATE_ALERTS, alert);
            logger.debug("检测到重复告警: {}", alertKey);
        } else {
            // 记录告警并输出到主流
            recordAlert(alertKey);
            out.collect(alert);
        }
    }
    
    @Override
    public void close() throws Exception {
        if (jedisPool != null) {
            jedisPool.close();
            logger.info("Redis连接池已关闭");
        }
    }
    
    /**
     * 生成告警的唯一标识
     */
    private String generateAlertKey(AlarmEvent alert) {
        // 使用告警类型、源IP、目标IP和时间戳（精确到分钟）生成唯一标识
        Map<String, Object> keyMap = new HashMap<>();
        keyMap.put("type", alert.getType());
        keyMap.put("source", alert.getSourceIp());
        keyMap.put("destination", alert.getDestinationIp());
        keyMap.put("timestamp", alert.getTimestamp() / (60 * 1000) * (60 * 1000)); // 精确到分钟
        
        return "alert:" + JsonUtils.toJsonString(keyMap);
    }
    
    /**
     * 检查是否为重复告警
     */
    private boolean isDuplicateAlert(String alertKey) {
        try (Jedis jedis = jedisPool.getResource()) {
            return jedis.exists(alertKey);
        } catch (Exception e) {
            logger.error("检查重复告警失败: {}", e.getMessage(), e);
            return false; // 如果Redis操作失败，默认不去重
        }
    }
    
    /**
     * 记录告警
     */
    private void recordAlert(String alertKey) {
        try (Jedis jedis = jedisPool.getResource()) {
            // 设置告警记录，并设置过期时间
            jedis.setex(alertKey, deduplicationWindowSeconds, "1");
        } catch (Exception e) {
            logger.error("记录告警失败: {}", e.getMessage(), e);
        }
    }
}
