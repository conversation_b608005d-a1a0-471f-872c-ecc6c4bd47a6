package com.geeksec.threatdetector.operator.label.dns;

import com.geeksec.threatdetector.common.util.FileUtil;
import com.geeksec.threatdetector.model.nebula.DnsParseToInfo;
import com.geeksec.threatdetector.model.nebula.DnsQueryInfo;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * DNS查询行映射器
 * 负责将DNS查询信息映射为行格式，包括客户端查询和服务器响应
 *
 * <AUTHOR>
 */
public class DnsQueryRowMapper extends RichFlatMapFunction<Map<String, Object>, Row> {
    private static final Logger log = LoggerFactory.getLogger(DnsQueryRowMapper.class);
    /** 白名单域名列表 */
    public static List<String> WHITE_DOMAIN_LIST = new ArrayList<>();

    @Override
    public void open(Configuration parameters) throws Exception {
        InputStream white_domain_stream = this.getClass().getClassLoader().getResourceAsStream("white_domains.txt");
        BufferedReader white_domain_buffer = new BufferedReader(new InputStreamReader(white_domain_stream));
        //加载配置文件
        try {
            WHITE_DOMAIN_LIST = FileUtil.loadWhiteDomainList(white_domain_buffer);
        } catch (Exception e) {
            throw new IOException("file load fail ==>", e);
        }
        super.open(parameters);
    }

    @Override
    public void flatMap(Map<String, Object> InfoMap, Collector<Row> collector) throws Exception {
        // ClientIp客户端IP ---> Domain 域名 client_query_domain
        DnsQueryInfo clientQueryDomainEdge = (DnsQueryInfo) InfoMap.get("clientQueryDomainEdge");
        if (clientQueryDomainEdge != null) {
            Row clientQueryDomainRow = new Row(9);
            clientQueryDomainRow.setField(0, "CLIENT_QUERY_DOMAIN_EDGE");
            clientQueryDomainRow.setField(1, clientQueryDomainEdge.getSrcId());
            clientQueryDomainRow.setField(2, clientQueryDomainEdge.getDstId());
            clientQueryDomainRow.setField(3, 0);
            clientQueryDomainRow.setField(4, clientQueryDomainEdge.getFirstSeen());
            clientQueryDomainRow.setField(5, clientQueryDomainEdge.getLastSeen());
            clientQueryDomainRow.setField(6, clientQueryDomainEdge.getSessionCnt());
            clientQueryDomainRow.setField(7, clientQueryDomainEdge.getQueryType());
            clientQueryDomainRow.setField(8, clientQueryDomainEdge.getAnswerType());
            collector.collect(clientQueryDomainRow);
        }

        // Domain ---> ServerIP ---> parse_to
        List<DnsParseToInfo> dnsParseToInfoList = (List<DnsParseToInfo>) InfoMap.get("dnsParseToEdgeList");
        if (!CollectionUtils.isEmpty(dnsParseToInfoList)) {
            for (DnsParseToInfo edge : dnsParseToInfoList) {
                Row dnsParseToEdgeRow = new Row(11);
                dnsParseToEdgeRow.setField(0, "DNS_PARSE_TO_EDGE");
                dnsParseToEdgeRow.setField(1, edge.getSrcId());
                dnsParseToEdgeRow.setField(2, edge.getDstId());
                dnsParseToEdgeRow.setField(3, 0);
                dnsParseToEdgeRow.setField(4, edge.getFirstSeen());
                dnsParseToEdgeRow.setField(5, edge.getLastSeen());
                dnsParseToEdgeRow.setField(6, edge.getSessionCnt());
                dnsParseToEdgeRow.setField(7, edge.getDnsServer());
                dnsParseToEdgeRow.setField(8, edge.getFinalParse());
                dnsParseToEdgeRow.setField(9, edge.getMaxTTL());
                dnsParseToEdgeRow.setField(10, edge.getMinTTL());
                collector.collect(dnsParseToEdgeRow);
            }
        }

        Row dns_tunnel_info = (Row) InfoMap.get("dns_tunnel_info");
        if (!ObjectUtils.isEmpty(dns_tunnel_info)){
//            collector.collect(dns_tunnel_info);
            String dIp = dns_tunnel_info.getFieldAs(1);
//            Map<String,Integer> domain_map = dns_tunnel_info.getFieldAs(3);
//            if (!BenignDNSServer_Map.contains(dIp) && !WHITE_DOMAIN_LIST.contains(new ArrayList<>(domain_map.keySet()).get(0))){
//                collector.collect(dns_tunnel_info);
//            }
            if (!DnsQueryProcessor.BenignDNSServer_Map.contains(dIp)){
                collector.collect(dns_tunnel_info);
            }
        }
    }

}
