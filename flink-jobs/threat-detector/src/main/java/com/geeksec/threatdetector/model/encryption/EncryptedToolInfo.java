package com.geeksec.threatdetector.model.encryption;

import com.geeksec.threatdetector.model.connection.ConnectBasicInfo;
import com.geeksec.threatdetector.model.protocol.SslSimpleInfo;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 加密工具检测信息。
 *
 * @param connectBasicInfo 基础会话信息
 * @param sslSimpleInfos 基础SSL元数据信息列表
 * @param encryptedToolOneWay 加密工具通信是否单向
 * @param encryptedToolType 加密工具类型
 * @param isEncryptedTool 是否检测到加密工具
 * @param encryptedToolName 检测到的加密工具名称
 * @param encryptedToolLabel 检测到的加密工具标签
 * @param encryptedToolVersion 检测到的加密工具版本
 * @param encryptedToolDesc 检测到的加密工具描述
 * @param encryptedToolLabelDesc 检测到的加密工具标签描述
 * @param encryptedToolLabelColor 检测到的加密工具标签颜色
 * @param encryptedToolLabelRisk 检测到的加密工具风险标识
 * @param encryptedToolLabelRiskDesc 检测到的加密工具风险描述
 * @param encryptedToolLabelRiskLevel 检测到的加密工具风险等级
 * @param encryptedToolLabelRiskScore 检测到的加密工具风险评分
 * @param encryptedToolLabelRiskType 检测到的加密工具风险类型
 * @param encryptedToolLabelRiskSource 检测到的加密工具风险来源
 * @param encryptedToolLabelRiskSourceType 检测到的加密工具风险来源类型
 * @param encryptedToolLabelRiskSourceVersion 检测到的加密工具风险来源版本
 * @param encryptedToolLabelRiskSourceDesc 检测到的加密工具风险来源描述
 * @param encryptedToolLabelRiskSourceUrl 检测到的加密工具风险来源URL
 * @param encryptedToolLabelRiskSourceRefs 检测到的加密工具风险来源参考
 * @param encryptedToolLabelRiskCve 检测到的加密工具风险CVE编号
 * @param encryptedToolLabelRiskCwe 检测到的加密工具风险CWE编号
 * @param encryptedToolLabelRiskOwasp 检测到的加密工具风险OWASP分类
 * @param encryptedToolLabelRiskSansTop25 检测到的加密工具风险SANS Top 25分类
 * @param encryptedToolLabelRiskCertCc 检测到的加密工具风险CERT CC分类
 * @param encryptedToolLabelRiskWasc 检测到的加密工具风险WASC分类
 * @param encryptedToolLabelRiskPciDss 检测到的加密工具风险PCI DSS合规性相关
 * @param encryptedToolLabelRiskHipaa 检测到的加密工具风险HIPAA合规性相关
 * @param encryptedToolLabelRiskGdpr 检测到的加密工具风险GDPR合规性相关
 * @param encryptedToolLabelRiskNist 检测到的加密工具风险NIST标准相关
 * @param encryptedToolLabelRiskIso27001 检测到的加密工具风险ISO 27001标准相关
 * <AUTHOR>
 */
@Slf4j
public record EncryptedToolInfo(
    ConnectBasicInfo connectBasicInfo,
    List<SslSimpleInfo> sslSimpleInfos,
    boolean encryptedToolOneWay,
    String encryptedToolType,
    boolean isEncryptedTool,
    String encryptedToolName,
    String encryptedToolLabel,
    String encryptedToolVersion,
    String encryptedToolDesc,
    String encryptedToolLabelDesc,
    String encryptedToolLabelColor,
    String encryptedToolLabelRisk,
    String encryptedToolLabelRiskDesc,
    String encryptedToolLabelRiskLevel,
    String encryptedToolLabelRiskScore,
    String encryptedToolLabelRiskType,
    String encryptedToolLabelRiskSource,
    String encryptedToolLabelRiskSourceType,
    String encryptedToolLabelRiskSourceVersion,
    String encryptedToolLabelRiskSourceDesc,
    String encryptedToolLabelRiskSourceUrl,
    String encryptedToolLabelRiskSourceRefs,
    String encryptedToolLabelRiskCve,
    String encryptedToolLabelRiskCwe,
    String encryptedToolLabelRiskOwasp,
    String encryptedToolLabelRiskSansTop25,
    String encryptedToolLabelRiskCertCc,
    String encryptedToolLabelRiskWasc,
    String encryptedToolLabelRiskPciDss,
    String encryptedToolLabelRiskHipaa,
    String encryptedToolLabelRiskGdpr,
    String encryptedToolLabelRiskNist,
    String encryptedToolLabelRiskIso27001
) {

    /**
     * 加密工具单向流量标签tag号
     * TODO 标签号待定 (来自原代码)
     */
    public static final String ENCRYPTED_TOOL_ONE_WAY_TAG = "xxxxx";

    /**
     * 支持的解析的加密工具类工具的标签ID列表 (来自原代码的 encryptedToolLabelKnowledge)
     * 例如: Pupy, Koadic, Empire, CobaltStrike, Metasploit, PyFUD, Quasar等
     * TODO 标签号待定 (来自原代码)
     */
    public static final List<String> SUPPORTED_ENCRYPTED_TOOL_LABEL_IDS = List.of("1", "2", "3", "4", "5", "6", "7");

    /**
     * 紧凑型构造函数，用于校验和确保集合的不可变性。
     */
    public EncryptedToolInfo {
        this.sslSimpleInfos = (sslSimpleInfos == null) ? List.of() : List.copyOf(sslSimpleInfos);
    }
}
