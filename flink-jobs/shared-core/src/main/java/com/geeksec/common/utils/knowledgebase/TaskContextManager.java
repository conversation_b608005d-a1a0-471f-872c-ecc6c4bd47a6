package com.geeksec.common.utils.knowledgebase;

import com.geeksec.common.utils.db.DatabaseConnectionManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 任务上下文管理器
 * 提供任务相关上下文信息的获取和缓存功能
 *
 * <AUTHOR>
 */
@Slf4j
public class TaskContextManager {

    /**
     * 单例实例
     */
    private static volatile TaskContextManager instance = null;

    /**
     * 任务信息映射
     */
    private final Map<Integer, TaskInfo> taskInfoMap = new ConcurrentHashMap<>();

    /**
     * 私有构造函数
     */
    private TaskContextManager() {
        initialize();
    }

    /**
     * 获取单例实例
     *
     * @return TaskContextManager实例
     */
    public static TaskContextManager getInstance() {
        if (instance == null) {
            synchronized (TaskContextManager.class) {
                if (instance == null) {
                    instance = new TaskContextManager();
                }
            }
        }
        return instance;
    }

    /**
     * 初始化任务上下文管理器
     */
    private void initialize() {
        try {
            // 初始化时不加载数据，等到需要时再加载
            log.info("任务上下文管理器初始化成功");
        } catch (Exception e) {
            log.error("初始化任务上下文管理器失败", e);
        }
    }

    /**
     * 获取任务信息
     *
     * @param taskId 任务ID
     * @return 任务信息，如果不存在则返回null
     */
    public TaskInfo getTaskInfo(Integer taskId) {
        // 如果缓存为空，加载任务信息
        if (MapUtils.isEmpty(taskInfoMap)) {
            loadTaskInfo();
        }

        // 如果缓存中没有当前任务，重新加载
        if (!taskInfoMap.containsKey(taskId)) {
            loadTaskInfo();
        }

        return taskInfoMap.get(taskId);
    }

    /**
     * 加载任务信息
     *
     * @return 任务信息列表
     */
    public List<TaskInfo> loadTaskInfo() {
        List<TaskInfo> resultList = new ArrayList<>();

        try (Connection conn = DatabaseConnectionManager.getConnection();
                Statement stmt = conn.createStatement()) {

            String sql = "SELECT t1.task_id, t2.batch_id, t1.task_name, t1.task_remark, t1.created_time, t1.task_state "
                    +
                    "FROM tb_task_analysis t1 LEFT JOIN tb_task_batch t2 ON t1.task_id = t2.task_id";

            try (ResultSet rs = stmt.executeQuery(sql)) {
                while (rs.next()) {
                    TaskInfo info = new TaskInfo();
                    info.setTaskId(rs.getInt(1));
                    info.setBatchId(rs.getInt(2));
                    info.setTaskName(rs.getString(3));
                    info.setTaskRemark(rs.getString(4));
                    info.setCreatedTime(rs.getInt(5));
                    info.setState(rs.getInt(6));

                    taskInfoMap.put(info.getTaskId(), info);
                    resultList.add(info);
                }
            }

            log.info("任务信息加载成功，共加载 {} 条记录", resultList.size());
            return resultList;
        } catch (Exception e) {
            log.error("加载任务信息失败", e);
            return Collections.emptyList();
        }
    }

    /**
     * 刷新任务上下文缓存
     */
    public void refresh() {
        taskInfoMap.clear();
        loadTaskInfo();
    }

    /**
     * 任务信息类
     */
    public static class TaskInfo {
        private int taskId;
        private int batchId;
        private String taskName;
        private String taskRemark;
        private int createdTime;
        private int state;

        public int getTaskId() {
            return taskId;
        }

        public void setTaskId(int taskId) {
            this.taskId = taskId;
        }

        public int getBatchId() {
            return batchId;
        }

        public void setBatchId(int batchId) {
            this.batchId = batchId;
        }

        public String getTaskName() {
            return taskName;
        }

        public void setTaskName(String taskName) {
            this.taskName = taskName;
        }

        public String getTaskRemark() {
            return taskRemark;
        }

        public void setTaskRemark(String taskRemark) {
            this.taskRemark = taskRemark;
        }

        public int getCreatedTime() {
            return createdTime;
        }

        public void setCreatedTime(int createdTime) {
            this.createdTime = createdTime;
        }

        public int getState() {
            return state;
        }

        public void setState(int state) {
            this.state = state;
        }
    }
}
