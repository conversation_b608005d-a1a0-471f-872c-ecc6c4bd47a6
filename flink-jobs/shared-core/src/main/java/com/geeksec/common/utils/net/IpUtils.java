package com.geeksec.common.utils.net;

import com.google.common.net.InetAddresses;

import java.net.Inet4Address;
import java.net.Inet6Address;
import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * IP工具类
 * 提供IP地址处理的通用方法
 *
 * <AUTHOR> Team
 */
public class IpUtils {

    /**
     * 判断是否为有效的IPv4地址
     *
     * @param ip IP地址
     * @return 如果是有效的IPv4地址则返回true
     */
    public static boolean isValidIpv4(String ip) {
        try {
            return InetAddress.getByName(ip) instanceof Inet4Address;
        } catch (UnknownHostException e) {
            return false;
        }
    }

    /**
     * 判断是否为有效的IPv6地址
     *
     * @param ip IP地址
     * @return 如果是有效的IPv6地址则返回true
     */
    public static boolean isValidIpv6(String ip) {
        try {
            return InetAddress.getByName(ip) instanceof Inet6Address;
        } catch (UnknownHostException e) {
            return false;
        }
    }

    /**
     * 判断是否为有效的IP地址（IPv4或IPv6）
     *
     * @param ip IP地址
     * @return 如果是有效的IP地址则返回true
     */
    public static boolean isValidIp(String ip) {
        try {
            InetAddress.getByName(ip);
            return true;
        } catch (UnknownHostException e) {
            return false;
        }
    }

    /**
     * 判断是否为内网IP地址
     *
     * @param ip IP地址
     * @return 如果是内网IP地址则返回true
     */
    public static boolean isInternalIp(String ip) {
        try {
            InetAddress inetAddress = InetAddress.getByName(ip);
            return inetAddress.isSiteLocalAddress() ||
                   inetAddress.isLoopbackAddress() ||
                   inetAddress.isLinkLocalAddress();
        } catch (UnknownHostException e) {
            return false;
        }
    }

    /**
     * 判断字符串是否为IP地址（IPv4或IPv6）
     * 该方法是isValidIp的别名，用于保持与其他代码的兼容性
     *
     * @param str 要检查的字符串
     * @return 如果是IP地址则返回true
     */
    public static boolean isIpAddress(String str) {
        return isValidIp(str);
    }

    /**
     * 判断IP地址是否在指定的CIDR网段内
     * 使用Google Guava库进行高效的网段匹配
     *
     * @param ipAddress IP地址字符串
     * @param cidr CIDR网段字符串（如 "***********/24"）
     * @return 如果IP在CIDR网段内则返回true
     */
    public static boolean isIpInCidr(String ipAddress, String cidr) {
        try {
            // 解析IP地址
            InetAddress ip = InetAddresses.forString(ipAddress);

            // 解析CIDR
            String[] parts = cidr.split("/");
            if (parts.length != 2) {
                return false;
            }

            InetAddress network = InetAddresses.forString(parts[0]);
            int prefixLength = Integer.parseInt(parts[1]);

            // 检查IP类型是否匹配
            if (ip instanceof Inet4Address && network instanceof Inet4Address) {
                return isIpv4InCidr((Inet4Address) ip, (Inet4Address) network, prefixLength);
            } else if (ip instanceof Inet6Address && network instanceof Inet6Address) {
                return isIpv6InCidr((Inet6Address) ip, (Inet6Address) network, prefixLength);
            }

            return false;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 判断IPv4地址是否在指定的CIDR网段内
     *
     * @param ip IPv4地址
     * @param network 网络地址
     * @param prefixLength 前缀长度
     * @return 如果IP在网段内则返回true
     */
    private static boolean isIpv4InCidr(Inet4Address ip, Inet4Address network, int prefixLength) {
        if (prefixLength < 0 || prefixLength > 32) {
            return false;
        }

        int ipInt = InetAddresses.coerceToInteger(ip);
        int networkInt = InetAddresses.coerceToInteger(network);
        int mask = (0xFFFFFFFF << (32 - prefixLength)) & 0xFFFFFFFF;

        return (ipInt & mask) == (networkInt & mask);
    }

    /**
     * 判断IPv6地址是否在指定的CIDR网段内
     *
     * @param ip IPv6地址
     * @param network 网络地址
     * @param prefixLength 前缀长度
     * @return 如果IP在网段内则返回true
     */
    private static boolean isIpv6InCidr(Inet6Address ip, Inet6Address network, int prefixLength) {
        if (prefixLength < 0 || prefixLength > 128) {
            return false;
        }

        byte[] ipBytes = ip.getAddress();
        byte[] networkBytes = network.getAddress();

        int bytesToCheck = prefixLength / 8;
        int bitsInLastByte = prefixLength % 8;

        // 检查完整的字节
        for (int i = 0; i < bytesToCheck; i++) {
            if (ipBytes[i] != networkBytes[i]) {
                return false;
            }
        }

        // 检查最后一个字节的部分位
        if (bitsInLastByte > 0 && bytesToCheck < 16) {
            int mask = (0xFF << (8 - bitsInLastByte)) & 0xFF;
            return (ipBytes[bytesToCheck] & mask) == (networkBytes[bytesToCheck] & mask);
        }

        return true;
    }

    /**
     * 批量检查IP地址是否在任意一个CIDR网段内
     *
     * @param ipAddress IP地址字符串
     * @param cidrs CIDR网段列表
     * @return 如果IP在任意一个网段内则返回true
     */
    public static boolean isIpInAnyCidr(String ipAddress, String... cidrs) {
        for (String cidr : cidrs) {
            if (isIpInCidr(ipAddress, cidr)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取CIDR网段的网络地址
     *
     * @param cidr CIDR网段字符串
     * @return 网络地址字符串，解析失败返回null
     */
    public static String getCidrNetworkAddress(String cidr) {
        try {
            String[] parts = cidr.split("/");
            if (parts.length != 2) {
                return null;
            }
            return parts[0];
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取CIDR网段的前缀长度
     *
     * @param cidr CIDR网段字符串
     * @return 前缀长度，解析失败返回-1
     */
    public static int getCidrPrefixLength(String cidr) {
        try {
            String[] parts = cidr.split("/");
            if (parts.length != 2) {
                return -1;
            }
            return Integer.parseInt(parts[1]);
        } catch (Exception e) {
            return -1;
        }
    }

    /**
     * 验证CIDR格式是否正确
     *
     * @param cidr CIDR网段字符串
     * @return 如果格式正确则返回true
     */
    public static boolean isValidCidr(String cidr) {
        try {
            String[] parts = cidr.split("/");
            if (parts.length != 2) {
                return false;
            }

            // 验证IP地址部分
            InetAddress network = InetAddresses.forString(parts[0]);

            // 验证前缀长度
            int prefixLength = Integer.parseInt(parts[1]);
            if (network instanceof Inet4Address) {
                return prefixLength >= 0 && prefixLength <= 32;
            } else if (network instanceof Inet6Address) {
                return prefixLength >= 0 && prefixLength <= 128;
            }

            return false;
        } catch (Exception e) {
            return false;
        }
    }
}
