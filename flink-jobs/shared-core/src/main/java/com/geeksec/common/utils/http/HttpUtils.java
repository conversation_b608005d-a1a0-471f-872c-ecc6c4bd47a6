package com.geeksec.common.utils.http;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;

/**
 * HTTP工具类
 * 提供HTTP请求相关的工具方法
 *
 * <AUTHOR>
 */
@Slf4j
public class HttpUtils {

    /**
     * HTTP状态码常量
     */
    private static final int HTTP_OK = 200;

    /**
     * 私有构造函数，防止实例化
     */
    private HttpUtils() {
        // 私有构造函数，防止实例化
    }

    /**
     * 发送POST请求并返回响应
     *
     * @param url        请求URL
     * @param jsonObject 请求体JSON对象
     * @return 响应JSON对象，失败时返回null
     */
    public static JSONObject sendPost(String url, JSONObject jsonObject) {
        String result = null;
        try {
            URL serverUrl = new URL(url);
            HttpURLConnection conn = (HttpURLConnection) serverUrl.openConnection();
            
            // 设置请求参数
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setRequestMethod("POST");
            conn.setUseCaches(false);
            conn.setRequestProperty("Content-type", "application/json");
            conn.setInstanceFollowRedirects(false);
            
            // 建立连接
            conn.connect();
            
            // 设置请求体
            String requestBody = jsonObject.toJSONString();
            log.debug("发送POST请求: {}, 请求体: {}", url, requestBody);
            
            // 获取返回值
            result = getResponse(conn, requestBody);
            
        } catch (IOException e) {
            log.error("发送POST请求失败，URL: {}", url, e);
            return null;
        }
        
        // 如果返回值是标准的JSON字符串，进行转换
        try {
            return JSONObject.parseObject(result);
        } catch (Exception e) {
            log.error("解析响应JSON失败，响应内容: {}", result, e);
            return null;
        }
    }

    /**
     * 发送POST请求（仅发送，不处理响应）
     *
     * @param url        请求URL
     * @param jsonObject 请求体JSON对象
     */
    public static void sendPostOnly(String url, JSONObject jsonObject) {
        try {
            URL serverUrl = new URL(url);
            HttpURLConnection conn = (HttpURLConnection) serverUrl.openConnection();
            
            // 设置请求参数
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setRequestMethod("POST");
            conn.setUseCaches(false);
            conn.setRequestProperty("Content-type", "application/json");
            conn.setInstanceFollowRedirects(false);
            
            // 建立连接
            conn.connect();
            
            // 设置请求体
            String requestBody = jsonObject.toJSONString();
            log.debug("发送POST请求（仅发送）: {}, 请求体: {}", url, requestBody);
            
            // 仅发送请求，不处理响应
            sendRequestOnly(conn, requestBody);
            
        } catch (IOException e) {
            log.error("发送POST请求失败，URL: {}", url, e);
        }
    }

    /**
     * 发送请求并获取响应
     *
     * @param connection HTTP连接
     * @param requestBody 请求体
     * @return 响应内容
     * @throws IOException IO异常
     */
    private static String getResponse(HttpURLConnection connection, String requestBody) throws IOException {
        StringBuilder buffer = new StringBuilder();
        
        // 发送请求体
        try (OutputStream outputStream = connection.getOutputStream()) {
            byte[] bytes = requestBody.getBytes(StandardCharsets.UTF_8);
            outputStream.write(bytes);
            outputStream.flush();
        }

        int responseCode = connection.getResponseCode();
        log.debug("HTTP响应码: {}", responseCode);
        
        if (responseCode != HTTP_OK) {
            try (InputStream errorStream = connection.getErrorStream()) {
                if (errorStream != null) {
                    JSONObject errorResult = new JSONObject();
                    errorResult.put("error_stream", "存在错误流");
                    errorResult.put("response_code", responseCode);
                    return errorResult.toString();
                }
            }
        }

        // 读取响应内容
        try (InputStream inputStream = connection.getInputStream();
             InputStreamReader inputStreamReader = new InputStreamReader(inputStream, StandardCharsets.UTF_8);
             BufferedReader bufferedReader = new BufferedReader(inputStreamReader)) {
            
            String line;
            while ((line = bufferedReader.readLine()) != null) {
                buffer.append(line);
            }
        }

        return buffer.toString();
    }

    /**
     * 仅发送请求，不处理响应
     *
     * @param connection HTTP连接
     * @param requestBody 请求体
     * @throws IOException IO异常
     */
    private static void sendRequestOnly(HttpURLConnection connection, String requestBody) throws IOException {
        try (OutputStream outputStream = connection.getOutputStream()) {
            byte[] bytes = requestBody.getBytes(StandardCharsets.UTF_8);
            outputStream.write(bytes);
            outputStream.flush();
        }
    }

    /**
     * 发送GET请求
     *
     * @param url 请求URL
     * @return 响应内容
     */
    public static String sendGet(String url) {
        StringBuilder result = new StringBuilder();
        
        try {
            URL getUrl = new URL(url);
            HttpURLConnection conn = (HttpURLConnection) getUrl.openConnection();
            
            // 设置请求参数
            conn.setRequestMethod("GET");
            conn.setUseCaches(false);
            conn.setRequestProperty("Accept", "application/json");
            conn.setRequestProperty("User-Agent", "Mozilla/5.0");
            
            int responseCode = conn.getResponseCode();
            log.debug("GET请求响应码: {}", responseCode);
            
            // 读取响应
            try (InputStream inputStream = (responseCode == HTTP_OK) ? conn.getInputStream() : conn.getErrorStream();
                 InputStreamReader inputStreamReader = new InputStreamReader(inputStream, StandardCharsets.UTF_8);
                 BufferedReader bufferedReader = new BufferedReader(inputStreamReader)) {
                
                String line;
                while ((line = bufferedReader.readLine()) != null) {
                    result.append(line);
                }
            }
            
        } catch (IOException e) {
            log.error("发送GET请求失败，URL: {}", url, e);
        }
        
        return result.toString();
    }

    /**
     * 设置连接超时时间
     *
     * @param connection HTTP连接
     * @param connectTimeout 连接超时时间（毫秒）
     * @param readTimeout 读取超时时间（毫秒）
     */
    public static void setTimeout(HttpURLConnection connection, int connectTimeout, int readTimeout) {
        connection.setConnectTimeout(connectTimeout);
        connection.setReadTimeout(readTimeout);
    }

    /**
     * 添加请求头
     *
     * @param connection HTTP连接
     * @param key 请求头键
     * @param value 请求头值
     */
    public static void addHeader(HttpURLConnection connection, String key, String value) {
        connection.setRequestProperty(key, value);
    }
}
