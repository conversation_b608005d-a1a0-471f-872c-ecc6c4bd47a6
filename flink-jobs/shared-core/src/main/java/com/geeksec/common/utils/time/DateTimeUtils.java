package com.geeksec.common.utils.time;

import lombok.extern.slf4j.Slf4j;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;
import java.util.Objects;

/**
 * 日期时间工具类
 * 提供基于Java 8+ 日期时间API的通用方法
 *
 * <AUTHOR> Team
 */
@Slf4j
public class DateTimeUtils {

    /**
     * 默认日期时间格式
     */
    public static final String DEFAULT_DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    public static final DateTimeFormatter DEFAULT_DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern(DEFAULT_DATE_TIME_FORMAT);

    /**
     * 默认日期格式
     */
    public static final String DEFAULT_DATE_FORMAT = "yyyy-MM-dd";
    public static final DateTimeFormatter DEFAULT_DATE_FORMATTER = DateTimeFormatter.ofPattern(DEFAULT_DATE_FORMAT);

    /**
     * 默认时间格式
     */
    public static final String DEFAULT_TIME_FORMAT = "HH:mm:ss";
    public static final DateTimeFormatter DEFAULT_TIME_FORMATTER = DateTimeFormatter.ofPattern(DEFAULT_TIME_FORMAT);

    /**
     * 私有构造函数，防止实例化
     */
    private DateTimeUtils() {
        throw new AssertionError("工具类不需要实例化");
    }

    /**
     * 将Date转换为LocalDateTime
     *
     * @param date Date对象
     * @return LocalDateTime对象
     */
    public static LocalDateTime toLocalDateTime(Date date) {
        if (date == null) {
            return null;
        }
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    /**
     * 将LocalDateTime转换为Date
     *
     * @param localDateTime LocalDateTime对象
     * @return Date对象
     */
    public static Date toDate(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        }
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 将LocalDate转换为Date
     *
     * @param localDate LocalDate对象
     * @return Date对象
     */
    public static Date toDate(LocalDate localDate) {
        if (localDate == null) {
            return null;
        }
        return Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 将字符串转换为LocalDateTime
     *
     * @param dateTimeStr 日期时间字符串
     * @param pattern 格式
     * @return LocalDateTime对象
     */
    public static LocalDateTime parseDateTime(String dateTimeStr, String pattern) {
        if (dateTimeStr == null || dateTimeStr.isEmpty()) {
            return null;
        }
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
            return LocalDateTime.parse(dateTimeStr, formatter);
        } catch (DateTimeParseException e) {
            log.error("解析日期时间字符串失败: {}", dateTimeStr, e);
            return null;
        }
    }

    /**
     * 将字符串转换为LocalDateTime，使用默认格式
     *
     * @param dateTimeStr 日期时间字符串
     * @return LocalDateTime对象
     */
    public static LocalDateTime parseDateTime(String dateTimeStr) {
        return parseDateTime(dateTimeStr, DEFAULT_DATE_TIME_FORMAT);
    }

    /**
     * 将字符串转换为LocalDate
     *
     * @param dateStr 日期字符串
     * @param pattern 格式
     * @return LocalDate对象
     */
    public static LocalDate parseDate(String dateStr, String pattern) {
        if (dateStr == null || dateStr.isEmpty()) {
            return null;
        }
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
            return LocalDate.parse(dateStr, formatter);
        } catch (DateTimeParseException e) {
            log.error("解析日期字符串失败: {}", dateStr, e);
            return null;
        }
    }

    /**
     * 将字符串转换为LocalDate，使用默认格式
     *
     * @param dateStr 日期字符串
     * @return LocalDate对象
     */
    public static LocalDate parseDate(String dateStr) {
        return parseDate(dateStr, DEFAULT_DATE_FORMAT);
    }

    /**
     * 将LocalDateTime格式化为字符串
     *
     * @param localDateTime LocalDateTime对象
     * @param pattern 格式
     * @return 格式化后的字符串
     */
    public static String formatDateTime(LocalDateTime localDateTime, String pattern) {
        if (localDateTime == null) {
            return null;
        }
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
            return localDateTime.format(formatter);
        } catch (Exception e) {
            log.error("格式化日期时间失败", e);
            return null;
        }
    }

    /**
     * 将LocalDateTime格式化为字符串，使用默认格式
     *
     * @param localDateTime LocalDateTime对象
     * @return 格式化后的字符串
     */
    public static String formatDateTime(LocalDateTime localDateTime) {
        return formatDateTime(localDateTime, DEFAULT_DATE_TIME_FORMAT);
    }

    /**
     * 将LocalDate格式化为字符串
     *
     * @param localDate LocalDate对象
     * @param pattern 格式
     * @return 格式化后的字符串
     */
    public static String formatDate(LocalDate localDate, String pattern) {
        if (localDate == null) {
            return null;
        }
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
            return localDate.format(formatter);
        } catch (Exception e) {
            log.error("格式化日期失败", e);
            return null;
        }
    }

    /**
     * 将LocalDate格式化为字符串，使用默认格式
     *
     * @param localDate LocalDate对象
     * @return 格式化后的字符串
     */
    public static String formatDate(LocalDate localDate) {
        return formatDate(localDate, DEFAULT_DATE_FORMAT);
    }

    /**
     * 将Date格式化为字符串
     *
     * @param date Date对象
     * @param pattern 格式
     * @return 格式化后的字符串
     */
    public static String formatDate(Date date, String pattern) {
        if (date == null) {
            return null;
        }
        return formatDateTime(toLocalDateTime(date), pattern);
    }

    /**
     * 获取当前日期时间
     *
     * @return 当前日期时间
     */
    public static LocalDateTime now() {
        return LocalDateTime.now();
    }

    /**
     * 获取当前日期
     *
     * @return 当前日期
     */
    public static LocalDate today() {
        return LocalDate.now();
    }

    /**
     * 获取当前时间的字符串表示，使用默认格式
     *
     * @return 当前时间的字符串表示
     */
    public static String getCurrentDateTime() {
        return formatDateTime(now());
    }

    /**
     * 获取当前日期的字符串表示，使用默认格式
     *
     * @return 当前日期的字符串表示
     */
    public static String getCurrentDate() {
        return formatDate(today());
    }

    /**
     * 获取当前日期的字符串表示
     * 从TimeFormatter合并的方法
     *
     * @return 当前日期的字符串表示，格式为yyyy-MM-dd
     */
    public static String getDateByString() {
        return formatDate(today());
    }

    /**
     * 获取当前日期的字符串表示，使用指定格式
     * 从TimeFormatter合并的方法
     *
     * @param pattern 日期格式
     * @return 当前日期的字符串表示
     */
    public static String getDateByString(String pattern) {
        try {
            return formatDate(today(), pattern);
        } catch (Exception e) {
            log.error("无效的日期格式: {}", pattern, e);
            return getDateByString();
        }
    }

    /**
     * 获取两个日期时间之间的天数差
     *
     * @param start 开始时间
     * @param end 结束时间
     * @return 天数差
     */
    public static long daysBetween(LocalDateTime start, LocalDateTime end) {
        if (start == null || end == null) {
            return 0;
        }
        return ChronoUnit.DAYS.between(start, end);
    }

    /**
     * 获取两个日期之间的天数差
     *
     * @param start 开始日期
     * @param end 结束日期
     * @return 天数差
     */
    public static long daysBetween(LocalDate start, LocalDate end) {
        if (start == null || end == null) {
            return 0;
        }
        return ChronoUnit.DAYS.between(start, end);
    }

    /**
     * 获取两个日期时间之间的小时差
     *
     * @param start 开始时间
     * @param end 结束时间
     * @return 小时差
     */
    public static long hoursBetween(LocalDateTime start, LocalDateTime end) {
        if (start == null || end == null) {
            return 0;
        }
        return ChronoUnit.HOURS.between(start, end);
    }

    /**
     * 获取两个日期时间之间的分钟差
     *
     * @param start 开始时间
     * @param end 结束时间
     * @return 分钟差
     */
    public static long minutesBetween(LocalDateTime start, LocalDateTime end) {
        if (start == null || end == null) {
            return 0;
        }
        return ChronoUnit.MINUTES.between(start, end);
    }

    /**
     * 获取两个日期时间之间的秒数差
     *
     * @param start 开始时间
     * @param end 结束时间
     * @return 秒数差
     */
    public static long secondsBetween(LocalDateTime start, LocalDateTime end) {
        if (start == null || end == null) {
            return 0;
        }
        return ChronoUnit.SECONDS.between(start, end);
    }

    /**
     * 获取当前时间戳（毫秒）
     *
     * @return 当前时间戳
     */
    public static long currentTimestamp() {
        return System.currentTimeMillis();
    }

    /**
     * 获取当前时间戳（秒）
     *
     * @return 当前时间戳
     */
    public static long currentTimestampSeconds() {
        return System.currentTimeMillis() / 1000;
    }

    /**
     * 获取指定日期的开始时间（00:00:00）
     *
     * @param date 日期
     * @return 该日期的开始时间
     */
    public static LocalDateTime startOfDay(LocalDate date) {
        return Objects.requireNonNull(date).atStartOfDay();
    }

    /**
     * 获取指定日期的结束时间（23:59:59.999999999）
     *
     * @param date 日期
     * @return 该日期的结束时间
     */
    public static LocalDateTime endOfDay(LocalDate date) {
        return Objects.requireNonNull(date).atTime(LocalTime.MAX);
    }

    /**
     * 获取指定日期所在月份的第一天
     *
     * @param date 日期
     * @return 所在月份的第一天
     */
    public static LocalDate firstDayOfMonth(LocalDate date) {
        return Objects.requireNonNull(date).with(TemporalAdjusters.firstDayOfMonth());
    }

    /**
     * 获取指定日期所在月份的最后一天
     *
     * @param date 日期
     * @return 所在月份的最后一天
     */
    public static LocalDate lastDayOfMonth(LocalDate date) {
        return Objects.requireNonNull(date).with(TemporalAdjusters.lastDayOfMonth());
    }

    /**
     * 将时间戳（毫秒）转换为LocalDateTime
     *
     * @param timestamp 时间戳（毫秒）
     * @return LocalDateTime对象
     */
    public static LocalDateTime fromTimestamp(long timestamp) {
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault());
    }

    /**
     * 将时间戳（秒）转换为LocalDateTime
     *
     * @param timestamp 时间戳（秒）
     * @return LocalDateTime对象
     */
    public static LocalDateTime fromTimestampSeconds(long timestamp) {
        return LocalDateTime.ofInstant(Instant.ofEpochSecond(timestamp), ZoneId.systemDefault());
    }

    /**
     * 将LocalDateTime转换为时间戳（毫秒）
     *
     * @param localDateTime LocalDateTime对象
     * @return 时间戳（毫秒）
     */
    public static long toTimestamp(LocalDateTime localDateTime) {
        return Objects.requireNonNull(localDateTime).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    /**
     * 将LocalDateTime转换为时间戳（秒）
     *
     * @param localDateTime LocalDateTime对象
     * @return 时间戳（秒）
     */
    public static long toTimestampSeconds(LocalDateTime localDateTime) {
        return Objects.requireNonNull(localDateTime).atZone(ZoneId.systemDefault()).toInstant().getEpochSecond();
    }

    /**
     * 将毫秒时间戳转换为LocalDate (使用系统默认时区)
     *
     * @param timestampMs 毫秒时间戳
     * @return LocalDate对象，如果时间戳为0或负数则返回null (可根据需求调整)
     * <AUTHOR>
     */
    public static LocalDate getLocalDateFromTimestamp(long timestampMs) {
        if (timestampMs <= 0) {
            // 或者可以根据需求抛出异常或返回当前日期
            log.warn("无效的毫秒时间戳: {}, 将返回null", timestampMs);
            return null;
        }
        return Instant.ofEpochMilli(timestampMs)
                .atZone(ZoneId.systemDefault())
                .toLocalDate();
    }

    /**
     * 将毫秒时间戳格式化为默认的日期时间字符串 "yyyy-MM-dd HH:mm:ss" (使用系统默认时区)
     *
     * @param timestampMs 毫秒时间戳
     * @return 格式化后的日期时间字符串，如果时间戳为0或负数则返回null (可根据需求调整)
     * <AUTHOR>
     */
    public static String formatMillisToDateTime(long timestampMs) {
        if (timestampMs <= 0) {
            log.warn("无效的毫秒时间戳: {}, 将返回null", timestampMs);
            return null;
        }
        LocalDateTime localDateTime = Instant.ofEpochMilli(timestampMs)
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();
        return localDateTime.format(DEFAULT_DATE_TIME_FORMATTER);
    }
}
