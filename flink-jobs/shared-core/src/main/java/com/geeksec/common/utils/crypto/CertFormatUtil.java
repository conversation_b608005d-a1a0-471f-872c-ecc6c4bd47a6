package com.geeksec.common.utils.crypto;

import lombok.extern.slf4j.Slf4j;

import javax.security.auth.x500.X500Principal;
import java.io.ByteArrayInputStream;
import java.security.MessageDigest;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.regex.Pattern;

/**
 * 证书格式化工具类
 * 提供证书相关的格式化和验证功能
 *
 * <AUTHOR>
 */
@Slf4j
public class CertFormatUtil {

    private static final Pattern DOMAIN_PATTERN = Pattern.compile(
        "^(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\\.)+" +
        "[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?$"
    );

    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    /**
     * 私有构造函数，防止实例化
     */
    private CertFormatUtil() {
        // 私有构造函数，防止实例化
    }

    /**
     * 格式化证书主题
     *
     * @param subject 证书主题
     * @return 格式化后的主题
     */
    public static String formatSubject(String subject) {
        if (subject == null || subject.trim().isEmpty()) {
            return "";
        }
        
        try {
            // 移除多余的空格和换行符
            return subject.replaceAll("\\s+", " ").trim();
        } catch (Exception e) {
            log.error("格式化证书主题失败: {}", subject, e);
            return subject;
        }
    }

    /**
     * 格式化证书颁发者
     *
     * @param issuer 证书颁发者
     * @return 格式化后的颁发者
     */
    public static String formatIssuer(String issuer) {
        if (issuer == null || issuer.trim().isEmpty()) {
            return "";
        }
        
        try {
            // 移除多余的空格和换行符
            return issuer.replaceAll("\\s+", " ").trim();
        } catch (Exception e) {
            log.error("格式化证书颁发者失败: {}", issuer, e);
            return issuer;
        }
    }

    /**
     * 格式化证书序列号
     *
     * @param serialNumber 证书序列号
     * @return 格式化后的序列号
     */
    public static String formatSerialNumber(String serialNumber) {
        if (serialNumber == null || serialNumber.trim().isEmpty()) {
            return "";
        }
        
        try {
            // 转换为大写并移除冒号
            return serialNumber.toUpperCase().replaceAll(":", "");
        } catch (Exception e) {
            log.error("格式化证书序列号失败: {}", serialNumber, e);
            return serialNumber;
        }
    }

    /**
     * 格式化日期
     *
     * @param date 日期
     * @return 格式化后的日期字符串
     */
    public static String formatDate(Date date) {
        if (date == null) {
            return "";
        }
        
        try {
            return DATE_FORMAT.format(date);
        } catch (Exception e) {
            log.error("格式化日期失败: {}", date, e);
            return date.toString();
        }
    }

    /**
     * 验证域名格式
     *
     * @param domain 域名
     * @return 是否为有效域名
     */
    public static boolean isValidDomain(String domain) {
        if (domain == null || domain.trim().isEmpty()) {
            return false;
        }
        
        try {
            String trimmedDomain = domain.trim().toLowerCase();
            
            // 检查长度
            if (trimmedDomain.length() > 253) {
                return false;
            }
            
            // 检查格式
            return DOMAIN_PATTERN.matcher(trimmedDomain).matches();
        } catch (Exception e) {
            log.error("验证域名格式失败: {}", domain, e);
            return false;
        }
    }

    /**
     * 提取证书中的域名
     *
     * @param cert X509证书
     * @return 主域名
     */
    public static String extractDomainFromCert(X509Certificate cert) {
        if (cert == null) {
            return "";
        }
        
        try {
            String subject = cert.getSubjectDN().getName();
            
            // 查找CN字段
            String[] parts = subject.split(",");
            for (String part : parts) {
                String trimmedPart = part.trim();
                if (trimmedPart.startsWith("CN=")) {
                    String domain = trimmedPart.substring(3).trim();
                    if (isValidDomain(domain)) {
                        return domain.toLowerCase();
                    }
                }
            }
            
            return "";
        } catch (Exception e) {
            log.error("从证书提取域名失败", e);
            return "";
        }
    }

    /**
     * 检查证书是否过期
     *
     * @param cert X509证书
     * @return 是否过期
     */
    public static boolean isCertExpired(X509Certificate cert) {
        if (cert == null) {
            return true;
        }
        
        try {
            Date now = new Date();
            Date notAfter = cert.getNotAfter();
            return now.after(notAfter);
        } catch (Exception e) {
            log.error("检查证书过期状态失败", e);
            return true;
        }
    }

    /**
     * 检查证书是否还未生效
     *
     * @param cert X509证书
     * @return 是否还未生效
     */
    public static boolean isCertNotYetValid(X509Certificate cert) {
        if (cert == null) {
            return true;
        }
        
        try {
            Date now = new Date();
            Date notBefore = cert.getNotBefore();
            return now.before(notBefore);
        } catch (Exception e) {
            log.error("检查证书生效状态失败", e);
            return true;
        }
    }

    /**
     * 获取证书有效期天数
     *
     * @param cert X509证书
     * @return 有效期天数，-1表示计算失败
     */
    public static long getCertValidityDays(X509Certificate cert) {
        if (cert == null) {
            return -1;
        }
        
        try {
            Date notBefore = cert.getNotBefore();
            Date notAfter = cert.getNotAfter();
            
            long diffInMillies = notAfter.getTime() - notBefore.getTime();
            return diffInMillies / (24 * 60 * 60 * 1000);
        } catch (Exception e) {
            log.error("计算证书有效期失败", e);
            return -1;
        }
    }

    /**
     * 获取证书剩余有效期天数
     *
     * @param cert X509证书
     * @return 剩余有效期天数，负数表示已过期
     */
    public static long getCertRemainingDays(X509Certificate cert) {
        if (cert == null) {
            return -1;
        }

        try {
            Date now = new Date();
            Date notAfter = cert.getNotAfter();

            long diffInMillies = notAfter.getTime() - now.getTime();
            return diffInMillies / (24 * 60 * 60 * 1000);
        } catch (Exception e) {
            log.error("计算证书剩余有效期失败", e);
            return -1;
        }
    }

    /**
     * 获取DER格式证书的哈希值
     *
     * @param derBytes DER编码的证书字节
     * @param algorithm 哈希算法（如 "MD5", "SHA", "SHA-1", "SHA-256"）
     * @return 哈希值的十六进制字符串
     */
    public static String getDerCertHash(byte[] derBytes, String algorithm) {
        if (derBytes == null || derBytes.length == 0) {
            return "";
        }

        try {
            MessageDigest digest = MessageDigest.getInstance(algorithm);
            byte[] hashBytes = digest.digest(derBytes);

            StringBuilder hexString = new StringBuilder();
            for (byte b : hashBytes) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString().toUpperCase();
        } catch (Exception e) {
            log.error("计算证书哈希值失败: algorithm={}", algorithm, e);
            return "";
        }
    }

    /**
     * 获取证书主题或颁发者的OID映射
     *
     * @param cert X509证书
     * @param type "subject" 或 "issuer"
     * @return OID到值的映射
     */
    public static LinkedHashMap<String, String> getNameOIDMap(X509Certificate cert, String type) {
        LinkedHashMap<String, String> result = new LinkedHashMap<>();

        if (cert == null) {
            return result;
        }

        try {
            X500Principal principal = "subject".equalsIgnoreCase(type)
                ? cert.getSubjectX500Principal()
                : cert.getIssuerX500Principal();

            String name = principal.getName();
            String[] parts = name.split(",");

            for (String part : parts) {
                String trimmed = part.trim();
                int equalIndex = trimmed.indexOf('=');
                if (equalIndex > 0) {
                    String key = trimmed.substring(0, equalIndex).trim();
                    String value = trimmed.substring(equalIndex + 1).trim();
                    result.put(key, value);
                }
            }
        } catch (Exception e) {
            log.error("解析证书{}失败", type, e);
        }

        return result;
    }

    /**
     * 从键值映射中读取数据
     *
     * @param map 键值映射
     * @param key 要查找的键
     * @param defaultValue 默认值
     * @return 找到的值或默认值
     */
    public static String readDataFromKeys(LinkedHashMap<String, String> map, String key, String defaultValue) {
        if (map == null || key == null) {
            return defaultValue;
        }
        return map.getOrDefault(key, defaultValue);
    }

    /**
     * 格式化时间
     *
     * @param date 日期对象
     * @return 格式化后的时间字符串
     */
    public static String timeFormat(Date date) {
        if (date == null) {
            return "";
        }

        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return formatter.format(date);
    }

    /**
     * 从字节数组获取X509证书
     *
     * @param certBytes 证书字节数组
     * @return X509证书对象
     * @throws CertificateException 证书解析异常
     */
    public static X509Certificate getCertificate(byte[] certBytes) throws CertificateException {
        if (certBytes == null || certBytes.length == 0) {
            throw new CertificateException("证书字节数组为空");
        }

        try {
            CertificateFactory factory = CertificateFactory.getInstance("X.509");
            ByteArrayInputStream inputStream = new ByteArrayInputStream(certBytes);
            return (X509Certificate) factory.generateCertificate(inputStream);
        } catch (Exception e) {
            throw new CertificateException("解析证书失败", e);
        }
    }
}
