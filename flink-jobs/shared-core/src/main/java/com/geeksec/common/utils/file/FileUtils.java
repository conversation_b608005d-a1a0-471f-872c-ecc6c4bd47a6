package com.geeksec.common.utils.file;

import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * 文件工具类
 * 提供文件读取和解析相关的工具方法
 *
 * <AUTHOR>
 */
@Slf4j
public class FileUtils {

    /**
     * 私有构造函数，防止实例化
     */
    private FileUtils() {
        // 私有构造函数，防止实例化
    }

    /**
     * 从类路径加载Properties文件
     *
     * @param propertiesPath Properties文件路径
     * @return Properties对象
     */
    public static Properties loadProperties(String propertiesPath) {
        Properties properties = new Properties();
        
        try (InputStream inputStream = FileUtils.class.getResourceAsStream(propertiesPath);
             BufferedInputStream bufferedInputStream = new BufferedInputStream(inputStream)) {
            
            properties.load(bufferedInputStream);
            log.debug("成功加载Properties文件: {}", propertiesPath);
            
        } catch (IOException e) {
            log.error("加载Properties文件失败: {}", propertiesPath, e);
        } catch (Exception e) {
            log.error("加载Properties文件时发生未知错误: {}", propertiesPath, e);
        }
        
        return properties;
    }

    /**
     * 读取文本文件内容
     *
     * @param filePath 文件路径
     * @return 文件内容字符串
     */
    public static String readTextFile(String filePath) {
        StringBuilder content = new StringBuilder();
        
        try (InputStream inputStream = FileUtils.class.getResourceAsStream(filePath);
             InputStreamReader inputStreamReader = new InputStreamReader(inputStream, StandardCharsets.UTF_8);
             BufferedReader bufferedReader = new BufferedReader(inputStreamReader)) {
            
            String line;
            boolean isFirstLine = true;
            while ((line = bufferedReader.readLine()) != null) {
                if (!isFirstLine) {
                    content.append("\n");
                }
                content.append(line);
                isFirstLine = false;
            }
            
            log.debug("成功读取文本文件: {}", filePath);
            
        } catch (IOException e) {
            log.error("读取文本文件失败: {}", filePath, e);
        } catch (Exception e) {
            log.error("读取文本文件时发生未知错误: {}", filePath, e);
        }
        
        return content.toString();
    }

    /**
     * 读取文本文件并返回行列表
     *
     * @param filePath 文件路径
     * @return 行列表
     */
    public static List<String> readTextFileLines(String filePath) {
        List<String> lines = new ArrayList<>();
        
        try (InputStream inputStream = FileUtils.class.getResourceAsStream(filePath);
             InputStreamReader inputStreamReader = new InputStreamReader(inputStream, StandardCharsets.UTF_8);
             BufferedReader bufferedReader = new BufferedReader(inputStreamReader)) {
            
            String line;
            while ((line = bufferedReader.readLine()) != null) {
                lines.add(line);
            }
            
            log.debug("成功读取文本文件行: {}, 共{}行", filePath, lines.size());
            
        } catch (IOException e) {
            log.error("读取文本文件行失败: {}", filePath, e);
        } catch (Exception e) {
            log.error("读取文本文件行时发生未知错误: {}", filePath, e);
        }
        
        return lines;
    }

    /**
     * 解析CSV文件为Map列表（第一行作为标题）
     *
     * @param filePath CSV文件路径
     * @param delimiter 分隔符，默认为逗号
     * @return Map列表，每个Map代表一行数据
     */
    public static List<Map<String, String>> parseCsvToMapList(String filePath, String delimiter) {
        List<Map<String, String>> result = new ArrayList<>();
        
        try (InputStream inputStream = FileUtils.class.getResourceAsStream(filePath);
             InputStreamReader inputStreamReader = new InputStreamReader(inputStream, StandardCharsets.UTF_8);
             BufferedReader bufferedReader = new BufferedReader(inputStreamReader)) {
            
            String headerLine = bufferedReader.readLine();
            if (headerLine == null) {
                log.warn("CSV文件为空: {}", filePath);
                return result;
            }
            
            String[] headers = headerLine.trim().split(delimiter);
            String line;
            int rowCount = 0;
            
            while ((line = bufferedReader.readLine()) != null) {
                String[] values = line.trim().split(delimiter);
                if (values.length > 0 && !values[0].isEmpty()) {
                    Map<String, String> rowMap = new HashMap<>(headers.length);
                    for (int i = 0; i < Math.min(headers.length, values.length); i++) {
                        rowMap.put(headers[i], values[i]);
                    }
                    result.add(rowMap);
                    rowCount++;
                }
            }
            
            log.debug("成功解析CSV文件: {}, 共{}行数据", filePath, rowCount);
            
        } catch (IOException e) {
            log.error("解析CSV文件失败: {}", filePath, e);
        } catch (Exception e) {
            log.error("解析CSV文件时发生未知错误: {}", filePath, e);
        }
        
        return result;
    }

    /**
     * 解析CSV文件为Map列表（使用默认逗号分隔符）
     *
     * @param filePath CSV文件路径
     * @return Map列表，每个Map代表一行数据
     */
    public static List<Map<String, String>> parseCsvToMapList(String filePath) {
        return parseCsvToMapList(filePath, ",");
    }

    /**
     * 解析简单的键值对CSV文件
     *
     * @param filePath CSV文件路径
     * @param keyColumnIndex 键列索引
     * @param valueColumnIndex 值列索引
     * @param hasHeader 是否有标题行
     * @param delimiter 分隔符
     * @return 键值对Map
     */
    public static Map<String, String> parseKeyValueCsv(String filePath, int keyColumnIndex, 
                                                       int valueColumnIndex, boolean hasHeader, String delimiter) {
        Map<String, String> result = new HashMap<>(16);
        
        try (InputStream inputStream = FileUtils.class.getResourceAsStream(filePath);
             InputStreamReader inputStreamReader = new InputStreamReader(inputStream, StandardCharsets.UTF_8);
             BufferedReader bufferedReader = new BufferedReader(inputStreamReader)) {
            
            String line;
            boolean isFirstLine = true;
            
            while ((line = bufferedReader.readLine()) != null) {
                // 跳过标题行
                if (isFirstLine && hasHeader) {
                    isFirstLine = false;
                    continue;
                }
                
                String[] values = line.trim().split(delimiter);
                if (values.length > Math.max(keyColumnIndex, valueColumnIndex)) {
                    String key = values[keyColumnIndex];
                    String value = values[valueColumnIndex];
                    if (!key.isEmpty()) {
                        result.put(key, value);
                    }
                }
                isFirstLine = false;
            }
            
            log.debug("成功解析键值对CSV文件: {}, 共{}条记录", filePath, result.size());
            
        } catch (IOException e) {
            log.error("解析键值对CSV文件失败: {}", filePath, e);
        } catch (Exception e) {
            log.error("解析键值对CSV文件时发生未知错误: {}", filePath, e);
        }
        
        return result;
    }

    /**
     * 解析简单的键值对CSV文件（使用默认参数）
     *
     * @param filePath CSV文件路径
     * @return 键值对Map（第一列为键，第二列为值，有标题行）
     */
    public static Map<String, String> parseKeyValueCsv(String filePath) {
        return parseKeyValueCsv(filePath, 0, 1, true, ",");
    }

    /**
     * 读取单列CSV文件为字符串列表
     *
     * @param filePath CSV文件路径
     * @param columnIndex 列索引
     * @param hasHeader 是否有标题行
     * @param delimiter 分隔符
     * @return 字符串列表
     */
    public static List<String> readSingleColumnCsv(String filePath, int columnIndex, 
                                                   boolean hasHeader, String delimiter) {
        List<String> result = new ArrayList<>();
        
        try (InputStream inputStream = FileUtils.class.getResourceAsStream(filePath);
             InputStreamReader inputStreamReader = new InputStreamReader(inputStream, StandardCharsets.UTF_8);
             BufferedReader bufferedReader = new BufferedReader(inputStreamReader)) {
            
            String line;
            boolean isFirstLine = true;
            
            while ((line = bufferedReader.readLine()) != null) {
                // 跳过标题行
                if (isFirstLine && hasHeader) {
                    isFirstLine = false;
                    continue;
                }
                
                String[] values = line.trim().split(delimiter);
                if (values.length > columnIndex && !values[columnIndex].isEmpty()) {
                    result.add(values[columnIndex]);
                }
                isFirstLine = false;
            }
            
            log.debug("成功读取单列CSV文件: {}, 共{}条记录", filePath, result.size());
            
        } catch (IOException e) {
            log.error("读取单列CSV文件失败: {}", filePath, e);
        } catch (Exception e) {
            log.error("读取单列CSV文件时发生未知错误: {}", filePath, e);
        }
        
        return result;
    }

    /**
     * 检查文件是否存在于类路径中
     *
     * @param filePath 文件路径
     * @return 如果文件存在则返回true，否则返回false
     */
    public static boolean existsInClasspath(String filePath) {
        try (InputStream inputStream = FileUtils.class.getResourceAsStream(filePath)) {
            return inputStream != null;
        } catch (IOException e) {
            log.debug("检查文件存在性时发生IO异常: {}", filePath, e);
            return false;
        }
    }
}
