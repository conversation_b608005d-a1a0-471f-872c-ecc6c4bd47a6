package com.geeksec.common.utils.knowledgebase;

import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Tranco域名排名管理器
 *
 * Tranco是一个研究导向的域名排名列表，整合了多个数据源，
 * 具有更好的抗操纵性和研究价值
 *
 * <AUTHOR>
 */
@Slf4j
public class TrancoRankingManager {

    /**
     * 单例实例
     */
    private static volatile TrancoRankingManager instance = null;

    /**
     * 域名排名映射 (域名 -> 排名)
     */
    private final Map<String, Integer> domainRankMap = new ConcurrentHashMap<>();

    /**
     * Tranco数据文件名
     */
    private static final String TRANCO_DATA_FILE = "tranco_NNJPW.csv";

    /**
     * 知识库资源路径（支持外部挂载）
     */
    private static final String KNOWLEDGE_BASE_PATH = System.getProperty("knowledgebase.path",
        System.getenv("KNOWLEDGE_BASE_PATH") != null ? System.getenv("KNOWLEDGE_BASE_PATH") : "/opt/flink/data/knowledge-base");

    /**
     * Tranco数据完整路径
     */
    private static final String TRANCO_DATA_PATH = KNOWLEDGE_BASE_PATH + "/tranco/" + TRANCO_DATA_FILE;

    /**
     * 默认排名值（未找到排名时返回）
     */
    private static final int DEFAULT_RANK = 0;



    /**
     * 私有构造函数
     */
    private TrancoRankingManager() {
        initialize();
    }

    /**
     * 获取单例实例
     *
     * @return TrancoRankingManager实例
     */
    public static TrancoRankingManager getInstance() {
        if (instance == null) {
            synchronized (TrancoRankingManager.class) {
                if (instance == null) {
                    instance = new TrancoRankingManager();
                }
            }
        }
        return instance;
    }

    /**
     * 初始化Tranco排名数据
     */
    private void initialize() {
        try {
            loadTrancoRankingData();
            log.info("Tranco域名排名管理器初始化成功，共加载 {} 条排名记录", domainRankMap.size());
        } catch (Exception e) {
            log.error("初始化Tranco域名排名管理器失败", e);
        }
    }

    /**
     * 加载Tranco排名数据
     */
    private void loadTrancoRankingData() throws IOException {
        domainRankMap.clear();

        if (!loadFromFile(TRANCO_DATA_PATH)) {
            throw new IOException("无法加载Tranco数据文件");
        }
    }

    /**
     * 从文件路径加载Tranco数据
     */
    private boolean loadFromFile(String filePath) {
        try {
            java.io.File file = new java.io.File(filePath);
            if (!file.exists() || !file.canRead()) {
                log.error("Tranco数据文件不存在或不可读：{}", filePath);
                return false;
            }

            try (BufferedReader reader = new BufferedReader(
                    new java.io.FileReader(file, StandardCharsets.UTF_8))) {

                int lineCount = parseRankingData(reader);
                log.info("成功加载Tranco排名数据：{}，处理了 {} 行，有效记录 {} 条",
                        filePath, lineCount, domainRankMap.size());
                return true;
            }
        } catch (IOException e) {
            log.error("加载Tranco数据失败：{}", filePath, e);
            return false;
        }
    }

    /**
     * 解析排名数据
     */
    private int parseRankingData(BufferedReader reader) throws IOException {
        String line;
        int lineCount = 0;

        while ((line = reader.readLine()) != null) {
            lineCount++;

            // 跳过空行
            if (line.trim().isEmpty()) {
                continue;
            }

            try {
                // 解析CSV格式: 排名,域名
                String[] parts = line.split(",", 2);
                if (parts.length == 2) {
                    int rank = Integer.parseInt(parts[0].trim());
                    String domain = parts[1].trim();

                    if (!domain.isEmpty()) {
                        domainRankMap.put(domain, rank);
                    }
                }
            } catch (NumberFormatException e) {
                log.warn("解析Tranco数据第{}行失败: {}", lineCount, line);
            }
        }

        return lineCount;
    }

    /**
     * 获取域名的Tranco排名
     *
     * @param domain 域名
     * @return 域名排名，如果不存在则返回0
     */
    public int getDomainRank(String domain) {
        if (domain == null || domain.trim().isEmpty()) {
            return DEFAULT_RANK;
        }

        return domainRankMap.getOrDefault(domain.trim().toLowerCase(), DEFAULT_RANK);
    }

    /**
     * 检查域名是否在Tranco排名中
     *
     * @param domain 域名
     * @return 如果域名在排名中返回true，否则返回false
     */
    public boolean isRankedDomain(String domain) {
        if (domain == null || domain.trim().isEmpty()) {
            return false;
        }

        return domainRankMap.containsKey(domain.trim().toLowerCase());
    }

    /**
     * 获取排名数据的总数量
     *
     * @return 排名数据总数
     */
    public int getTotalRankingCount() {
        return domainRankMap.size();
    }

    /**
     * 刷新排名数据缓存
     */
    public void refresh() {
        try {
            loadTrancoRankingData();
            log.info("Tranco排名数据刷新成功，共 {} 条记录", domainRankMap.size());
        } catch (Exception e) {
            log.error("刷新Tranco排名数据失败", e);
        }
    }


}
