package com.geeksec.common.utils.net;

import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 协议管理器
 * 提供统一的协议名称解析功能，基于IANA协议号注册表硬编码协议定义
 * 参考: https://www.iana.org/assignments/protocol-numbers/protocol-numbers.xhtml
 *
 * <AUTHOR> Team
 */
@Slf4j
public class ProtocolManager {

    /**
     * 单例实例
     */
    private static volatile ProtocolManager instance;

    /**
     * 协议名称映射
     */
    private final Map<Integer, String> protocolNames = new ConcurrentHashMap<>();

    /**
     * 私有构造函数
     */
    private ProtocolManager() {
        initialize();
    }

    /**
     * 获取ProtocolManager单例实例
     *
     * @return ProtocolManager实例
     */
    public static ProtocolManager getInstance() {
        if (instance == null) {
            synchronized (ProtocolManager.class) {
                if (instance == null) {
                    instance = new ProtocolManager();
                }
            }
        }
        return instance;
    }

    /**
     * 初始化协议管理器
     */
    private void initialize() {
        try {
            initializeProtocols();
            log.info("协议名称数据加载成功，共加载 {} 条记录", protocolNames.size());
        } catch (Exception e) {
            log.error("初始化协议管理器失败", e);
        }
    }

    /**
     * 初始化所有协议定义
     * 基于IANA协议号注册表 (https://www.iana.org/assignments/protocol-numbers/protocol-numbers.xhtml)
     */
    private void initializeProtocols() {
        addBasicProtocols();
        addIpv6Protocols();
        addRoutingProtocols();
        addEncapsulationProtocols();
        addTransportProtocols();
        addExperimentalProtocols();
    }

    /**
     * 添加基本协议
     */
    private void addBasicProtocols() {
        /* IPv6 Hop-by-Hop Option */
        protocolNames.put(0, "HOPOPT");

        /* Internet Control Message Protocol */
        protocolNames.put(1, "ICMP");

        /* Internet Group Management Protocol */
        protocolNames.put(2, "IGMP");

        /* Gateway-to-Gateway Protocol */
        protocolNames.put(3, "GGP");

        /* IPv4 encapsulation */
        protocolNames.put(4, "IPv4");

        /* Stream */
        protocolNames.put(5, "ST");

        /* Transmission Control Protocol */
        protocolNames.put(6, "TCP");

        /* CBT */
        protocolNames.put(7, "CBT");

        /* Exterior Gateway Protocol */
        protocolNames.put(8, "EGP");

        /* Interior Gateway Protocol (any private interior gateway) */
        protocolNames.put(9, "IGP");

        /* BBN RCC Monitoring */
        protocolNames.put(10, "BBN-RCC-MON");

        /* Network Voice Protocol */
        protocolNames.put(11, "NVP-II");

        /* PUP */
        protocolNames.put(12, "PUP");

        /* User Datagram Protocol */
        protocolNames.put(17, "UDP");

        /* Multiplexing */
        protocolNames.put(18, "MUX");
    }

    /**
     * 添加IPv6相关协议
     */
    private void addIpv6Protocols() {
        /* IPv6 encapsulation */
        protocolNames.put(41, "IPv6");

        /* Routing Header for IPv6 */
        protocolNames.put(43, "IPv6-Route");

        /* Fragment Header for IPv6 */
        protocolNames.put(44, "IPv6-Frag");

        /* ICMP for IPv6 */
        protocolNames.put(58, "IPv6-ICMP");

        /* No Next Header for IPv6 */
        protocolNames.put(59, "IPv6-NoNxt");

        /* Destination Options for IPv6 */
        protocolNames.put(60, "IPv6-Opts");
    }

    /**
     * 添加路由协议
     */
    private void addRoutingProtocols() {
        /* Reliable Data Protocol */
        protocolNames.put(27, "RDP");

        /* Internet Reliable Transaction Protocol */
        protocolNames.put(28, "IRTP");

        /* ISO Transport Protocol Class 4 */
        protocolNames.put(29, "ISO-TP4");

        /* Inter-Domain Routing Protocol */
        protocolNames.put(45, "IDRP");

        /* Reservation Protocol */
        protocolNames.put(46, "RSVP");

        /* Dynamic Source Routing Protocol */
        protocolNames.put(48, "DSR");

        /* Enhanced Interior Gateway Routing Protocol */
        protocolNames.put(88, "EIGRP");

        /* Open Shortest Path First */
        protocolNames.put(89, "OSPF");

        /* Protocol Independent Multicast */
        protocolNames.put(103, "PIM");

        /* IS-IS over IPv4 */
        protocolNames.put(124, "ISIS");
    }

    /**
     * 添加封装协议
     */
    private void addEncapsulationProtocols() {
        /* Generic Routing Encapsulation */
        protocolNames.put(47, "GRE");

        /* Encapsulating Security Payload */
        protocolNames.put(50, "ESP");

        /* Authentication Header */
        protocolNames.put(51, "AH");

        /* NBMA Address Resolution Protocol */
        protocolNames.put(54, "NARP");

        /* Minimal IPv4 Encapsulation */
        protocolNames.put(55, "Min-IPv4");

        /* IP-within-IP Encapsulation Protocol */
        protocolNames.put(94, "IPIP");

        /* Ethernet-within-IP Encapsulation */
        protocolNames.put(97, "ETHERIP");

        /* Encapsulation Header */
        protocolNames.put(98, "ENCAP");

        /* IP Payload Compression Protocol */
        protocolNames.put(108, "IPComp");

        /* Wrapped Encapsulating Security Payload */
        protocolNames.put(141, "WESP");
    }

    /**
     * 添加传输协议
     */
    private void addTransportProtocols() {
        /* Bulk Data Transfer Protocol */
        protocolNames.put(30, "NETBLT");

        /* Datagram Congestion Control Protocol */
        protocolNames.put(33, "DCCP");

        /* Xpress Transfer Protocol */
        protocolNames.put(36, "XTP");

        /* Datagram Delivery Protocol */
        protocolNames.put(37, "DDP");

        /* Multicast Transport Protocol */
        protocolNames.put(92, "MTP");

        /* Virtual Router Redundancy Protocol */
        protocolNames.put(112, "VRRP");

        /* PGM Reliable Transport Protocol */
        protocolNames.put(113, "PGM");

        /* Layer Two Tunneling Protocol */
        protocolNames.put(115, "L2TP");

        /* Stream Control Transmission Protocol */
        protocolNames.put(132, "SCTP");

        /* Fibre Channel */
        protocolNames.put(133, "FC");

        /* Mobility Header */
        protocolNames.put(135, "Mobility");

        /* UDPLite */
        protocolNames.put(136, "UDPLite");

        /* MPLS-in-IP */
        protocolNames.put(137, "MPLS-in-IP");

        /* MANET Protocols */
        protocolNames.put(138, "MANET");

        /* Host Identity Protocol */
        protocolNames.put(139, "HIP");

        /* Shim6 Protocol */
        protocolNames.put(140, "Shim6");

        /* Robust Header Compression */
        protocolNames.put(142, "ROHC");

        /* Ethernet */
        protocolNames.put(143, "Ethernet");
    }

    /**
     * 添加实验性和保留协议
     */
    private void addExperimentalProtocols() {
        /* 实验性协议 */
        protocolNames.put(253, "Experimental");
        protocolNames.put(254, "Experimental");

        /* 保留 */
        protocolNames.put(255, "Reserved");
    }

    /**
     * 根据协议ID获取协议名称
     *
     * @param protocolId 协议ID
     * @return 协议名称，如果不存在则返回协议ID的字符串表示
     */
    public String getProtocolName(Integer protocolId) {
        if (protocolId == null) {
            return "UNKNOWN";
        }

        // 如果缓存为空，重新加载
        if (protocolNames.isEmpty()) {
            initialize();
        }

        return protocolNames.getOrDefault(protocolId, String.valueOf(protocolId));
    }

    /**
     * 刷新协议名称缓存
     */
    public void refresh() {
        protocolNames.clear();
        initialize();
    }
}
