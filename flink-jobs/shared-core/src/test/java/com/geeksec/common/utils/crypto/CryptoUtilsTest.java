package com.geeksec.common.utils.crypto;

import org.junit.jupiter.api.Test;

import java.nio.charset.StandardCharsets;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;

/**
 * CryptoUtils工具类的单元测试
 * 
 * <AUTHOR>
 */
public class CryptoUtilsTest {

    @Test
    public void testBase64EncodeDecode() {
        // 准备测试数据
        String originalText = "这是一个测试文本";
        byte[] originalBytes = originalText.getBytes(StandardCharsets.UTF_8);
        
        // 测试编码
        String encodedText = CryptoUtils.encodeBase64(originalBytes);
        assertNotNull(encodedText);
        
        // 测试解码
        byte[] decodedBytes = CryptoUtils.decodeBase64(encodedText);
        assertNotNull(decodedBytes);
        
        // 验证解码后的结果与原始数据一致
        assertEquals(originalText, new String(decodedBytes, StandardCharsets.UTF_8));
    }
    
    @Test
    public void testBase64WithNullInput() {
        // 测试空输入
        assertNull(CryptoUtils.encodeBase64(null));
        assertNull(CryptoUtils.decodeBase64(null));
        assertNull(CryptoUtils.decodeBase64(""));
    }
    
    @Test
    public void testHashFunctions() {
        String input = "测试哈希函数";
        
        // 测试MD5
        String md5Hash = CryptoUtils.md5(input);
        assertNotNull(md5Hash);
        assertEquals(32, md5Hash.length());
        
        // 测试SHA-256
        String sha256Hash = CryptoUtils.sha256(input);
        assertNotNull(sha256Hash);
        assertEquals(64, sha256Hash.length());
        
        // 测试SHA-512
        String sha512Hash = CryptoUtils.sha512(input);
        assertNotNull(sha512Hash);
        assertEquals(128, sha512Hash.length());
        
        // 测试空输入
        assertNull(CryptoUtils.md5(null));
        assertNull(CryptoUtils.sha256(null));
        assertNull(CryptoUtils.sha512(null));
    }
    
    @Test
    public void testAESEncryptionDecryption() throws NoSuchAlgorithmException {
        // 准备测试数据
        String plainText = "这是需要加密的敏感数据";
        byte[] key = CryptoUtils.generateAESKey(128);
        byte[] iv = CryptoUtils.generateIV(16);
        
        // 测试加密
        String encryptedText = CryptoUtils.encryptAES(plainText, key, iv);
        assertNotNull(encryptedText);
        
        // 测试解密
        String decryptedText = CryptoUtils.decryptAES(encryptedText, key, iv);
        assertNotNull(decryptedText);
        
        // 验证解密后的结果与原始数据一致
        assertEquals(plainText, decryptedText);
        
        // 测试使用错误的密钥解密
        byte[] wrongKey = CryptoUtils.generateAESKey(128);
        String wrongDecryption = CryptoUtils.decryptAES(encryptedText, wrongKey, iv);
        assertNull(wrongDecryption);
    }
    
    @Test
    public void testHexConversion() {
        // 准备测试数据
        byte[] originalBytes = {0x01, 0x23, 0x45, 0x67, (byte) 0x89, (byte) 0xAB, (byte) 0xCD, (byte) 0xEF};
        String expectedHex = "0123456789abcdef";
        
        // 测试字节数组转十六进制
        String hexString = CryptoUtils.bytesToHex(originalBytes);
        assertEquals(expectedHex, hexString.toLowerCase());
        
        // 测试十六进制转字节数组
        byte[] convertedBytes = CryptoUtils.hexToBytes(hexString);
        assertTrue(Arrays.equals(originalBytes, convertedBytes));
    }
    
    @Test
    public void testKeyAndIVGeneration() throws NoSuchAlgorithmException {
        // 测试AES密钥生成
        byte[] key128 = CryptoUtils.generateAESKey(128);
        assertEquals(16, key128.length);
        
        byte[] key256 = CryptoUtils.generateAESKey(256);
        assertEquals(32, key256.length);
        
        // 测试IV生成
        byte[] iv = CryptoUtils.generateIV(16);
        assertEquals(16, iv.length);
    }
}
