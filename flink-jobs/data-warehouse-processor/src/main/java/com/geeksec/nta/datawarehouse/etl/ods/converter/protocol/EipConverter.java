package com.geeksec.nta.datawarehouse.etl.ods.converter.protocol;

import com.geeksec.common.utils.time.DateTimeUtils;
import com.geeksec.nta.datawarehouse.etl.constant.FieldConstants;
import com.geeksec.nta.datawarehouse.etl.ods.converter.common.AbstractProtobufMessageConverter;
import com.geeksec.nta.datawarehouse.etl.ods.tag.MessageOutputTag;
import com.geeksec.proto.ZMPNMsg;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

/**
 * EIP协议转换器
 * 将EIP协议的protobuf消息转换为Doris ods_eip_protocol_metadata表格式
 *
 * <AUTHOR>
 */
public class EipConverter extends AbstractProtobufMessageConverter {

    @Override
    protected Row convertMessage(ZMPNMsg.JKNmsg msg) {
        Row row = Row.withNames();

        // 设置通用字段
        ZMPNMsg.Comm_msg commMsg = getCommMsg(msg);
        if (commMsg != null) {
            row.setField(FieldConstants.FIELD_SESSION_ID, commMsg.getSessionId());
            row.setField(FieldConstants.FIELD_SRC_IP, commMsg.getSrcIp());
            row.setField(FieldConstants.FIELD_DST_IP, commMsg.getDstIp());
            row.setField(FieldConstants.FIELD_SRC_PORT, commMsg.getSrcPort());
            row.setField(FieldConstants.FIELD_DST_PORT, commMsg.getDstPort());
            row.setField(FieldConstants.FIELD_IP_PROTOCOL, commMsg.getIppro());
            row.setField(FieldConstants.FIELD_BEGIN_TIME, DateTimeUtils.fromTimestampSeconds(commMsg.getBeginTime()));
            row.setField(FieldConstants.FIELD_SERVER_IP, commMsg.getServerIp());
            row.setField(FieldConstants.FIELD_APP_ID, commMsg.getAppId());
            row.setField(FieldConstants.FIELD_APP_NAME, commMsg.getAppName());
            row.setField(FieldConstants.FIELD_THREAD_ID, commMsg.getThreadId());
            row.setField(FieldConstants.FIELD_TASK_ID, commMsg.getTaskId());
            row.setField(FieldConstants.FIELD_BEGIN_NSEC, commMsg.getBeginNsec());
            row.setField(FieldConstants.FIELD_BATCH_ID, commMsg.getBatchId());
        }

        // 设置EIP特定字段（需要根据实际protobuf定义实现）
        // TODO: 根据实际的EIP protobuf消息结构实现字段映射

        return row;
    }

    private ZMPNMsg.Comm_msg getCommMsg(ZMPNMsg.JKNmsg msg) {
        // TODO: 根据实际的protobuf结构实现
        return null;
    }

    @Override
    public OutputTag<Row> getOutputTag() {
        return MessageOutputTag.EIP_STREAM;
    }
}
