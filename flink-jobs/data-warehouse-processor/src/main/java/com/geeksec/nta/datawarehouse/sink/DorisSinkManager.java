package com.geeksec.nta.datawarehouse.sink;

import com.geeksec.nta.datawarehouse.common.MessageType;
import com.geeksec.nta.datawarehouse.etl.ods.tag.MessageOutputTag;
import org.apache.doris.flink.cfg.DorisExecutionOptions;
import org.apache.doris.flink.cfg.DorisOptions;
import org.apache.doris.flink.sink.DorisSink;
import org.apache.doris.flink.sink.writer.LoadConstants;
import org.apache.doris.flink.sink.writer.serializer.RowSerializer;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

import java.util.Properties;
import java.util.UUID;

import static com.geeksec.common.config.ConfigConstants.*;

/**
 * Manages the configuration and creation of <PERSON>.
 *
 * <AUTHOR>
 */
public final class DorisSinkManager {

        private DorisSinkManager() {
                // Utility class
        }

        /**
         * Configures and creates all Doris Sinks for various protocol data streams.
         *
         * @param sideOutStream The side output stream containing different protocol
         *                      data.
         * @param config        The application configuration.
         */
        public static void configureDorisSinks(
                        SingleOutputStreamOperator<Row> sideOutStream,
                        ParameterTool config) {

                int sinkParallelism = config.getInt(DORIS_SINK_PARALLELISM, config.getInt(PARALLELISM_DORIS_SINK, 4));

                // 配置所有协议的sink
                configureSink(sideOutStream, MessageOutputTag.SESSION_STREAM, "ods_single_session_logs",
                                "Session Logs", sinkParallelism, config);
                configureSink(sideOutStream, MessageOutputTag.HTTP_STREAM, "ods_http_metadata", "HTTP Metadata",
                                sinkParallelism, config);
                configureSink(sideOutStream, MessageOutputTag.DNS_STREAM, "ods_dns_metadata", "DNS Metadata",
                                sinkParallelism, config);
                configureSink(sideOutStream, MessageOutputTag.SSL_STREAM, "ods_ssl_metadata", "SSL Metadata",
                                sinkParallelism, config);
                configureSink(sideOutStream, MessageOutputTag.SSH_STREAM, "ods_ssh_metadata", "SSH Metadata",
                                sinkParallelism, config);
                configureSink(sideOutStream, MessageOutputTag.RLOGIN_STREAM, "ods_rlogin_metadata", "RLOGIN Metadata",
                                sinkParallelism, config);
                configureSink(sideOutStream, MessageOutputTag.TELNET_STREAM, "ods_telnet_metadata", "TELNET Metadata",
                                sinkParallelism, config);
                configureSink(sideOutStream, MessageOutputTag.RDP_STREAM, "ods_rdp_metadata", "RDP Metadata",
                                sinkParallelism, config);
                configureSink(sideOutStream, MessageOutputTag.VNC_STREAM, "ods_vnc_metadata", "VNC Metadata",
                                sinkParallelism, config);
                configureSink(sideOutStream, MessageOutputTag.XDMCP_STREAM, "ods_xdmcp_metadata", "XDMCP Metadata",
                                sinkParallelism, config);
                configureSink(sideOutStream, MessageOutputTag.NTP_STREAM, "ods_ntp_metadata", "NTP Metadata",
                                sinkParallelism, config);
                configureSink(sideOutStream, MessageOutputTag.ICMP_STREAM, "ods_icmp_metadata", "ICMP Metadata",
                                sinkParallelism, config);
        }

        /**
         * 配置Doris Sink
         *
         * @param sideOutStream 侧输出流
         * @param outputTag     输出标签
         * @param tableName     Doris表名
         * @param sinkName      Sink名称
         * @param parallelism   并行度
         * @param config        配置参数
         */
        private static <T> void configureSink(
                        SingleOutputStreamOperator<Row> sideOutStream,
                        OutputTag<Row> outputTag,
                        String tableName,
                        String sinkName,
                        int parallelism,
                        ParameterTool config) {
                DataStream<Row> stream = sideOutStream.getSideOutput(outputTag);
                stream.sinkTo(buildDorisSink(tableName, config))
                                .name(sinkName + " Doris Sink")
                                .setParallelism(parallelism);
        }

        /**
         * Configures and creates a Doris Sink for a specific data stream.
         *
         * @param stream     The data stream to sink to Doris.
         * @param config     The application configuration.
         * @param msgType    The type of the message.
         */
        public static void configureDorisSink(DataStream<Row> stream, ParameterTool config, MessageType msgType) {
                String tableName;

                if (MessageType.SESSION == msgType) {
                        tableName = "ods_single_session_logs";
                } else {
                        tableName = "ods_" + msgType.name().toLowerCase() + "_protocol_metadata";
                }

                int sinkParallelism = config.getInt(DORIS_SINK_PARALLELISM, config.getInt(PARALLELISM_DORIS_SINK, 4));

                stream.sinkTo(buildDorisSink(tableName, config))
                      .name(msgType.name() + " Data Doris Sink")
                      .setParallelism(sinkParallelism);
        }



        /**
         * Builds a DorisSink for the given table name using Row type.
         *
         * @param tableName The name of the Doris table.
         * @param config    The application configuration.
         * @return Configured DorisSink for Row.
         */
        public static DorisSink<Row> buildDorisSink(String tableName, ParameterTool config) {
                String dorisFeNodes = config.get(DORIS_FE_NODES);
                String dorisUser = config.get(DORIS_USERNAME);
                String dorisPassword = config.get(DORIS_PASSWORD);
                String databaseName = config.get(DORIS_DATABASE);

                DorisOptions dorisOptions = DorisOptions.builder()
                                .setFenodes(dorisFeNodes)
                                .setTableIdentifier(databaseName + "." + tableName)
                                .setUsername(dorisUser)
                                .setPassword(dorisPassword)
                                .build();

                // 设置 Stream Load 相关属性
                Properties streamLoadProp = new Properties();
                streamLoadProp.setProperty("format", "json");
                streamLoadProp.setProperty("read_json_by_line", "true");
                streamLoadProp.setProperty("line_delimiter", "\n");

                return DorisSink.<Row>builder()
                                .setDorisOptions(dorisOptions)
                                .setDorisExecutionOptions(DorisExecutionOptions.builder()
                                                .setStreamLoadProp(streamLoadProp)
                                                .setLabelPrefix("doris-label-" + tableName + "-" + UUID.randomUUID())
                                                .build())
                                .setSerializer(RowSerializer.builder().setType(LoadConstants.JSON)
                                                .build())
                                .build();
        }


}
