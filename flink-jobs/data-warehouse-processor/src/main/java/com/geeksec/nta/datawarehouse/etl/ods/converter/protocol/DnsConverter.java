package com.geeksec.nta.datawarehouse.etl.ods.converter.protocol;

import com.geeksec.common.utils.time.DateTimeUtils;
import com.geeksec.nta.datawarehouse.etl.constant.FieldConstants;
import com.geeksec.nta.datawarehouse.etl.ods.converter.common.AbstractProtobufMessageConverter;
import com.geeksec.nta.datawarehouse.etl.ods.tag.MessageOutputTag;
import com.geeksec.proto.ZMPNMsg;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

/**
 * Converter for DNS protocol messages.
 * Maps DNS protocol data from protobuf messages to Doris
 * ods_dns_protocol_metadata table format.
 * This converter is aligned with the latest ods_dns_protocol_metadata table
 * schema.
 *
 * <AUTHOR> Team
 */
public class DnsConverter extends AbstractProtobufMessageConverter {

    @Override
    protected Row convertMessage(ZMPNMsg.JKNmsg msg) {
        ZMPNMsg.dns_msg dns = msg.getDns();
        ZMPNMsg.Comm_msg commMsg = dns.getCommMsg();

        // 创建带有命名字段的Row
        Row row = Row.withNames();

        // 设置通用字段
        row.setField(FieldConstants.FIELD_SESSION_ID, commMsg.getSessionId());
        row.setField(FieldConstants.FIELD_SRC_IP, commMsg.getSrcIp());
        row.setField(FieldConstants.FIELD_DST_IP, commMsg.getDstIp());
        row.setField(FieldConstants.FIELD_SRC_PORT, commMsg.getSrcPort());
        row.setField(FieldConstants.FIELD_DST_PORT, commMsg.getDstPort());
        row.setField(FieldConstants.FIELD_IP_PROTOCOL, commMsg.getIppro());
        row.setField(FieldConstants.FIELD_BEGIN_TIME, DateTimeUtils.fromTimestampSeconds(commMsg.getBeginTime()));
        row.setField(FieldConstants.FIELD_SERVER_IP, commMsg.getServerIp());
        row.setField(FieldConstants.FIELD_APP_ID, commMsg.getAppId());
        row.setField(FieldConstants.FIELD_APP_NAME, commMsg.getAppName());
        row.setField(FieldConstants.FIELD_THREAD_ID, commMsg.getThreadId());
        row.setField(FieldConstants.FIELD_TASK_ID, commMsg.getTaskId());
        row.setField(FieldConstants.FIELD_BEGIN_NSEC, commMsg.getBeginNsec());
        row.setField(FieldConstants.FIELD_BATCH_ID, commMsg.getBatchId());

        // 设置DNS特定字段
        row.setField(FieldConstants.FIELD_DNS_ID, dns.getDnsId());
        row.setField(FieldConstants.FIELD_DNS_FLAGS, dns.getDnsFlags());
        row.setField(FieldConstants.FIELD_DNS_QUE, dns.getDnsQue());
        row.setField(FieldConstants.FIELD_DNS_ANS, dns.getDnsAns());
        row.setField(FieldConstants.FIELD_DNS_AUTH, dns.getDnsAuth());
        row.setField(FieldConstants.FIELD_DNS_ADD, dns.getDnsAdd());
        row.setField(FieldConstants.FIELD_DNS_DOMAIN, dns.getDnsDomain());
        row.setField(FieldConstants.FIELD_DNS_DOMAIN_IP, dns.getDnsDomainIp());
        row.setField(FieldConstants.FIELD_DNS_QUERY, dns.getDnsQuery());
        row.setField(FieldConstants.FIELD_DNS_ANSWER, dns.getDnsAnswer());

        return row;
    }

    @Override
    public OutputTag<Row> getOutputTag() {
        return MessageOutputTag.DNS_STREAM;
    }
}
