package com.geeksec.nta.datawarehouse.etl.ods.converter.common;

import com.geeksec.common.utils.crypto.CryptoUtils;
import com.geeksec.proto.ZMPNMsg;
import com.google.protobuf.ByteString;
import com.google.protobuf.ProtocolStringList;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Abstract base class for all message-specific protobuf converters.
 * Provides common functionality and defines the interface for all converters.
 *
 * <AUTHOR> Team
 */
@Slf4j
public abstract class AbstractProtobufMessageConverter {

    /** Direction constant for client to server */
    protected static final int DIRECTION_CLIENT_TO_SERVER = 0;

    /** Direction constant for server to client */
    protected static final int DIRECTION_SERVER_TO_CLIENT = 1;

    /**
     * Convert a protobuf message to a Row representation.
     * This is a template method that calls convertMessage and then adds common
     * metadata.
     *
     * @param msg The protobuf message to convert
     * @return A Row representation of the message with common metadata
     */
    public final Row convert(ZMPNMsg.JKNmsg msg) {
        if (msg == null) {
            log.warn("收到空数据");
            return null;
        }

        try {
            Row row = convertMessage(msg);
            if (row == null) {
                log.debug("跳过空数据映射");
            }
            return row;
        } catch (Exception e) {
            log.error("转换数据时发生错误: {}", e.getMessage(), e);
            throw new RuntimeException("转换数据失败", e);
        }
    }

    /**
     * Convert a protobuf message to a Row representation without metadata.
     * Subclasses should implement this method to perform the actual conversion.
     *
     * @param msg The protobuf message to convert
     * @return A Row representation of the message without metadata
     */
    protected abstract Row convertMessage(ZMPNMsg.JKNmsg msg);

    /**
     * 获取对应的输出标签
     * 子类应该重写此方法，返回对应的输出标签
     *
     * @return 输出标签
     */
    public abstract OutputTag<Row> getOutputTag();

    /**
     * Convert a ProtocolStringList to a Java List, filtering out "None" values.
     *
     * @param protoList The protobuf string list to convert
     * @return A filtered Java List
     */
    protected List<String> convertProtoListToJavaList(ProtocolStringList protoList) {
        List<String> list = protoList.stream()
                .filter(s -> !"None".equals(s))
                .collect(Collectors.toList());
        return list;
    }

    /**
     * Convert ByteString to Base64 string representation using CryptoUtils
     *
     * @param bytes The ByteString to convert
     * @return The Base64 string representation
     */
    protected String bytesToBase64String(ByteString bytes) {
        if (bytes == null || bytes.isEmpty()) {
            return "";
        }

        // Convert ByteString to byte array and then encode using CryptoUtils
        String base64String = CryptoUtils.encodeBase64(bytes.toByteArray());
        return base64String != null ? base64String : "";
    }
}
