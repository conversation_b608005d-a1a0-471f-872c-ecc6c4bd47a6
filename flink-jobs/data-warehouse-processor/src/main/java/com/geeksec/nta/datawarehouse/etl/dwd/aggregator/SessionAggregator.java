package com.geeksec.nta.datawarehouse.etl.dwd.aggregator;

import com.geeksec.nta.datawarehouse.common.MessageType;
import com.geeksec.nta.datawarehouse.etl.dwd.converter.ProtocolJsonConverter;
import com.geeksec.nta.datawarehouse.etl.dwd.model.StreamData;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.types.Row;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 会话聚合器
 * 使用StreamData包装类，基于OutputTag的消息类型识别
 * 负责聚合同一会话的多个协议元数据，并将其转换为JSON数组格式
 * 支持DWD层单表+JSON数组的架构设计
 *
 * <AUTHOR>
 */
@Slf4j
@Data
public class SessionAggregator implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 会话ID */
    private String sessionId;

    /** 会话基础信息 */
    private Row sessionInfo;

    /** 各协议类型的数据列表 */
    private Map<String, List<String>> protocolDataMap;

    /** 协议JSON转换器 */
    private transient ProtocolJsonConverter jsonConverter;

    /**
     * 构造函数
     *
     * @param sessionId 会话ID
     */
    public SessionAggregator(String sessionId) {
        this.sessionId = sessionId;
        this.protocolDataMap = new HashMap<>();
        this.jsonConverter = new ProtocolJsonConverter();
    }

    /**
     * 添加流数据
     * 基于StreamData的消息类型直接识别，无需字段判断
     *
     * @param streamData 流数据包装对象
     */
    public void addStreamData(StreamData streamData) {
        if (streamData == null || streamData.getData() == null) {
            log.warn("收到空的流数据");
            return;
        }

        MessageType messageType = streamData.getMessageType();
        Row data = streamData.getData();

        // 如果是会话数据，保存为会话基础信息
        if (MessageType.SESSION.equals(messageType)) {
            this.sessionInfo = data;
            log.debug("更新会话基础信息: sessionId={}", sessionId);
            return;
        }

        // 转换为JSON格式
        String protocolType = messageType.name();
        String jsonData = getJsonConverter().convertToJson(data, protocolType);
        if (jsonData != null) {
            // 添加到对应协议类型的列表中
            protocolDataMap.computeIfAbsent(protocolType, k -> new ArrayList<>()).add(jsonData);
            log.debug("添加协议数据: sessionId={}, messageType={}, 当前数量={}",
                    sessionId, messageType, protocolDataMap.get(protocolType).size());
        }
    }



    /**
     * 获取协议数据总数
     *
     * @return 协议数据总数
     */
    public int getProtocolCount() {
        return protocolDataMap.values().stream()
                .mapToInt(List::size)
                .sum();
    }

    /**
     * 获取指定协议类型的JSON数组
     *
     * @param protocolType 协议类型
     * @return JSON数组字符串列表
     */
    public List<String> getProtocolJsonArray(String protocolType) {
        return protocolDataMap.getOrDefault(protocolType, new ArrayList<>());
    }

    /**
     * 获取所有协议类型
     *
     * @return 协议类型集合
     */
    public java.util.Set<String> getProtocolTypes() {
        return protocolDataMap.keySet();
    }

    /**
     * 检查是否有会话基础信息
     *
     * @return 是否有会话基础信息
     */
    public boolean hasSessionInfo() {
        return sessionInfo != null;
    }

    /**
     * 获取JSON转换器，延迟初始化
     *
     * @return JSON转换器
     */
    private ProtocolJsonConverter getJsonConverter() {
        if (jsonConverter == null) {
            jsonConverter = new ProtocolJsonConverter();
        }
        return jsonConverter;
    }
}
