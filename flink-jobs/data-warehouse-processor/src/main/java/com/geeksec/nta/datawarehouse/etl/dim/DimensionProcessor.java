package com.geeksec.nta.datawarehouse.etl.dim;

import com.geeksec.nta.datawarehouse.etl.dim.function.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.doris.flink.sink.DorisSink;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

/**
 * Processor for handling dimension data extraction and writing to <PERSON>.
 * This class coordinates the extraction of dimension data from protocol metadata
 * and routes it to the appropriate Doris tables.
 *
 * <AUTHOR>
 */
@Slf4j
public class DimensionProcessor {

    /**
     * IPv4维度侧输出标签
     */
    public static final OutputTag<Row> IPV4_DIM_TAG =
            new OutputTag<Row>("IPv4Dimension") {};

    /**
     * IPv6维度侧输出标签
     */
    public static final OutputTag<Row> IPV6_DIM_TAG =
            new OutputTag<Row>("IPv6Dimension") {};

    /**
     * MAC地址维度侧输出标签
     */
    public static final OutputTag<Row> MAC_DIM_TAG =
            new OutputTag<Row>("MacDimension") {};

    /**
     * 域名维度侧输出标签
     */
    public static final OutputTag<Row> DOMAIN_DIM_TAG =
            new OutputTag<Row>("DomainDimension") {};

    /**
     * URL维度侧输出标签
     */
    public static final OutputTag<Row> URL_DIM_TAG =
            new OutputTag<Row>("UrlDimension") {};

    /**
     * 应用服务维度侧输出标签
     */
    public static final OutputTag<Row> APP_SERVICE_DIM_TAG =
            new OutputTag<Row>("AppServiceDimension") {};

    /**
     * 应用维度侧输出标签
     */
    public static final OutputTag<Row> APP_DIM_TAG =
            new OutputTag<Row>("AppDimension") {};

    /**
     * 操作系统维度侧输出标签
     */
    public static final OutputTag<Row> OS_DIM_TAG =
            new OutputTag<Row>("OSDimension") {};

    /**
     * 设备维度侧输出标签
     */
    public static final OutputTag<Row> DEVICE_DIM_TAG =
            new OutputTag<Row>("DeviceDimension") {};

    /**
     * Process DNS metadata to extract dimension data and write to Doris
     *
     * @param dnsStream The DNS metadata stream
     * @param domainDimensionSink Sink for domain dimension data
     * @param ipv4DimensionSink Sink for IPv4 dimension data
     * @param ipv6DimensionSink Sink for IPv6 dimension data
     * @param parallelism The parallelism for sink operations
     * @return The processed DNS stream with dimension data extracted
     */
    public static SingleOutputStreamOperator<Row> processDnsDimensions(
            DataStream<Row> dnsStream,
            DorisSink<Row> domainDimensionSink,
            DorisSink<Row> ipv4DimensionSink,
            DorisSink<Row> ipv6DimensionSink,
            int parallelism) {

        log.info("设置DNS维度数据提取");

        // 应用域名维度表处理函数
        SingleOutputStreamOperator<Row> domainProcessedStream = dnsStream
                .process(new DomainDimensionTableFunction())
                .name("DNS域名维度表处理");

        // 应用IP维度表处理函数
        SingleOutputStreamOperator<Row> processedStream = domainProcessedStream
                .process(new IpDimensionTableFunction("domain_ip"))
                .name("DNS IP维度表处理");

        // 输出域名维度数据到Doris
        domainProcessedStream
                .getSideOutput(DomainDimensionTableFunction.DOMAIN_DIM_TAG)
                .sinkTo(domainDimensionSink)
                .name("域名维度Doris Sink")
                .setParallelism(parallelism);

        // 输出IPv4维度数据到Doris
        processedStream
                .getSideOutput(IpDimensionTableFunction.IPV4_DIM_TAG)
                .sinkTo(ipv4DimensionSink)
                .name("IPv4维度Doris Sink")
                .setParallelism(parallelism);

        // 输出IPv6维度数据到Doris
        processedStream
                .getSideOutput(IpDimensionTableFunction.IPV6_DIM_TAG)
                .sinkTo(ipv6DimensionSink)
                .name("IPv6维度Doris Sink")
                .setParallelism(parallelism);

        return processedStream;
    }

    /**
     * Process HTTP metadata to extract dimension data and write to Doris
     *
     * @param httpStream The HTTP metadata stream
     * @param domainDimensionSink Sink for domain dimension data
     * @param urlDimensionSink Sink for URL dimension data
     * @param parallelism The parallelism for sink operations
     * @return The processed HTTP stream with dimension data extracted
     */
    public static SingleOutputStreamOperator<Row> processHttpDimensions(
            DataStream<Row> httpStream,
            DorisSink<Row> domainDimensionSink,
            DorisSink<Row> urlDimensionSink,
            int parallelism) {

        log.info("设置HTTP维度数据提取");

        // 应用域名维度表处理函数
        SingleOutputStreamOperator<Row> processedStream = httpStream
                .process(new DomainDimensionTableFunction())
                .name("HTTP域名维度表处理");

        // 输出域名维度数据到Doris
        processedStream
                .getSideOutput(DomainDimensionTableFunction.DOMAIN_DIM_TAG)
                .sinkTo(domainDimensionSink)
                .name("HTTP域名维度Doris Sink")
                .setParallelism(parallelism);

        // 应用URL维度表处理函数
        SingleOutputStreamOperator<Row> urlProcessedStream = processedStream
                .process(new UrlDimensionTableFunction("url"))
                .name("HTTP URL维度表处理");

        // 输出URL维度数据到Doris
        urlProcessedStream
                .getSideOutput(UrlDimensionTableFunction.URL_DIM_TAG)
                .sinkTo(urlDimensionSink)
                .name("URL维度Doris Sink")
                .setParallelism(parallelism);

        return urlProcessedStream;
    }

    /**
     * Process session metadata to extract dimension data and write to Doris
     *
     * @param sessionStream The session metadata stream
     * @param ipv4DimensionSink Sink for IPv4 dimension data
     * @param ipv6DimensionSink Sink for IPv6 dimension data
     * @param macDimensionSink Sink for MAC address dimension data
     * @param appServiceDimensionSink Sink for application service dimension data
     * @param appDimensionSink Sink for application dimension data
     * @param osDimensionSink Sink for OS dimension data
     * @param deviceDimensionSink Sink for device dimension data
     * @param parallelism The parallelism for sink operations
     * @return The processed session stream with dimension data extracted
     */
    public static SingleOutputStreamOperator<Row> processSessionDimensions(
            DataStream<Row> sessionStream,
            DorisSink<Row> ipv4DimensionSink,
            DorisSink<Row> ipv6DimensionSink,
            DorisSink<Row> macDimensionSink,
            DorisSink<Row> appServiceDimensionSink,
            DorisSink<Row> appDimensionSink,
            DorisSink<Row> osDimensionSink,
            DorisSink<Row> deviceDimensionSink,
            int parallelism) {

        log.info("设置Session维度数据提取");

        // 处理源IP维度
        SingleOutputStreamOperator<Row> srcIpProcessedStream = sessionStream
                .process(new IpDimensionTableFunction("src_ip"))
                .name("Session源IP维度表处理");

        // 处理目标IP维度
        SingleOutputStreamOperator<Row> ipProcessedStream = srcIpProcessedStream
                .process(new IpDimensionTableFunction("dst_ip"))
                .name("Session目标IP维度表处理");

        // 处理MAC维度
        SingleOutputStreamOperator<Row> macProcessedStream = ipProcessedStream
                .process(new MacDimensionTableFunction("smac"))
                .name("Session源MAC维度表处理");

        SingleOutputStreamOperator<Row> macDstProcessedStream = macProcessedStream
                .process(new MacDimensionTableFunction("dmac"))
                .name("Session目标MAC维度表处理");

        // 处理应用服务维度
        SingleOutputStreamOperator<Row> appServiceProcessedStream = macDstProcessedStream
                .process(new AppServiceDimensionTableFunction("service_name"))
                .name("Session应用服务维度表处理");

        // 处理应用维度
        SingleOutputStreamOperator<Row> appProcessedStream = appServiceProcessedStream
                .process(new AppDimensionTableFunction("app_name"))
                .name("Session应用维度表处理");

        // 处理客户端操作系统维度
        SingleOutputStreamOperator<Row> clientOsProcessedStream = appProcessedStream
                .process(new OsDimensionTableFunction("client_os_name"))
                .name("Session客户端OS维度表处理");

        // 处理服务端操作系统维度
        SingleOutputStreamOperator<Row> osProcessedStream = clientOsProcessedStream
                .process(new OsDimensionTableFunction("server_os_name"))
                .name("Session服务端OS维度表处理");

        // 处理客户端设备维度
        SingleOutputStreamOperator<Row> clientDeviceProcessedStream = osProcessedStream
                .process(new DeviceDimensionTableFunction("client_device_type"))
                .name("Session客户端设备维度表处理");

        // 处理服务端设备维度
        SingleOutputStreamOperator<Row> processedStream = clientDeviceProcessedStream
                .process(new DeviceDimensionTableFunction("server_device_type"))
                .name("Session服务端设备维度表处理");

        // 输出IPv4维度数据到Doris
        DataStream<Row> ipv4Stream = srcIpProcessedStream
                .getSideOutput(IpDimensionTableFunction.IPV4_DIM_TAG)
                .union(ipProcessedStream.getSideOutput(IpDimensionTableFunction.IPV4_DIM_TAG));

        ipv4Stream
                .sinkTo(ipv4DimensionSink)
                .name("Session IPv4维度Doris Sink")
                .setParallelism(parallelism);

        // 输出IPv6维度数据到Doris
        DataStream<Row> ipv6Stream = srcIpProcessedStream
                .getSideOutput(IpDimensionTableFunction.IPV6_DIM_TAG)
                .union(ipProcessedStream.getSideOutput(IpDimensionTableFunction.IPV6_DIM_TAG));

        ipv6Stream
                .sinkTo(ipv6DimensionSink)
                .name("Session IPv6维度Doris Sink")
                .setParallelism(parallelism);

        // 输出MAC维度数据到Doris
        DataStream<Row> macStream = macProcessedStream
                .getSideOutput(MacDimensionTableFunction.MAC_DIM_TAG)
                .union(macDstProcessedStream.getSideOutput(MacDimensionTableFunction.MAC_DIM_TAG));

        macStream
                .sinkTo(macDimensionSink)
                .name("MAC维度Doris Sink")
                .setParallelism(parallelism);

        // 输出应用服务维度数据到Doris
        appServiceProcessedStream
                .getSideOutput(AppServiceDimensionTableFunction.APP_SERVICE_DIM_TAG)
                .sinkTo(appServiceDimensionSink)
                .name("应用服务维度Doris Sink")
                .setParallelism(parallelism);

        // 输出应用维度数据到Doris
        appProcessedStream
                .getSideOutput(AppDimensionTableFunction.APP_DIM_TAG)
                .sinkTo(appDimensionSink)
                .name("应用维度Doris Sink")
                .setParallelism(parallelism);

        // 输出操作系统维度数据到Doris
        DataStream<Row> osStream = clientOsProcessedStream
                .getSideOutput(OsDimensionTableFunction.OS_DIM_TAG)
                .union(osProcessedStream.getSideOutput(OsDimensionTableFunction.OS_DIM_TAG));

        osStream
                .sinkTo(osDimensionSink)
                .name("操作系统维度Doris Sink")
                .setParallelism(parallelism);

        // 输出设备维度数据到Doris
        DataStream<Row> deviceStream = clientDeviceProcessedStream
                .getSideOutput(DeviceDimensionTableFunction.DEVICE_DIM_TAG)
                .union(processedStream.getSideOutput(DeviceDimensionTableFunction.DEVICE_DIM_TAG));

        deviceStream
                .sinkTo(deviceDimensionSink)
                .name("设备维度Doris Sink")
                .setParallelism(parallelism);

        return processedStream;
    }
}
