package com.geeksec.nta.datawarehouse.etl.ods.converter.protocol;

import com.geeksec.common.utils.time.DateTimeUtils;
import com.geeksec.nta.datawarehouse.etl.constant.FieldConstants;
import com.geeksec.nta.datawarehouse.etl.ods.converter.common.AbstractProtobufMessageConverter;
import com.geeksec.nta.datawarehouse.etl.ods.tag.MessageOutputTag;
import com.geeksec.proto.ZMPNMsg;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

/**
 * Converter for Rlogin protocol messages.
 * Maps Rlogin protocol data from protobuf messages to Doris
 * ods_rlogin_protocol_metadata table format.
 * This converter is aligned with the latest ods_rlogin_protocol_metadata table
 * schema.
 *
 * <AUTHOR> Team
 */
public class RloginConverter extends AbstractProtobufMessageConverter {

    @Override
    protected Row convertMessage(ZMPNMsg.JKNmsg msg) {
        ZMPNMsg.rlogin_msg rlogin = msg.getRlogin();
        ZMPNMsg.Comm_msg commMsg = rlogin.getCommMsg();
        
        Row row = Row.withNames();

        // Common fields from Comm_msg
        row.setField(FieldConstants.FIELD_SESSION_ID, commMsg.getSessionId());
        row.setField(FieldConstants.FIELD_SRC_IP, commMsg.getSrcIp());
        row.setField(FieldConstants.FIELD_DST_IP, commMsg.getDstIp());
        row.setField(FieldConstants.FIELD_SRC_PORT, commMsg.getSrcPort());
        row.setField(FieldConstants.FIELD_DST_PORT, commMsg.getDstPort());
        row.setField(FieldConstants.FIELD_IP_PROTOCOL, commMsg.getIppro());
        row.setField(FieldConstants.FIELD_BEGIN_TIME, DateTimeUtils.fromTimestampSeconds(commMsg.getBeginTime()));
        row.setField(FieldConstants.FIELD_SERVER_IP, commMsg.getServerIp());
        row.setField(FieldConstants.FIELD_APP_ID, commMsg.getAppId());
        row.setField(FieldConstants.FIELD_APP_NAME, commMsg.getAppName());
        row.setField(FieldConstants.FIELD_THREAD_ID, commMsg.getThreadId());
        row.setField(FieldConstants.FIELD_TASK_ID, commMsg.getTaskId());
        row.setField(FieldConstants.FIELD_BEGIN_NSEC, commMsg.getBeginNsec());
        row.setField(FieldConstants.FIELD_BATCH_ID, commMsg.getBatchId());

        // Rlogin window information
        if (rlogin.hasWininfo()) {
            ZMPNMsg.rlogin_wininfo_msg wininfo = rlogin.getWininfo();
            row.setField(FieldConstants.FIELD_MAGIC_COOKIE, wininfo.getMagicCookie());
            row.setField(FieldConstants.FIELD_WINSIZE_MARKER, wininfo.getWinsizeMarker());
            row.setField(FieldConstants.FIELD_ROWS, wininfo.getRows());
            row.setField(FieldConstants.FIELD_COLUMNS, wininfo.getColumns());
            row.setField(FieldConstants.FIELD_X_PIXELS, wininfo.getXPixels());
            row.setField(FieldConstants.FIELD_Y_PIXELS, wininfo.getYPixels());
            row.setField(FieldConstants.FIELD_TERM_TYPE, wininfo.getTermType());
            row.setField(FieldConstants.FIELD_TERM_SPEED, wininfo.getTermSpeed());
        }

        // Rlogin user information
        if (rlogin.hasUserinfo()) {
            ZMPNMsg.rlogin_userinfo_msg userinfo = rlogin.getUserinfo();
            row.setField(FieldConstants.FIELD_CLIENT_USERNAME, userinfo.getClientUsername());
            row.setField(FieldConstants.FIELD_SERVER_USERNAME, userinfo.getServerUsername());
            row.setField(FieldConstants.FIELD_PASSWD, userinfo.getPasswd());
        }

        return row;
    }

    @Override
    public OutputTag<Row> getOutputTag() {
        return MessageOutputTag.RLOGIN_STREAM;
    }
}
