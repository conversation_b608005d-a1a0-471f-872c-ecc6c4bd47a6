package com.geeksec.nta.datawarehouse.etl.dim.function;

import com.geeksec.common.utils.time.DateTimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.state.StateTtlConfig;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * 操作系统维度表处理函数，用于生成符合dim_os表结构的维度数据
 *
 * 使用Flink Backend State进行缓存管理，支持TTL自动清理
 *
 * <AUTHOR>
 */
@Slf4j
public class OsDimensionTableFunction extends ProcessFunction<Row, Row> {

    private static final long serialVersionUID = 1L;

    /** 客户端标识常量 */
    private static final String CLIENT_IDENTIFIER = "client";

    /** 服务端标识常量 */
    private static final String SERVER_IDENTIFIER = "server";

    /**
     * 操作系统维度数据输出标签
     */
    public static final OutputTag<Row> OS_DIM_TAG = new OutputTag<Row>("os-dimension") {};

    private final String osNameFieldName;
    private final Duration ttl;

    /**
     * 缓存操作系统维度数据的状态
     */
    private transient ValueState<Map<String, Object>> osDimensionState;

    /**
     * 构造函数
     *
     * @param osNameFieldName 操作系统名称字段名
     */
    public OsDimensionTableFunction(String osNameFieldName) {
        // 默认24小时TTL
        this(osNameFieldName, Duration.ofHours(24));
    }

    /**
     * 构造函数
     *
     * @param osNameFieldName 操作系统名称字段名
     * @param ttl 状态TTL时间
     */
    public OsDimensionTableFunction(String osNameFieldName, Duration ttl) {
        this.osNameFieldName = osNameFieldName;
        this.ttl = ttl;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        // 配置状态TTL
        StateTtlConfig ttlConfig = StateTtlConfig
                .newBuilder(ttl)
                .setUpdateType(StateTtlConfig.UpdateType.OnCreateAndWrite)
                .setStateVisibility(StateTtlConfig.StateVisibility.NeverReturnExpired)
                .build();

        // 初始化操作系统维度状态
        ValueStateDescriptor<Map<String, Object>> osStateDescriptor =
                new ValueStateDescriptor<>("os-dimension-state", Types.MAP(Types.STRING, Types.GENERIC(Object.class)));
        osStateDescriptor.enableTimeToLive(ttlConfig);
        osDimensionState = getRuntimeContext().getState(osStateDescriptor);
    }

    @Override
    public void processElement(Row value, Context ctx, Collector<Row> out) throws Exception {
        try {
            String osName = getStringFieldValue(value, osNameFieldName);
            if (osName == null || osName.trim().isEmpty()) {
                out.collect(value);
                return;
            }

            // 检查状态中是否已存在该操作系统的维度数据
            Map<String, Object> existingOsInfo = osDimensionState.value();
            if (existingOsInfo == null) {
                // 首次遇到该操作系统，创建维度数据
                Map<String, Object> osInfo = createOsDimensionInfo(value, osName);
                osDimensionState.update(osInfo);

                // 创建维度表记录并输出到侧输出流
                Row osDimensionRow = createDimensionRow(osName, osInfo);
                ctx.output(OS_DIM_TAG, osDimensionRow);

                log.debug("创建新的操作系统维度数据: {}", osName);
            }

            // 继续传递原始数据
            out.collect(value);

        } catch (Exception e) {
            log.error("处理操作系统维度数据时发生错误: {}", e.getMessage(), e);
            out.collect(value);
        }
    }

    /**
     * 创建操作系统维度信息
     *
     * @param value 原始数据Row
     * @param osName 操作系统名称
     * @return 操作系统维度信息Map
     */
    private Map<String, Object> createOsDimensionInfo(Row value, String osName) {
        Map<String, Object> osInfo = new HashMap<>(8);

        // 基础信息
        osInfo.put("os_name", osName);

        // 获取操作系统平台信息
        String osPlatform = null;

        // 尝试从不同字段获取平台信息
        if (osNameFieldName.contains(CLIENT_IDENTIFIER)) {
            osPlatform = getStringFieldValue(value, "client_os_platform");
        } else if (osNameFieldName.contains(SERVER_IDENTIFIER)) {
            osPlatform = getStringFieldValue(value, "server_os_platform");
        } else {
            // 通用字段名
            osPlatform = getStringFieldValue(value, "os_platform");
        }

        osInfo.put("os_platform", osPlatform != null ? osPlatform : "Unknown");

        return osInfo;
    }

    /**
     * 创建符合维度表结构的操作系统维度记录
     *
     * @param osName 操作系统名称
     * @param osInfo 操作系统信息
     * @return 符合dim_os表结构的Row
     */
    private Row createDimensionRow(String osName, Map<String, Object> osInfo) {
        Row dimensionRow = Row.withNames();

        // 设置主键字段
        dimensionRow.setField("os_name", osName);

        // 设置操作系统平台信息字段
        dimensionRow.setField("os_platform", osInfo.get("os_platform"));

        // 设置聚合字段初始值
        dimensionRow.setField("total_bytes", 0L);
        dimensionRow.setField("total_packets", 0L);
        dimensionRow.setField("total_bytes_sent", 0L);
        dimensionRow.setField("total_bytes_received", 0L);
        dimensionRow.setField("total_packets_sent", 0L);
        dimensionRow.setField("total_packets_received", 0L);
        dimensionRow.setField("session_count", 1);
        dimensionRow.setField("total_duration", 0L);
        dimensionRow.setField("avg_bps", 0.0);

        // 设置时间字段
        DateTimeFormatter formatter = DateTimeUtils.DEFAULT_DATE_TIME_FORMATTER != null
                ? DateTimeUtils.DEFAULT_DATE_TIME_FORMATTER
                : DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String currentTime = LocalDateTime.now().format(formatter);
        dimensionRow.setField("first_seen", currentTime);
        dimensionRow.setField("last_seen", currentTime);
        dimensionRow.setField("create_time", currentTime);
        dimensionRow.setField("update_time", currentTime);

        return dimensionRow;
    }

    /**
     * 从Row中获取字符串字段值
     *
     * @param row Row对象
     * @param fieldName 字段名
     * @return 字段值，如果不存在或类型不匹配则返回null
     */
    private String getStringFieldValue(Row row, String fieldName) {
        try {
            Set<String> fieldNames = row.getFieldNames(true);
            if (fieldNames != null && fieldNames.contains(fieldName)) {
                Object fieldValue = row.getField(fieldName);
                if (fieldValue instanceof String) {
                    return (String) fieldValue;
                }
            }
        } catch (Exception e) {
            log.warn("获取字符串字段值时发生错误，字段名: {}, 错误: {}", fieldName, e.getMessage());
        }
        return null;
    }
}
