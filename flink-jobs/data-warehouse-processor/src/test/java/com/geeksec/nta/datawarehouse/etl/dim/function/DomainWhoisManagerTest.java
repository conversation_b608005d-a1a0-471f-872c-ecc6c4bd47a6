package com.geeksec.nta.datawarehouse.etl.dim.function;

import com.geeksec.common.utils.metadata.DomainWhoisManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * DomainWhoisManager测试类
 * 测试基于WhoisXML API CSV数据的域名WHOIS功能
 *
 * <AUTHOR>
 */
@DisplayName("DomainWhoisManager功能测试")
public class DomainWhoisManagerTest {

    private DomainWhoisManager domainWhoisManager;

    @BeforeEach
    public void setUp() {
        domainWhoisManager = DomainWhoisManager.getInstance();
    }

    @Test
    @DisplayName("测试获取域名WHOIS信息")
    public void testGetDomainWhois() {
        // 测试样例数据中的域名
        String beetilWhois = domainWhoisManager.getDomainWhois("beetil.asia");
        assertNotNull(beetilWhois, "beetil.asia的whois信息不应为null");
        System.out.println("beetil.asia WHOIS: " + beetilWhois);
        
        // 测试另一个样例域名
        String aquasourceWhois = domainWhoisManager.getDomainWhois("aquasource.asia");
        assertNotNull(aquasourceWhois, "aquasource.asia的whois信息不应为null");
        System.out.println("aquasource.asia WHOIS: " + aquasourceWhois);
        
        // 测试不存在的域名
        String unknownWhois = domainWhoisManager.getDomainWhois("unknown-domain-12345.com");
        assertEquals("", unknownWhois, "不存在的域名应该返回空字符串");
    }

    @Test
    @DisplayName("测试详细WHOIS信息获取")
    public void testGetDetailedWhoisInfo() {
        // 测试获取详细WHOIS信息
        DomainWhoisManager.DomainWhoisInfo detailedInfo = domainWhoisManager.getDomainWhoisInfo("beetil.asia");
        if (detailedInfo != null) {
            assertNotNull(detailedInfo.getDomainName(), "域名不应为null");
            System.out.println("详细WHOIS信息: " + detailedInfo.toString());
            System.out.println("注册商: " + detailedInfo.getRegistrarName());
            System.out.println("组织: " + detailedInfo.getRegistrantOrganization());
            System.out.println("创建日期: " + detailedInfo.getCreatedDate());
            System.out.println("过期日期: " + detailedInfo.getExpiresDate());
        } else {
            System.out.println("未找到beetil.asia的详细WHOIS信息");
        }
    }

    @Test
    @DisplayName("测试空值和无效输入处理")
    public void testNullAndInvalidInputs() {
        // 测试null输入
        String nullResult = domainWhoisManager.getDomainWhois(null);
        assertEquals("", nullResult, "null输入应该返回空字符串");

        // 测试空字符串输入
        String emptyResult = domainWhoisManager.getDomainWhois("");
        assertEquals("", emptyResult, "空字符串输入应该返回空字符串");

        // 测试空白字符串输入
        String blankResult = domainWhoisManager.getDomainWhois("   ");
        assertEquals("", blankResult, "空白字符串输入应该返回空字符串");
    }

    @Test
    @DisplayName("测试缓存统计信息")
    public void testCacheStatistics() {
        // 触发数据加载
        domainWhoisManager.getDomainWhois("beetil.asia");
        
        // 检查缓存大小
        int cacheSize = domainWhoisManager.getCacheSize();
        assertTrue(cacheSize > 0, "缓存应该包含数据");
        System.out.println("缓存中的域名数量: " + cacheSize);
    }

    @Test
    @DisplayName("测试多个域名查询")
    public void testMultipleDomainQueries() {
        String[] testDomains = {
            "beetil.asia",
            "aquasource.asia", 
            "abercrombieandfitch.asia",
            "13thstreet.asia",
            "birdstrike.asia"
        };

        for (String domain : testDomains) {
            String whois = domainWhoisManager.getDomainWhois(domain);
            assertNotNull(whois, "域名 " + domain + " 的whois查询不应返回null");
            System.out.println("域名: " + domain + ", WHOIS: " + 
                (whois.isEmpty() ? "[无数据]" : whois.substring(0, Math.min(100, whois.length()))));
        }
    }

    @Test
    @DisplayName("测试WHOIS数据刷新功能")
    public void testWhoisRefresh() {
        String domain = "beetil.asia";

        // 获取初始whois信息
        String initialWhois = domainWhoisManager.getDomainWhois(domain);
        
        // 刷新缓存
        domainWhoisManager.refresh();
        
        // 再次获取whois信息
        String refreshedWhois = domainWhoisManager.getDomainWhois(domain);

        // 验证刷新后的结果
        assertEquals(initialWhois, refreshedWhois, "刷新前后的whois信息应该一致");
    }

    @Test
    @DisplayName("测试性能")
    public void testPerformance() {
        String[] domains = {
            "beetil.asia", "aquasource.asia", "abercrombieandfitch.asia", 
            "13thstreet.asia", "birdstrike.asia", "colorcon.asia",
            "crisispro.asia", "acwax.asia", "bowlingball.asia", "biketour.asia"
        };

        long startTime = System.currentTimeMillis();

        for (String domain : domains) {
            String whois = domainWhoisManager.getDomainWhois(domain);
            assertNotNull(whois, "域名 " + domain + " 的whois查询不应返回null");
        }

        long duration = System.currentTimeMillis() - startTime;
        System.out.println("查询 " + domains.length + " 个域名的whois信息耗时: " + duration + "ms");
        
        // 性能应该很快（小于1秒）
        assertTrue(duration < 1000, "查询性能应该在1秒内完成");
    }
}
