package com.geeksec.nta.graphbuilder.extractor.base;

import de.malkusch.whoisServerList.publicSuffixList.PublicSuffixList;
import de.malkusch.whoisServerList.publicSuffixList.PublicSuffixListFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;

import java.util.Map;

/**
 * 基础顶点提取器
 * 所有顶点提取器的基类，提供公共方法和初始化逻辑
 *
 * <AUTHOR> Team
 */
@Slf4j
public abstract class BaseVertexExtractor extends RichFlatMapFunction<Map<String, Object>, Map<String, Object>> {

    /** 公共后缀列表工厂 */
    protected static PublicSuffixListFactory factory = null;

    /** 公共后缀列表 */
    protected static PublicSuffixList suffixList = null;

    /**
     * 初始化方法
     * 在算子初始化时调用，用于初始化公共资源
     *
     * @param parameters 配置参数
     * @throws Exception 如果初始化失败
     */
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        // 初始化公共后缀列表
        if (factory == null) {
            factory = new PublicSuffixListFactory();
            suffixList = factory.build();
        }
    }

    /**
     * 获取基础域名
     * 从域名中提取注册域名
     *
     * @param domain 域名
     * @return 基础域名，如果无法获取则返回null
     */
    protected String getRegistrableDomain(String domain) {
        if (domain == null || domain.isEmpty()) {
            return null;
        }

        try {
            // 使用getRegistrableDomain方法获取注册域名
            String registrableDomain = suffixList.getRegistrableDomain(domain);
            if (registrableDomain != null) {
                return registrableDomain;
            }
        } catch (Exception e) {
            log.warn("获取可注册域名失败, domain: {}, error: {}", domain, e.getMessage());
        }

        return null;
    }
}
