package com.geeksec.nta.graphbuilder.config;

import com.geeksec.common.config.ConfigurationManager;
import com.geeksec.nta.graphbuilder.constant.ProcessingConstants;
import lombok.Getter;
import org.apache.flink.api.java.utils.ParameterTool;

/**
 * 图构建器配置类
 * 提供图构建相关的配置参数
 *
 * <AUTHOR> Team
 */
public class GraphBuilderConfig {

    /** 配置工具实例 */
    private static final ParameterTool CONFIG = ConfigurationManager.getConfig();

    /** Kafka 服务器地址 */
    @Getter
    private static final String kafkaBootstrapServers = CONFIG.get("kafka.bootstrap.servers");

    /** Kafka 消费者组ID */
    @Getter
    private static final String kafkaGroupId = CONFIG.get("kafka.group.id", "graph-builder");

    /** Kafka 会话信息主题 */
    @Getter
    private static final String kafkaConnectTopic = CONFIG.get("kafka.topic.connect", "enriched-connect-info");

    /** Kafka DNS信息主题 */
    @Getter
    private static final String kafkaDnsTopic = CONFIG.get("kafka.topic.dns", "enriched-dns-info");

    /** Kafka HTTP信息主题 */
    @Getter
    private static final String kafkaHttpTopic = CONFIG.get("kafka.topic.http", "enriched-http-info");

    /** Kafka SSL信息主题 */
    @Getter
    private static final String kafkaSslTopic = CONFIG.get("kafka.topic.ssl", "enriched-ssl-info");

    /** Nebula Graph 空间名称 */
    @Getter
    private static final String nebulaGraphSpace = CONFIG.get("nebula.graph.space", "nta");

    /** Nebula Graph 地址 */
    @Getter
    private static final String nebulaGraphAddress = CONFIG.get("nebula.graph.address", "127.0.0.1:9669");

    /** Nebula Meta 地址 */
    @Getter
    private static final String nebulaMetaAddress = CONFIG.get("nebula.meta.address", "127.0.0.1:9559");

    /** 源并行度 */
    @Getter
    private static final int sourceParallelism = CONFIG.getInt("parallelism.source", ProcessingConstants.Parallelism.SOURCE);

    /** 处理并行度 */
    @Getter
    private static final int processParallelism = CONFIG.getInt("parallelism.process", ProcessingConstants.Parallelism.PROCESS);

    /** 写入并行度 */
    @Getter
    private static final int sinkParallelism = CONFIG.getInt("parallelism.sink", ProcessingConstants.Parallelism.SINK);

    /** 批处理大小 */
    @Getter
    private static final int batchSize = CONFIG.getInt("batch.size", ProcessingConstants.Batch.SIZE);

    /** 批处理间隔（毫秒） */
    @Getter
    private static final int batchInterval = CONFIG.getInt("batch.interval", ProcessingConstants.Batch.INTERVAL);
}
