package com.geeksec.nta.graphbuilder.model.vertex;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 证书顶点
 * 表示SSL证书信息
 *
 * <AUTHOR> Team
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CertVertex extends BaseVertex {
    /**
     * 证书ID
     */
    private String certId;

    /**
     * 威胁分数
     */
    private Integer threatScore;

    /**
     * 信任分数
     */
    private Integer trustScore;

    /**
     * 备注
     */
    private String remark;

    @Override
    public String getVertexId() {
        return certId;
    }
    
    public String getCertId() {
        return certId;
    }
    
    public Integer getThreatScore() {
        return threatScore;
    }
    
    public Integer getTrustScore() {
        return trustScore;
    }
    
    public String getRemark() {
        return remark;
    }

    /**
     * 获取顶点标签
     *
     * @return 顶点标签常量
     */
    @Override
    public VertexTag getVertexTag() {
        return VertexTag.CERT;
    }
}
