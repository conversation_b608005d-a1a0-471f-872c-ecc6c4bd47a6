package com.geeksec.nta.graphbuilder.model.vertex;

import java.io.Serializable;
import java.util.Objects;

/**
 * 基础顶点类
 * 所有顶点实体的基类
 *
 * <AUTHOR>
 */
public abstract class BaseVertex implements Serializable {
    private static final long serialVersionUID = 1L;

    // Common fields that might be used by subclasses
    protected Integer threatScore;
    protected Integer trustScore;
    protected String remark;

    /**
     * 获取顶点ID
     *
     * @return 顶点ID
     */
    public abstract String getVertexId();

    /**
     * 获取顶点标签
     *
     * @return 顶点标签枚举
     */
    public abstract VertexTag getVertexTag();

    // Common getters and setters
    public Integer getThreatScore() {
        return threatScore;
    }

    public void setThreatScore(Integer threatScore) {
        this.threatScore = threatScore;
    }

    public Integer getTrustScore() {
        return trustScore;
    }

    public void setTrustScore(Integer trustScore) {
        this.trustScore = trustScore;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        BaseVertex that = (BaseVertex) o;
        return Objects.equals(threatScore, that.threatScore) &&
               Objects.equals(trustScore, that.trustScore) &&
               Objects.equals(remark, that.remark);
    }

    @Override
    public int hashCode() {
        return Objects.hash(threatScore, trustScore, remark);
    }

    @Override
    public String toString() {
        return "BaseVertex{" +
               "threatScore=" + threatScore +
               ", trustScore=" + trustScore +
               ", remark='" + remark + '\'' +
               '}';
    }
}
