package com.geeksec.nta.graphbuilder.model.vertex;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * SSL指纹顶点
 * 表示SSL指纹信息
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SslFingerprintVertex extends BaseVertex {
    /**
     * JA3哈希值
     */
    private String ja3Hash;

    /**
     * 指纹描述
     */
    private String fingerDesc;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 威胁分数
     */
    private Integer threatScore;

    /**
     * 信任分数
     */
    private Integer trustScore;

    /**
     * 获取顶点ID
     *
     * @return 顶点ID
     */
    @Override
    public String getVertexId() {
        return ja3Hash;
    }
    
    public String getJa3Hash() {
        return ja3Hash;
    }
    
    public String getFingerDesc() {
        return fingerDesc;
    }
    
    public Integer getType() {
        return type;
    }
    
    public Integer getThreatScore() {
        return threatScore;
    }
    
    public Integer getTrustScore() {
        return trustScore;
    }

    /**
     * 获取顶点标签
     *
     * @return 顶点标签常量
     */
    @Override
    public VertexTag getVertexTag() {
        return VertexTag.SSL_FINGERPRINT;
    }
}
