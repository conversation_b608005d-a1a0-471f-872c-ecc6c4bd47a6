package com.geeksec.nta.graphbuilder.extractor.dns;

import com.geeksec.common.utils.net.IpUtils;
import com.geeksec.nta.graphbuilder.extractor.base.BaseEdgeExtractor;
import com.geeksec.nta.graphbuilder.model.edge.BaseEdge;
import com.geeksec.nta.graphbuilder.model.edge.ClientQueriesDomainEdge;
import com.geeksec.nta.graphbuilder.model.edge.DomainResolvesToIpEdge;
import com.geeksec.nta.graphbuilder.model.edge.EdgeType;
import com.geeksec.nta.graphbuilder.utils.net.DomainUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.flink.shaded.netty4.io.netty.util.internal.StringUtil;
import org.apache.flink.util.Collector;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * DNS边提取器
 * 从DNS数据中提取边关系数据，包括DNS查询关系和解析关系
 *
 * <AUTHOR> Team
 */
@Slf4j
public class DnsEdgeExtractor extends BaseEdgeExtractor {

    /**
     * 从DNS信息中生成各类边关系
     *
     * @param pbMap 数据映射
     * @param collector 收集器
     * @throws Exception 如果处理失败
     */
    @Override
    public void flatMap(Map<String, Object> pbMap, Collector<Map<String, Object>> collector) throws Exception {
        // 获取DNS信息
        String clientIp = (String) pbMap.get("sIp");
        String serverIp = (String) pbMap.get("dIp");
        String domain = (String) pbMap.get("Domain");

        // 检查必要字段
        if (StringUtil.isNullOrEmpty(clientIp) || StringUtil.isNullOrEmpty(serverIp) ||
            StringUtil.isNullOrEmpty(domain) || !DomainUtils.isValidDomain(domain)) {
            return;
        }

        // 1. 客户端查询域名边
        ClientQueriesDomainEdge clientQueryDomainEdge = new ClientQueriesDomainEdge();
        clientQueryDomainEdge.setSrcId(clientIp);
        clientQueryDomainEdge.setDstId(domain);
        // 尝试将DNS类型和应答类型转换为整数
        try {
            String typeStr = (String) pbMap.get("Type");
            if (typeStr != null) {
                clientQueryDomainEdge.setDnsType(Integer.parseInt(typeStr));
            }

            String answerTypeStr = (String) pbMap.get("AnswerType");
            if (answerTypeStr != null) {
                clientQueryDomainEdge.setAnswerType(Integer.parseInt(answerTypeStr));
            }
        } catch (NumberFormatException e) {
            // 如果转换失败，设置默认值
            clientQueryDomainEdge.setDnsType(0);
            clientQueryDomainEdge.setAnswerType(0);
        }

        // 2. 客户端查询DNS服务器边
        BaseEdge clientQueryDnsServerEdge = new BaseEdge() {
            @Override
                        public EdgeType getEdgeType() {
                            return EdgeType.CLIENT_QUERIES_DNS_SERVER;
                        }
        };
        clientQueryDnsServerEdge.setSrcId(clientIp);
        clientQueryDnsServerEdge.setDstId(serverIp);

        // 3. DNS服务器解析域名边
        BaseEdge dnsServerResolveDomainEdge = new BaseEdge() {
            @Override
                        public EdgeType getEdgeType() {
                            return EdgeType.DNS_SERVER_RESOLVES_DOMAIN;
                        }
        };
        dnsServerResolveDomainEdge.setSrcId(serverIp);
        dnsServerResolveDomainEdge.setDstId(domain);

        // 4. 域名解析到IP边
        List<DomainResolvesToIpEdge> domainResolveToIpEdges = getDomainResolveToIpEdges(pbMap);

        // 5. CNAME别名边和CNAME解析到IP边
        List<BaseEdge> cnameAliasesDomainEdges = getCnameAliasesDomainEdges(pbMap);
        List<BaseEdge> cnameResolvesToIpEdges = getCnameResolvesToIpEdges(pbMap);

        // 合并由DNS元数据产出的边信息
        HashMap<String, Object> resultMap = new HashMap<>(8);
        resultMap.put("clientQueryDomainEdge", clientQueryDomainEdge);
        resultMap.put("clientQueryDnsServerEdge", clientQueryDnsServerEdge);
        resultMap.put("dnsServerResolveDomainEdge", dnsServerResolveDomainEdge);
        resultMap.put("dnsParseToEdgeList", domainResolveToIpEdges);
        resultMap.put("cnameAliasDomainEdgeList", cnameAliasesDomainEdges);
        resultMap.put("cnameResolvesToIpEdges", cnameResolvesToIpEdges);

        collector.collect(resultMap);
    }

    /**
     * 获取域名解析到IP的边
     *
     * @param pbMap 数据映射
     * @return 域名解析到IP的边列表
     */
    private List<DomainResolvesToIpEdge> getDomainResolveToIpEdges(Map<String, Object> pbMap) {
        List<DomainResolvesToIpEdge> edges = new ArrayList<>();

        String domain = (String) pbMap.get("Domain");
        String dnsServer = (String) pbMap.get("dIp");
        List<String> resolvedIps = (List<String>) pbMap.get("ResolvedIps");

        if (StringUtil.isNullOrEmpty(domain) || !DomainUtils.isValidDomain(domain) ||
            CollectionUtils.isEmpty(resolvedIps)) {
            return edges;
        }

        for (String ip : resolvedIps) {
            if (IpUtils.isIpAddress(ip)) {
                DomainResolvesToIpEdge edge = new DomainResolvesToIpEdge();
                edge.setSrcId(domain);
                edge.setDstId(ip);
                edge.setDnsServer(dnsServer);
                edge.setFinalParse(true);

                // 设置记录类型
                String type = (String) pbMap.get("Type");
                if (!StringUtil.isNullOrEmpty(type)) {
                    edge.setRecordType(type);
                }

                // 设置TTL信息
                Integer ttl = (Integer) pbMap.get("TTL");
                if (ttl != null) {
                    edge.setMaxTtl(ttl);
                    edge.setMinTtl(ttl);
                }

                edges.add(edge);
            }
        }

        return edges;
    }

    /**
     * 获取CNAME别名边
     *
     * @param pbMap 数据映射
     * @return CNAME别名边列表
     */
    private List<BaseEdge> getCnameAliasesDomainEdges(Map<String, Object> pbMap) {
        List<BaseEdge> edges = new ArrayList<>();

        String domain = (String) pbMap.get("Domain");
        List<String> cnameList = (List<String>) pbMap.get("CnameList");

        if (StringUtil.isNullOrEmpty(domain) || !DomainUtils.isValidDomain(domain) ||
            CollectionUtils.isEmpty(cnameList)) {
            return edges;
        }

        for (String cname : cnameList) {
            if (DomainUtils.isValidDomain(cname)) {
                BaseEdge edge = new BaseEdge() {
                    @Override
                        public EdgeType getEdgeType() {
                            return EdgeType.CNAME_POINTS_TO_DOMAIN;
                        }
                };
                edge.setSrcId(domain);
                edge.setDstId(cname);
                edges.add(edge);
            }
        }

        return edges;
    }

    /**
     * 获取CNAME解析到IP的边
     * 注意：根据最新的图模型，CNAME不再直接解析到IP，而是通过CNAME->Domain->IP的关系
     * 此方法保留但返回空列表
     *
     * @param pbMap 数据映射
     * @return CNAME解析到IP的边列表（始终为空）
     */
    private List<BaseEdge> getCnameResolvesToIpEdges(Map<String, Object> pbMap) {
        // 根据最新的图模型，CNAME不再直接解析到IP，而是通过CNAME->Domain->IP的关系
        return new ArrayList<>();
    }
}
