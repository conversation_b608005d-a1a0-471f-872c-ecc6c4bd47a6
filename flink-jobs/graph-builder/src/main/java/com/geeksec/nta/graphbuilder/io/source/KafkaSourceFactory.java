package com.geeksec.nta.graphbuilder.io.source;

import com.geeksec.common.config.ConfigurationManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.OffsetResetStrategy;

import java.util.Collections;
import java.util.Map;
import java.util.Properties;

import static com.geeksec.common.config.ConfigConstants.*;
import static com.geeksec.nta.graphbuilder.constant.ProcessingConstants.Parallelism.PA4;

/**
 * Kafka数据源工厂
 * 负责创建和配置Kafka数据源，提供统一的配置和实例化方法
 *
 * <AUTHOR> Team
 */
@Slf4j
public class KafkaSourceFactory {
    /** 配置工具实例 */
    private static final ParameterTool CONFIG = ConfigurationManager.getConfig();

    /** Kafka 服务器地址 */
    public static final String BOOTSTRAP_SERVERS = CONFIG.get(KAFKA_BOOTSTRAP_SERVERS);

    /** Kafka 消费者组ID */
    public static final String GROUP_ID = CONFIG.get(KAFKA_GROUP_ID);

    /** 模型切换消费者组ID */
    public static final String MODEL_GROUP_ID = CONFIG.get(KAFKA_MODEL_SWITCH_GROUP_ID);

    /** Kafka主题 */
    public static final String TOPIC = CONFIG.get(KAFKA_TOPIC);

    /** 模型切换主题 */
    public static final String MODEL_TOPIC = CONFIG.get(KAFKA_MODEL_SWITCH_TOPIC);

    /** 证书文件主题 */
    public static final String CERT_TOPIC = CONFIG.get(CERT_KAFKA_TOPIC_NAME);

    /** 证书文件消费者组ID */
    public static final String CERT_GROUP_ID = CONFIG.get(CERT_KAFKA_GROUP_ID);

    /**
     * 创建Kafka消费者配置
     * 优化配置以提高稳定性和性能
     *
     * @return Kafka消费者配置属性
     */
    public static Properties createConsumerProperties() {
        Properties properties = new Properties();
        // 设置Kafka服务器地址
        properties.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, BOOTSTRAP_SERVERS);
        // 增加请求超时时间，提高稳定性
        properties.put(ConsumerConfig.REQUEST_TIMEOUT_MS_CONFIG, "60000");
        // 设置批量获取大小，提高吞吐量
        properties.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, "500");
        // 设置心跳间隔，提高稳定性
        properties.put(ConsumerConfig.HEARTBEAT_INTERVAL_MS_CONFIG, "3000");
        // 设置会话超时时间
        properties.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, "30000");
        // 设置最大拉取等待时间
        properties.put(ConsumerConfig.FETCH_MAX_WAIT_MS_CONFIG, "500");
        // 设置自动提交间隔
        properties.put(ConsumerConfig.AUTO_COMMIT_INTERVAL_MS_CONFIG, "5000");

        log.info("Kafka consumer properties configured with bootstrap.servers: {}", BOOTSTRAP_SERVERS);
        return properties;
    }

    /**
     * 获取元数据Kafka流
     *
     * @param env Flink流执行环境
     * @return 包含元数据的数据流
     */
    public static DataStream<Map<String, Object>> getMetaDataKafkaStream(StreamExecutionEnvironment env) {
        // 创建消费者配置
        Properties properties = createConsumerProperties();
        // 设置消费者组ID
        properties.put(ConsumerConfig.GROUP_ID_CONFIG, GROUP_ID);

        log.info("Creating Kafka source with topic: {}, group.id: {}", TOPIC, GROUP_ID);

        KafkaSource<Map<String, Object>> kafkaSource = KafkaSource.<Map<String, Object>>builder()
                .setBootstrapServers(BOOTSTRAP_SERVERS)
                .setTopics(Collections.singletonList(TOPIC))
                // 从最新的偏移量开始消费
                .setStartingOffsets(OffsetsInitializer.committedOffsets(OffsetResetStrategy.LATEST))
                .setDeserializer(new KafkaJsonDeserializer())
                .setGroupId(GROUP_ID)
                .setProperties(properties)
                .build();

        // 使用配置中的并行度，如果未配置则使用默认值
        int parallelism = CONFIG.getInt(PARALLELISM_KAFKA_SOURCE, PA4);
        log.info("Kafka source parallelism set to: {}", parallelism);

        // 开始读取Kafka数据流
        return env.fromSource(
                kafkaSource,
                WatermarkStrategy.noWatermarks(),
                "Kafka-Source",
                TypeInformation.of(new TypeHint<Map<String, Object>>() {
                })).name("Kafka元数据流").setParallelism(parallelism);
    }

    /**
     * 获取各类信息Kafka流
     *
     * @param env Flink流执行环境
     * @param topic Kafka主题
     * @param groupId 消费者组ID
     * @param streamName 流名称
     * @return 包含连接信息的数据流
     */
    public static DataStream<Map<String, Object>> getTypedKafkaStream(
            StreamExecutionEnvironment env,
            String topic,
            String groupId,
            String streamName) {
        // 创建消费者配置
        Properties properties = createConsumerProperties();
        // 设置消费者组ID
        properties.put(ConsumerConfig.GROUP_ID_CONFIG, groupId);

        log.info("Creating Kafka source with topic: {}, group.id: {}", topic, groupId);

        KafkaSource<Map<String, Object>> kafkaSource = KafkaSource.<Map<String, Object>>builder()
                .setBootstrapServers(BOOTSTRAP_SERVERS)
                .setTopics(Collections.singletonList(topic))
                // 从最新的偏移量开始消费
                .setStartingOffsets(OffsetsInitializer.committedOffsets(OffsetResetStrategy.LATEST))
                .setDeserializer(new KafkaJsonDeserializer())
                .setGroupId(groupId)
                .setProperties(properties)
                .build();

        // 使用配置中的并行度，如果未配置则使用默认值
        int parallelism = CONFIG.getInt(PARALLELISM_KAFKA_SOURCE, PA4);
        log.info("Kafka source parallelism set to: {}", parallelism);

        // 开始读取Kafka数据流
        return env.fromSource(
                kafkaSource,
                WatermarkStrategy.noWatermarks(),
                "Kafka-" + streamName + "-Source",
                TypeInformation.of(new TypeHint<Map<String, Object>>() {
                })).name("Kafka " + streamName + " 流").setParallelism(parallelism);
    }
}
