package com.geeksec.nta.graphbuilder.converter.edge;

import com.geeksec.nta.graphbuilder.io.sink.tag.NebulaGraphOutputTag;
import com.geeksec.nta.graphbuilder.model.edge.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

import java.util.List;
import java.util.Map;

/**
 * 证书边转换器
 * 处理证书相关的边数据，将其转换为Flink Row格式
 *
 * <AUTHOR>
 */
@Slf4j
public class CertificateEdgeConverter extends ProcessFunction<Map<String, Object>, Row> {

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
    }

    @Override
    public void processElement(Map<String, Object> edgeMap, Context ctx, Collector<Row> out) throws Exception {
        // 处理客户端使用证书边
        processClientUseCertEdges(edgeMap, ctx, out);

        // 处理服务端使用证书边
        processServerUseCertEdges(edgeMap, ctx, out);

        // 处理客户端验证证书边
        processClientVerifyCertEdges(edgeMap, ctx, out);

        // 处理证书服务SNI边
        processCertServesSniEdges(edgeMap, ctx, out);

        // 处理客户端呈现指纹边
        processClientPresentsFingerprintEdges(edgeMap, ctx, out);

        // 处理服务端呈现指纹边
        processServerPresentsFingerprintEdges(edgeMap, ctx, out);

        // 处理指纹与证书一同出现边
        processFingerprintAppearsWithCertEdges(edgeMap, ctx, out);

        // 处理指纹与域名一同出现边
        processFingerprintAppearsWithDomainEdges(edgeMap, ctx, out);

        // 处理SNI绑定到基础域名边
        processSniBindsToBaseDomainEdges(edgeMap, ctx, out);
    }

    /**
     * 处理IP提供证书边
     *
     * @param edgeMap 边数据映射
     * @param ctx 上下文，用于访问侧输出流
     * @param out 收集器
     */
    private void processClientUseCertEdges(Map<String, Object> edgeMap, Context ctx, Collector<Row> out) {
        // 处理客户端提供证书边列表
        List<IpProvidesCertEdge> clientUseCertEdgeList = (List<IpProvidesCertEdge>) edgeMap.get("clientUseCertEdgeList");
        if (!CollectionUtils.isEmpty(clientUseCertEdgeList)) {
            for (IpProvidesCertEdge edge : clientUseCertEdgeList) {
                Row ipProvidesCertRow = new Row(5);
                ipProvidesCertRow.setField(0, edge.getSrcId());
                ipProvidesCertRow.setField(1, edge.getDstId());
                ipProvidesCertRow.setField(2, 0);
                // 设置role为client
                ipProvidesCertRow.setField(3, "client");
                // 设置SNI信息
                ipProvidesCertRow.setField(4, edge.getSni());

                // 直接输出到证书发行边的侧输出流
                ctx.output(NebulaGraphOutputTag.Edge.CERTIFICATE_ISSUE, ipProvidesCertRow);
            }
        }
    }

    /**
     * 处理IP提供证书边
     *
     * @param edgeMap 边数据映射
     * @param ctx 上下文，用于访问侧输出流
     * @param out 收集器
     */
    private void processServerUseCertEdges(Map<String, Object> edgeMap, Context ctx, Collector<Row> out) {
        // 处理服务端提供证书边列表
        List<IpProvidesCertEdge> serverUseCertEdgeList = (List<IpProvidesCertEdge>) edgeMap.get("serverUseCertEdgeList");
        if (!CollectionUtils.isEmpty(serverUseCertEdgeList)) {
            for (IpProvidesCertEdge edge : serverUseCertEdgeList) {
                Row ipProvidesCertRow = new Row(5);
                ipProvidesCertRow.setField(0, edge.getSrcId());
                ipProvidesCertRow.setField(1, edge.getDstId());
                ipProvidesCertRow.setField(2, 0);
                // 设置role为server
                ipProvidesCertRow.setField(3, "server");
                // 设置SNI信息
                ipProvidesCertRow.setField(4, edge.getSni());

                // 直接输出到证书发行边的侧输出流
                ctx.output(NebulaGraphOutputTag.Edge.CERTIFICATE_ISSUE, ipProvidesCertRow);
            }
        }
    }

    /**
     * 处理客户端接收证书边
     *
     * @param edgeMap 边数据映射
     * @param ctx 上下文，用于访问侧输出流
     * @param out 收集器
     */
    private void processClientVerifyCertEdges(Map<String, Object> edgeMap, Context ctx, Collector<Row> out) {
        // 处理客户端接收证书边列表
        List<ClientReceivesCertEdge> clientVerifyCertEdgeList = (List<ClientReceivesCertEdge>) edgeMap.get("clientVerifyCertEdgeList");
        if (!CollectionUtils.isEmpty(clientVerifyCertEdgeList)) {
            for (ClientReceivesCertEdge edge : clientVerifyCertEdgeList) {
                Row clientVerifyCertRow = new Row(3);
                clientVerifyCertRow.setField(0, edge.getSrcId());
                clientVerifyCertRow.setField(1, edge.getDstId());
                clientVerifyCertRow.setField(2, 0);

                // 直接输出到证书使用边的侧输出流
                ctx.output(NebulaGraphOutputTag.Edge.CERTIFICATE_USE, clientVerifyCertRow);
            }
        }
    }

    /**
     * 处理证书服务SNI边
     *
     * @param edgeMap 边数据映射
     * @param ctx 上下文，用于访问侧输出流
     * @param out 收集器
     */
    private void processCertServesSniEdges(Map<String, Object> edgeMap, Context ctx, Collector<Row> out) {
        // 处理证书服务SNI边列表
        List<BaseEdge> certServesSniEdgeList = (List<BaseEdge>) edgeMap.get("certServesSniEdgeList");
        if (!CollectionUtils.isEmpty(certServesSniEdgeList)) {
            for (BaseEdge edge : certServesSniEdgeList) {
                Row certServesSniRow = new Row(3);
                certServesSniRow.setField(0, edge.getSrcId());
                certServesSniRow.setField(1, edge.getDstId());
                certServesSniRow.setField(2, 0);

                // 直接输出到证书使用边的侧输出流
                ctx.output(NebulaGraphOutputTag.Edge.CERTIFICATE_USE, certServesSniRow);
            }
        }
    }

    /**
     * 处理IP呈现TLS指纹特征边
     *
     * @param edgeMap 边数据映射
     * @param ctx 上下文，用于访问侧输出流
     * @param out 收集器
     */
    private void processClientPresentsFingerprintEdges(Map<String, Object> edgeMap, Context ctx, Collector<Row> out) {
        // 处理客户端呈现指纹边列表
        List<BaseEdge> clientPresentsFingerprintEdgeList = (List<BaseEdge>) edgeMap.get("clientPresentsFingerprintEdgeList");
        if (!CollectionUtils.isEmpty(clientPresentsFingerprintEdgeList)) {
            for (BaseEdge edge : clientPresentsFingerprintEdgeList) {
                Row ipPresentsFingerprintRow = new Row(4);
                ipPresentsFingerprintRow.setField(0, edge.getSrcId());
                ipPresentsFingerprintRow.setField(1, edge.getDstId());
                ipPresentsFingerprintRow.setField(2, 0);

                // 设置role属性
                String role = "client";
                if (edge instanceof IpPresentsFingerprintEdge) {
                    Integer roleValue = ((IpPresentsFingerprintEdge) edge).getRole();
                    if (roleValue != null) {
                        role = roleValue == 1 ? "client" : "server";
                    }
                }
                ipPresentsFingerprintRow.setField(4, role);

                // 直接输出到证书使用边的侧输出流
                ctx.output(NebulaGraphOutputTag.Edge.CERTIFICATE_USE, ipPresentsFingerprintRow);
            }
        }
    }

    /**
     * 处理服务端呈现TLS指纹特征边
     *
     * @param edgeMap 边数据映射
     * @param ctx 上下文，用于访问侧输出流
     * @param out 收集器
     */
    private void processServerPresentsFingerprintEdges(Map<String, Object> edgeMap, Context ctx, Collector<Row> out) {
        // 处理服务端呈现指纹边列表
        List<BaseEdge> serverPresentsFingerprintEdgeList = (List<BaseEdge>) edgeMap.get("serverPresentsFingerprintEdgeList");
        if (!CollectionUtils.isEmpty(serverPresentsFingerprintEdgeList)) {
            for (BaseEdge edge : serverPresentsFingerprintEdgeList) {
                Row ipPresentsFingerprintRow = new Row(4);
                ipPresentsFingerprintRow.setField(0, edge.getSrcId());
                ipPresentsFingerprintRow.setField(1, edge.getDstId());
                ipPresentsFingerprintRow.setField(2, 0);

                // 设置role属性
                String role = "server";
                if (edge instanceof IpPresentsFingerprintEdge) {
                    Integer roleValue = ((IpPresentsFingerprintEdge) edge).getRole();
                    if (roleValue != null) {
                        role = roleValue == 1 ? "client" : "server";
                    }
                }
                ipPresentsFingerprintRow.setField(4, role);

                // 直接输出到证书使用边的侧输出流
                ctx.output(NebulaGraphOutputTag.Edge.CERTIFICATE_USE, ipPresentsFingerprintRow);
            }
        }
    }

    /**
     * 处理指纹与证书一同出现边
     *
     * @param edgeMap 边数据映射
     * @param ctx 上下文，用于访问侧输出流
     * @param out 收集器
     */
    private void processFingerprintAppearsWithCertEdges(Map<String, Object> edgeMap, Context ctx, Collector<Row> out) {
        // 处理指纹与证书一同出现边列表
        List<FingerprintAppearsWithCertEdge> fingerprintAppearsWithCertEdgeList = (List<FingerprintAppearsWithCertEdge>) edgeMap.get("fingerprintAppearsWithCertEdgeList");
        if (!CollectionUtils.isEmpty(fingerprintAppearsWithCertEdgeList)) {
            for (FingerprintAppearsWithCertEdge edge : fingerprintAppearsWithCertEdgeList) {
                Row fingerprintAppearsWithCertRow = new Row(4);
                fingerprintAppearsWithCertRow.setField(0, edge.getSrcId());
                fingerprintAppearsWithCertRow.setField(1, edge.getDstId());
                fingerprintAppearsWithCertRow.setField(2, 0);
                // 设置SNI信息
                fingerprintAppearsWithCertRow.setField(3, edge.getSni() != null ? edge.getSni() : "");

                // 直接输出到证书使用边的侧输出流
                ctx.output(NebulaGraphOutputTag.Edge.CERTIFICATE_USE, fingerprintAppearsWithCertRow);
            }
        }
    }

    /**
     * 处理指纹与域名一同出现边
     *
     * @param edgeMap 边数据映射
     * @param ctx 上下文，用于访问侧输出流
     * @param out 收集器
     */
    private void processFingerprintAppearsWithDomainEdges(Map<String, Object> edgeMap, Context ctx, Collector<Row> out) {
        // 处理指纹与域名一同出现边列表
        List<FingerprintAppearsWithDomainEdge> fingerprintAppearsWithDomainEdgeList = (List<FingerprintAppearsWithDomainEdge>) edgeMap.get("fingerprintAppearsWithDomainEdgeList");
        if (!CollectionUtils.isEmpty(fingerprintAppearsWithDomainEdgeList)) {
            for (FingerprintAppearsWithDomainEdge edge : fingerprintAppearsWithDomainEdgeList) {
                Row fingerprintAppearsWithDomainRow = new Row(3);
                fingerprintAppearsWithDomainRow.setField(0, edge.getSrcId());
                fingerprintAppearsWithDomainRow.setField(1, edge.getDstId());
                fingerprintAppearsWithDomainRow.setField(2, 0);

                // 直接输出到域名访问边的侧输出流
                ctx.output(NebulaGraphOutputTag.Edge.DOMAIN_ACCESS, fingerprintAppearsWithDomainRow);
            }
        }
    }

    /**
     * 处理SNI域名派生自基础域名边
     *
     * @param edgeMap 边数据映射
     * @param ctx 上下文，用于访问侧输出流
     * @param out 收集器
     */
    private void processSniBindsToBaseDomainEdges(Map<String, Object> edgeMap, Context ctx, Collector<Row> out) {
        // 处理SNI域名派生自基础域名边列表
        List<BaseEdge> sniBindsToBaseDomainEdgeList = (List<BaseEdge>) edgeMap.get("sniBindsToBaseDomainEdgeList");
        if (!CollectionUtils.isEmpty(sniBindsToBaseDomainEdgeList)) {
            for (BaseEdge edge : sniBindsToBaseDomainEdgeList) {
                Row sniBindsToBaseDomainRow = new Row(4);
                sniBindsToBaseDomainRow.setField(0, edge.getSrcId());
                sniBindsToBaseDomainRow.setField(1, edge.getDstId());
                sniBindsToBaseDomainRow.setField(2, 0);

                // 设置is_sni属性
                boolean isSni = false;
                if (edge instanceof DomainDerivesFromBaseDomainEdge) {
                    isSni = ((DomainDerivesFromBaseDomainEdge) edge).isSni();
                }
                sniBindsToBaseDomainRow.setField(4, isSni);

                // 直接输出到域名解析边的侧输出流
                ctx.output(NebulaGraphOutputTag.Edge.DOMAIN_RESOLVE, sniBindsToBaseDomainRow);
            }
        }
    }
}
