package com.geeksec.nta.graphbuilder.model.edge;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 证书包含特定基础域名作为其覆盖范围边
 * 表示证书与其覆盖的基础域名之间的关系
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CertCoversBaseDomainEdge extends BaseEdge {
    
    /**
     * 获取边类型
     *
     * @return 边类型常量
     */
    @Override
    public EdgeType getEdgeType() {
        return EdgeType.CERT_COVERS_REGISTRABLE_DOMAIN;
    }
}
