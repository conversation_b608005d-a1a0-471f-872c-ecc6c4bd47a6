package com.geeksec.nta.graphbuilder.converter.vertex;

import com.geeksec.nta.graphbuilder.io.sink.tag.NebulaGraphOutputTag;
import com.geeksec.nta.graphbuilder.model.vertex.IpVertex;
import com.geeksec.nta.graphbuilder.model.vertex.MacVertex;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

import java.util.Map;

/**
 * 会话顶点转换器
 * 处理网络会话相关的顶点数据（IP、MAC），将其转换为Flink Row格式
 *
 * <AUTHOR>
 */
@Slf4j
public class SessionVertexConverter extends ProcessFunction<Map<String, Object>, Row> {

    private static final long serialVersionUID = 1L;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
    }

    @Override
    public void processElement(Map<String, Object> vertexMap, Context ctx, Collector<Row> out) throws Exception {
        // 处理IP顶点并直接分流
        processIpVertex(vertexMap, ctx, out);

        // 处理MAC顶点并直接分流
        processMacVertex(vertexMap, ctx, out);
    }

    /**
     * 处理IP顶点并直接分流
     *
     * @param vertexMap 顶点数据映射
     * @param ctx 上下文
     * @param out 收集器
     */
    private void processIpVertex(Map<String, Object> vertexMap, Context ctx, Collector<Row> out) {
        // 处理客户端IP顶点
        IpVertex clientIpVertex = (IpVertex) vertexMap.get("clientIpTag");
        if (clientIpVertex != null) {
            Row ipVertexRow = new Row(6);
            ipVertexRow.setField(0, clientIpVertex.getIpAddr());
            ipVertexRow.setField(1, generateMd5(clientIpVertex.getIpAddr()));
            ipVertexRow.setField(2, clientIpVertex.getVersion());
            ipVertexRow.setField(3, clientIpVertex.getCity());
            ipVertexRow.setField(4, clientIpVertex.getCountry());
            ipVertexRow.setField(5, clientIpVertex.getRemark());

            // 直接输出到IP顶点的侧输出流
            ctx.output(NebulaGraphOutputTag.Vertex.IP, ipVertexRow);
        }

        // 处理服务端IP顶点
        IpVertex serverIpVertex = (IpVertex) vertexMap.get("serverIpTag");
        if (serverIpVertex != null) {
            Row ipVertexRow = new Row(6);
            ipVertexRow.setField(0, serverIpVertex.getIpAddr());
            ipVertexRow.setField(1, generateMd5(serverIpVertex.getIpAddr()));
            ipVertexRow.setField(2, serverIpVertex.getVersion());
            ipVertexRow.setField(3, serverIpVertex.getCity());
            ipVertexRow.setField(4, serverIpVertex.getCountry());
            ipVertexRow.setField(5, serverIpVertex.getRemark());

            // 直接输出到IP顶点的侧输出流
            ctx.output(NebulaGraphOutputTag.Vertex.IP, ipVertexRow);


        }

        // 处理通用IP顶点
        IpVertex ipVertex = (IpVertex) vertexMap.get("ipVertex");
        if (ipVertex != null) {
            Row ipVertexRow = new Row(6);
            ipVertexRow.setField(0, ipVertex.getIpAddr());
            ipVertexRow.setField(1, generateMd5(ipVertex.getIpAddr()));
            ipVertexRow.setField(2, ipVertex.getVersion());
            ipVertexRow.setField(3, ipVertex.getCity());
            ipVertexRow.setField(4, ipVertex.getCountry());
            ipVertexRow.setField(5, ipVertex.getRemark());

            // 直接输出到IP顶点的侧输出流
            ctx.output(NebulaGraphOutputTag.Vertex.IP, ipVertexRow);


        }
    }

    /**
     * 处理MAC顶点并直接分流
     *
     * @param vertexMap 顶点数据映射
     * @param ctx 上下文
     * @param out 收集器
     */
    private void processMacVertex(Map<String, Object> vertexMap, Context ctx, Collector<Row> out) {
        // 处理客户端MAC顶点
        MacVertex clientMacVertex = (MacVertex) vertexMap.get("clientMacTag");
        if (clientMacVertex != null) {
            Row macVertexRow = new Row(2);
            macVertexRow.setField(0, clientMacVertex.getMac());
            macVertexRow.setField(1, clientMacVertex.getVlanInfo());

            // 直接输出到设备顶点的侧输出流
            ctx.output(NebulaGraphOutputTag.Vertex.DEVICE, macVertexRow);


        }

        // 处理服务端MAC顶点
        MacVertex serverMacVertex = (MacVertex) vertexMap.get("serverMacTag");
        if (serverMacVertex != null) {
            Row macVertexRow = new Row(2);
            macVertexRow.setField(0, serverMacVertex.getMac());
            macVertexRow.setField(1, serverMacVertex.getVlanInfo());

            // 直接输出到设备顶点的侧输出流
            ctx.output(NebulaGraphOutputTag.Vertex.DEVICE, macVertexRow);


        }

        // 处理通用MAC顶点
        MacVertex macVertex = (MacVertex) vertexMap.get("macVertex");
        if (macVertex != null) {
            Row macVertexRow = new Row(2);
            macVertexRow.setField(0, macVertex.getMac());
            macVertexRow.setField(1, macVertex.getVlanInfo());

            // 直接输出到设备顶点的侧输出流
            ctx.output(NebulaGraphOutputTag.Vertex.DEVICE, macVertexRow);


        }
    }

    /**
     * 生成MD5哈希值
     *
     * @param input 输入字符串
     * @return MD5哈希值
     */
    protected String generateMd5(String input) {
        // 简单实现，实际应使用MD5算法
        return input != null ? String.valueOf(input.hashCode()) : "";
    }
}
