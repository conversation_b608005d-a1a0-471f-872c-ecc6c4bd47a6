package com.geeksec.nta.graphbuilder.model.vertex;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * IP顶点
 * 表示IP地址信息
 *
 * <AUTHOR> Team
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class IpVertex extends BaseVertex {
    /**
     * IP地址
     */
    private String ipAddr;

    /**
     * IP版本
     */
    private String version;

    /**
     * 城市
     */
    private String city;

    /**
     * 国家
     */
    private String country;

    /**
     * 威胁分数
     */
    private Integer threatScore;

    /**
     * 信任分数
     */
    private Integer trustScore;

    /**
     * 备注
     */
    private String remark;

    @Override
    public String getVertexId() {
        return ipAddr;
    }
    
    public String getIpAddr() {
        return ipAddr;
    }
    
    public String getVersion() {
        return version;
    }
    
    public String getCity() {
        return city;
    }
    
    public String getCountry() {
        return country;
    }
    
    public Integer getThreatScore() {
        return threatScore;
    }
    
    public Integer getTrustScore() {
        return trustScore;
    }
    
    public String getRemark() {
        return remark;
    }

    /**
     * 获取顶点标签
     *
     * @return 顶点标签枚举
     */
    @Override
    public VertexTag getVertexTag() {
        return VertexTag.IP;
    }
}
