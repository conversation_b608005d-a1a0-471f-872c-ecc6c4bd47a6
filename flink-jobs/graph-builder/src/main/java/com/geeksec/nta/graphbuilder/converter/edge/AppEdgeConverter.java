package com.geeksec.nta.graphbuilder.converter.edge;

import com.geeksec.nta.graphbuilder.io.sink.tag.NebulaGraphOutputTag;
import com.geeksec.nta.graphbuilder.model.edge.AppDeploysOnServerEdge;
import com.geeksec.nta.graphbuilder.model.edge.BaseEdge;
import com.geeksec.nta.graphbuilder.model.edge.ClientAccessesAppEdge;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

import java.util.List;
import java.util.Map;

/**
 * 应用边转换器
 * 处理应用相关的边数据，将其转换为Flink Row格式
 *
 * <AUTHOR>
 */
@Slf4j
public class AppEdgeConverter extends ProcessFunction<Map<String, Object>, Row> {

    private static final long serialVersionUID = 1L;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
    }

    @Override
    public void processElement(Map<String, Object> edgeMap, Context ctx, Collector<Row> out) throws Exception {
        // 处理客户端访问应用边
        processClientAccessesAppEdges(edgeMap, ctx, out);

        // 处理应用部署在服务器边
        processAppDeploysOnServerEdges(edgeMap, ctx, out);

        // 处理UA有应用边
        processUaHasAppEdges(edgeMap, ctx, out);

        // 处理证书被应用使用边
        processCertUsedByAppEdges(edgeMap, ctx, out);
    }

    /**
     * 处理客户端访问应用边
     *
     * @param edgeMap 边数据映射
     * @param ctx 上下文，用于访问侧输出流
     * @param out 收集器
     */
    private void processClientAccessesAppEdges(Map<String, Object> edgeMap, Context ctx, Collector<Row> out) {
        // 处理客户端访问应用边
        ClientAccessesAppEdge clientAppEdge = (ClientAccessesAppEdge) edgeMap.get("clientAppEdge");
        if (!ObjectUtils.isEmpty(clientAppEdge)) {
            Row clientAppEdgeRow = new Row(4);
            clientAppEdgeRow.setField(0, clientAppEdge.getSrcId());
            clientAppEdgeRow.setField(1, clientAppEdge.getDstId());
            clientAppEdgeRow.setField(2, 0);
            clientAppEdgeRow.setField(3, clientAppEdge.getDPort());

            // 直接输出到应用连接边的侧输出流
            ctx.output(NebulaGraphOutputTag.Edge.APPLICATION_CONNECT, clientAppEdgeRow);
        }
    }

    /**
     * 处理应用部署在服务器边
     *
     * @param edgeMap 边数据映射
     * @param ctx 上下文，用于访问侧输出流
     * @param out 收集器
     */
    private void processAppDeploysOnServerEdges(Map<String, Object> edgeMap, Context ctx, Collector<Row> out) {
        // 处理应用部署在服务器边
        AppDeploysOnServerEdge serverAppEdge = (AppDeploysOnServerEdge) edgeMap.get("serverAppEdge");
        if (!ObjectUtils.isEmpty(serverAppEdge)) {
            Row serverAppEdgeRow = new Row(4);
            serverAppEdgeRow.setField(0, serverAppEdge.getSrcId());
            serverAppEdgeRow.setField(1, serverAppEdge.getDstId());
            serverAppEdgeRow.setField(2, 0);
            serverAppEdgeRow.setField(3, serverAppEdge.getDPort());

            // 直接输出到应用部署在服务器边的侧输出流
            ctx.output(NebulaGraphOutputTag.Edge.APPLICATION_CONNECT, serverAppEdgeRow);
        }
    }

    /**
     * 处理UA有应用边
     *
     * @param edgeMap 边数据映射
     * @param ctx 上下文，用于访问侧输出流
     * @param out 收集器
     */
    private void processUaHasAppEdges(Map<String, Object> edgeMap, Context ctx, Collector<Row> out) {
        // 处理UA有应用边列表
        @SuppressWarnings("unchecked")
        List<BaseEdge> uaHasAppEdgeList = (List<BaseEdge>) edgeMap.get("uaHasAppEdgeList");
        if (!CollectionUtils.isEmpty(uaHasAppEdgeList)) {
            for (BaseEdge edge : uaHasAppEdgeList) {
                Row uaHasAppRow = new Row(3);
                uaHasAppRow.setField(0, edge.getSrcId());
                uaHasAppRow.setField(1, edge.getDstId());
                uaHasAppRow.setField(2, 0);

                // 直接输出到设备连接边的侧输出流
                ctx.output(NebulaGraphOutputTag.Edge.DEVICE_CONNECT, uaHasAppRow);
            }
        }
    }

    /**
     * 处理证书被应用使用边
     *
     * @param edgeMap 边数据映射
     * @param ctx 上下文，用于访问侧输出流
     * @param out 收集器
     */
    private void processCertUsedByAppEdges(Map<String, Object> edgeMap, Context ctx, Collector<Row> out) {
        // 处理证书被应用使用边列表
        @SuppressWarnings("unchecked")
        List<BaseEdge> certUsedByAppEdgeList = (List<BaseEdge>) edgeMap.get("certUsedByAppEdgeList");
        if (!CollectionUtils.isEmpty(certUsedByAppEdgeList)) {
            for (BaseEdge edge : certUsedByAppEdgeList) {
                Row certUsedByAppRow = new Row(3);
                certUsedByAppRow.setField(0, edge.getSrcId());
                certUsedByAppRow.setField(1, edge.getDstId());
                certUsedByAppRow.setField(2, 0);

                // 直接输出到证书使用边的侧输出流
                ctx.output(NebulaGraphOutputTag.Edge.CERTIFICATE_USE, certUsedByAppRow);
            }
        }
    }
}
