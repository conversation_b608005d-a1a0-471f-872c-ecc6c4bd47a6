package com.geeksec.nta.graphbuilder.converter.vertex;

import com.geeksec.nta.graphbuilder.io.sink.tag.NebulaGraphOutputTag;
import com.geeksec.nta.graphbuilder.model.vertex.DeviceVertex;
import com.geeksec.nta.graphbuilder.model.vertex.OsVertex;
import com.geeksec.nta.graphbuilder.model.vertex.UaVertex;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

import java.util.List;
import java.util.Map;

/**
 * HTTP顶点转换器
 * 处理HTTP相关的顶点数据（UA、设备、OS），将其转换为Flink Row格式并直接输出到侧输出流
 *
 * <AUTHOR>
 */
@Slf4j
public class HttpVertexConverter extends ProcessFunction<Map<String, Object>, Row> {

    private static final long serialVersionUID = 1L;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
    }

    @Override
    public void processElement(Map<String, Object> vertexMap, Context ctx, Collector<Row> out) throws Exception {
        // 处理UA顶点
        processUaVertices(vertexMap, ctx, out);

        // 处理设备顶点
        processDeviceVertices(vertexMap, ctx, out);

        // 处理操作系统顶点
        processOsVertices(vertexMap, ctx, out);
    }

    /**
     * 处理UA顶点
     *
     * @param vertexMap 顶点数据映射
     * @param ctx 上下文，用于访问侧输出流
     * @param out 收集器
     */
    private void processUaVertices(Map<String, Object> vertexMap, Context ctx, Collector<Row> out) {
        // 处理UA顶点列表
        List<UaVertex> uaVertexList = (List<UaVertex>) vertexMap.get("uaVertexList");
        if (!CollectionUtils.isEmpty(uaVertexList)) {
            for (UaVertex uaVertex : uaVertexList) {
                Row uaVertexRow = new Row(2);
                uaVertexRow.setField(0, uaVertex.getUaId());
                uaVertexRow.setField(1, uaVertex.getUaStr());

                // 直接输出到UA顶点的侧输出流
                ctx.output(NebulaGraphOutputTag.Vertex.URL, uaVertexRow);
            }
        }
    }

    /**
     * 处理设备顶点
     *
     * @param vertexMap 顶点数据映射
     * @param ctx 上下文，用于访问侧输出流
     * @param out 收集器
     */
    private void processDeviceVertices(Map<String, Object> vertexMap, Context ctx, Collector<Row> out) {
        // 处理设备顶点列表
        List<DeviceVertex> deviceVertexList = (List<DeviceVertex>) vertexMap.get("deviceVertexList");
        if (!CollectionUtils.isEmpty(deviceVertexList)) {
            for (DeviceVertex deviceVertex : deviceVertexList) {
                Row deviceVertexRow = new Row(1);
                deviceVertexRow.setField(0, deviceVertex.getDeviceName());

                // 直接输出到设备顶点的侧输出流
                ctx.output(NebulaGraphOutputTag.Vertex.DEVICE, deviceVertexRow);
            }
        }
    }

    /**
     * 处理操作系统顶点
     *
     * @param vertexMap 顶点数据映射
     * @param ctx 上下文，用于访问侧输出流
     * @param out 收集器
     */
    private void processOsVertices(Map<String, Object> vertexMap, Context ctx, Collector<Row> out) {
        // 处理操作系统顶点列表
        List<OsVertex> osVertexList = (List<OsVertex>) vertexMap.get("osVertexList");
        if (!CollectionUtils.isEmpty(osVertexList)) {
            for (OsVertex osVertex : osVertexList) {
                Row osVertexRow = new Row(2);
                osVertexRow.setField(0, osVertex.getOsName());
                osVertexRow.setField(1, osVertex.getOsVersion());

                // 直接输出到操作系统顶点的侧输出流 (使用设备顶点标签，因为没有专门的OS标签)
                ctx.output(NebulaGraphOutputTag.Vertex.DEVICE, osVertexRow);
            }
        }
    }
}
