package com.geeksec.nta.graphbuilder.utils.crypto;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 哈希工具类
 * 提供各种哈希算法的实现
 *
 * <AUTHOR> Team
 */
@Slf4j
public final class HashUtils {
    /**
     * SHA-1算法名称
     */
    private static final String SHA1_ALGORITHM = "SHA-1";

    /**
     * SHA-256算法名称
     */
    private static final String SHA256_ALGORITHM = "SHA-256";

    /**
     * SHA-512算法名称
     */
    private static final String SHA512_ALGORITHM = "SHA-512";

    /**
     * 私有构造函数，防止实例化
     */
    private HashUtils() {
        throw new AssertionError("工具类不需要实例化");
    }

    /**
     * 计算字符串的MD5哈希值
     *
     * @param data 数据
     * @return MD5哈希值（32位十六进制字符串），如果输入为null则返回null
     */
    public static String md5(String data) {
        if (StringUtils.isEmpty(data)) {
            return null;
        }
        return DigestUtils.md5Hex(data);
    }

    /**
     * 计算字节数组的MD5哈希值
     *
     * @param data 要计算哈希值的字节数组
     * @return MD5哈希值（32位十六进制字符串），如果输入为null则返回null
     */
    public static String md5(byte[] data) {
        if (data == null || data.length == 0) {
            return null;
        }
        return DigestUtils.md5Hex(data);
    }

    /**
     * 计算文件的MD5哈希值
     *
     * @param file 要计算哈希值的文件
     * @return MD5哈希值（32位十六进制字符串），如果计算失败则返回null
     */
    public static String md5(File file) {
        if (file == null || !file.exists() || !file.isFile()) {
            return null;
        }

        try (FileInputStream fis = new FileInputStream(file)) {
            return org.apache.commons.codec.digest.DigestUtils.md5Hex(fis);
        } catch (IOException e) {
            log.error("计算文件MD5失败: {}", file.getAbsolutePath(), e);
            return null;
        }
    }

    /**
     * 计算Map的MD5哈希值
     * 将Map中的键值对按照特定格式拼接后计算MD5
     *
     * @param map 要计算哈希值的Map
     * @return MD5哈希值，如果Map为空则返回空字符串
     * @throws UnsupportedEncodingException 如果编码不支持
     */
    public static String md5(LinkedHashMap<String, String> map) throws UnsupportedEncodingException {
        if (map == null || map.isEmpty()) {
            return "";
        }

        StringBuilder result = new StringBuilder();
        for (Map.Entry<String, String> entry : map.entrySet()) {
            result.append("/").append(entry.getKey()).append("=").append(entry.getValue());
        }

        return md5(result.toString());
    }

    /**
     * 验证字符串与MD5哈希值是否匹配
     *
     * @param data 数据
     * @param md5Hash MD5哈希值
     * @return 如果匹配则返回true，否则返回false
     */
    public static boolean verifyMd5(String data, String md5Hash) {
        if (StringUtils.isEmpty(data) || StringUtils.isEmpty(md5Hash)) {
            return false;
        }
        return md5(data).equals(md5Hash);
    }

    /**
     * 计算SHA-1哈希值
     *
     * @param data 数据
     * @return SHA-1哈希值（40位十六进制字符串）
     */
    public static String sha1(String data) {
        return hash(data, SHA1_ALGORITHM);
    }

    /**
     * 计算SHA-256哈希值
     *
     * @param data 数据
     * @return SHA-256哈希值（64位十六进制字符串）
     */
    public static String sha256(String data) {
        return hash(data, SHA256_ALGORITHM);
    }

    /**
     * 计算SHA-512哈希值
     *
     * @param data 数据
     * @return SHA-512哈希值（128位十六进制字符串）
     */
    public static String sha512(String data) {
        return hash(data, SHA512_ALGORITHM);
    }

    /**
     * 使用指定算法计算哈希值
     *
     * @param data 要计算哈希值的字符串
     * @param algorithm 哈希算法名称（如MD5、SHA-1、SHA-256等）
     * @return 十六进制表示的哈希值，如果计算失败则返回null
     */
    public static String hash(String data, String algorithm) {
        if (StringUtils.isEmpty(data)) {
            return null;
        }
        return hash(data.getBytes(StandardCharsets.UTF_8), algorithm);
    }

    /**
     * 使用指定算法计算哈希值
     *
     * @param data 要计算哈希值的字节数组
     * @param algorithm 哈希算法名称（如MD5、SHA-1、SHA-256等）
     * @return 十六进制表示的哈希值，如果计算失败则返回null
     */
    public static String hash(byte[] data, String algorithm) {
        if (data == null || data.length == 0 || algorithm == null) {
            return null;
        }

        try {
            MessageDigest digest = MessageDigest.getInstance(algorithm);
            byte[] hashBytes = digest.digest(data);
            return Hex.encodeHexString(hashBytes);
        } catch (NoSuchAlgorithmException e) {
            log.error("计算哈希值失败: {}", algorithm, e);
            return null;
        }
    }
}
