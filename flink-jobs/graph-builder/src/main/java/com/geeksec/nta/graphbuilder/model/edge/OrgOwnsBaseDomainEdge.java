package com.geeksec.nta.graphbuilder.model.edge;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 组织拥有可注册域名边
 * 表示组织与可注册域名之间的所有权关系
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OrgOwnsRegistrableDomainEdge extends BaseEdge {
    
    /**
     * 获取边类型
     *
     * @return 边类型常量
     */
    @Override
    public EdgeType getEdgeType() {
        return EdgeType.ORG_OWNS_REGISTRABLE_DOMAIN;
    }
}
