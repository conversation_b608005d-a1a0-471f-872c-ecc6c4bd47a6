package com.geeksec.nta.graphbuilder.model.edge;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 域名派生自可注册域名边
 * 表示域名与其可注册域名之间的派生关系
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DomainDerivesFromRegistrableDomainEdge extends BaseEdge {

    /**
     * 是否为SNI域名
     * 标识该域名是否为TLS会话中的SNI域名
     */
    private Boolean isSni;

    /**
     * 获取边类型
     *
     * @return 边类型常量
     */
    @Override
    public EdgeType getEdgeType() {
        return EdgeType.DOMAIN_DERIVES_FROM_REGISTRABLE_DOMAIN;
    }
}
