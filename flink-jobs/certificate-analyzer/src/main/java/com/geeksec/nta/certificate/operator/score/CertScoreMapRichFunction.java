package com.geeksec.nta.certificate.operator.score;

import com.geeksec.analysisFunction.analysisEntity.cert.X509Cert;
import com.geeksec.utils.FileUtil;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.configuration.Configuration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2022/11/8
 */

public class CertScoreMapRichFunction extends RichMapFunction<X509Cert, X509Cert> {

    protected static final Logger LOG = LoggerFactory.getLogger(CertScoreMapRichFunction.class);

    //定义威胁分数名单，label.csv的索引+分数
    private HashMap<String, Integer> BLACK_SCORE_MAP = new HashMap<>();

    //定义正常分数名单，label.csv的索引+分数
    private HashMap<String, Integer> WHITE_SCORE_MAP = new HashMap<>();

    //标签告警名单
    private static HashMap<String, String> LABEL_ALARM_LIST = new HashMap<>();
    public static HashMap<String, HashMap<String, String>> LABEL_INFO_LIST = new HashMap<>();



    @Override
    public void open(Configuration parameters) throws Exception {
        LOG.info("Initialization of global parameters starts.");
        Map<String, String> globalParam = getRuntimeContext().getExecutionConfig().getGlobalJobParameters().toMap();//获取全局参数

        // 依赖文件初始化
        // 加载配置文件
        loadData();
        // 白名单标签列表加载 & 黑名单标签列表加载，加载白名单黑名单分数列表
        HashMap<String, HashMap<String, String>> tagList = FileUtil.loadTagList("/label.csv");
        Set<String> tagKeys = tagList.keySet();//得到所有的标签去重后的信息
        for (String tagkey : tagKeys) {
            HashMap<String, String> element = tagList.get(tagkey);
            BLACK_SCORE_MAP.put(element.get("Tag_Remark"), Integer.valueOf(element.get("Black_List")));
            WHITE_SCORE_MAP.put(element.get("Tag_Remark"), Integer.valueOf(element.get("White_List")));
        }
    }
    private void loadData() throws IOException {
        try {
            LABEL_ALARM_LIST = FileUtil.loadLabelAlarmList("/Label_Alarm_List.csv");
            LABEL_INFO_LIST = FileUtil.loadLabelInfoList("/label.csv");
        } catch (Exception e) {
            throw new IOException("file load fail ==>", e);
        }
    }


    @Override
    public X509Cert map(X509Cert x509Cert) throws Exception {
        ArrayList<String> tags = new ArrayList<>(x509Cert.getTagList());
        // 校验标签，获取所有的标签，并转化为标签ID，并设置证书的黑白名单值
        x509Cert.setTagList(tagsToTagId(x509Cert.getTagList()));
        x509Cert.setBlackList(getScore(tags, BLACK_SCORE_MAP));
        x509Cert.setWhiteList(getScore(tags, WHITE_SCORE_MAP));
        x509Cert.setImportTime(System.currentTimeMillis()/1000);
        return x509Cert;
    }

    /**
     * Tags名称转TagID，入ES库时只存ID
     *
     * @return 当前证书的TagID
     */
    public static List<String> tagsToTagId(List<String> tags) {
        ArrayList<String> tagIdList = new ArrayList<>();
        ArrayList<String> tags_new = new ArrayList<>();
        for (String tag:tags){
            tags_new.add(tag.replaceAll(" ", "").toLowerCase());
        }
        if (tags_new.size() == 0) {
            return tagIdList;
        }

        for (String tagId : LABEL_ALARM_LIST.keySet()) {
            String tagIdTmp = LABEL_ALARM_LIST.get(tagId).replaceAll(" ", "").toLowerCase();
            if (tags_new.contains(tagIdTmp)) {
                tagIdList.add(tagId);
                tags_new.remove(tagIdTmp);
            }
        }
        return tagIdList;
    }

    /**
     * Util function
     *
     * @param tags
     * @param map
     * @return
     */
    private int getScore(ArrayList<String> tags, HashMap<String, Integer> map) {
        int score = 0;

        for (String tag : tags) {
            Integer tagNumber = map.getOrDefault(tag, 0);//根据tag的值反查map中的标签对应的黑白名单对应的值，如果查不到就返回0
            score += tagNumber;
        }

        return Math.min(score, 100);//超过100取100
    }

}

