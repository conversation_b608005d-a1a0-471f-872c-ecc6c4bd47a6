package com.geeksec.nta.certificate.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Date 2023/7/11
 */

public class LevenshteinDistance {
    protected static final Logger LOG = LoggerFactory.getLogger(LevenshteinDistance.class);
    public static int SafeLevenshteinDistance = 2;
    public static int calculateMinDistance(byte[] word1, byte[] word2) {
        int m = word1.length;
        int n = word2.length;
        if( m*n == 0 ) {
            return Math.max(m, n);
        }

        int[][] lev = new int[m+1][n+1];

        // 字符串word1从空串 变为 字符串word2 前j个字符 的莱文斯坦距离
        for (int j=0; j<n+1; j++) {
            lev[0][j] = j;
        }
        // 字符串word1从前i个字符 变为 空串 的莱文斯坦距离
        for (int i=0; i<m+1; i++) {
            lev[i][0] = i;
        }

        for (int i=1; i<m+1; i++) {
            for (int j=1; j<n+1; j++) {
                // 在 字符串A的前i个字符 与 字符串B的前j-1个字符 完全相同的基础上, 进行一次插入操作
                int countByInsert = lev[i][j-1] + 1;
                // 在 字符串A的前i-1个字符 与 字符串B的前j个字符 完全相同的基础上, 进行一次删除操作
                int countByDel = lev[i-1][j] + 1;
                // 在 字符串A的前i-1个字符 与 字符串B的前j-1个字符 完全相同的基础上, 进行一次替换操作
                int countByReplace =  word1[i-1]==word2[j-1] ? lev[i-1][j-1] : lev[i-1][j-1]+1;
                // 计算 字符串A的前i个字符 与 字符串B的前j个字符 的莱文斯坦距离
                lev[i][j] = min( countByInsert, countByDel, countByReplace );
            }
        }

        return lev[m][n];
    }

    /**
     * 计算三个数中的最小值
     * @param a
     * @param b
     * @param c
     * @return
     */
    private static int min(int a, int b, int c) {
        int temp = Math.min(a,b);
        int res= Math.min(temp, c);
        return res;
    }

    public static boolean check_distance(byte[] a,byte[] b,String errorType) {
        switch (errorType){
            case "ByteLoss":
            case "ByteRedundancy":
                SafeLevenshteinDistance = 20;
                break;
            case "ByteFlip":
                SafeLevenshteinDistance = 2;
                break;
            default:
                break;
        }
        int levenshteinDistance = LevenshteinDistance.calculateMinDistance(a, b);
        if (levenshteinDistance>SafeLevenshteinDistance){
            LOG.info("LevenshteinDistance距离为：{},安全距离为：{}",levenshteinDistance,SafeLevenshteinDistance);
            return false;
        }else {
            return true;
        }
    }
}
