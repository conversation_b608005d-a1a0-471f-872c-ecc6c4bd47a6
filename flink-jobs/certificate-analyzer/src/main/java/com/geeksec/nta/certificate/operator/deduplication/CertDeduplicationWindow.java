package com.geeksec.nta.certificate.operator.deduplication;

import com.geeksec.analysisFunction.analysisEntity.cert.X509Cert;
import com.geeksec.flinkTool.labelKeySelector.CertKeySelector;
import com.geeksec.utils.EsUtils;
import org.apache.commons.pool.impl.GenericObjectPool;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.functions.windowing.ProcessWindowFunction;
import org.apache.flink.streaming.api.windowing.assigners.TumblingProcessingTimeWindows;
import org.apache.flink.streaming.api.windowing.time.Time;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.util.Collector;
import org.elasticsearch.client.RestHighLevelClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

import static com.geeksec.analysisFunction.deduplication.RedisDeduplicationFlatMapFunction.EsPool;
import static com.geeksec.flinkTool.sideOutputTag.DeduplicationOutPutTag.Dedup_cert;
import static com.geeksec.flinkTool.sideOutputTag.DeduplicationOutPutTag.Not_Dedup_cert;
import static com.geeksec.nta.pipeline.CertificateAnalysisPipeline.PA16;
import static com.geeksec.nta.pipeline.CertificateAnalysisPipeline.PA4;
import static com.geeksec.utils.EsUtils.MultiMatchQuery;
import static com.geeksec.utils.EsUtils.logger;

/**
 * <AUTHOR>
 * @Date 2023/2/14
 */

public class CertDeduplicationWindow {
    protected static final Logger LOG = LoggerFactory.getLogger(CertDeduplicationWindow.class);
    public static SingleOutputStreamOperator<X509Cert> certDeduplicationWindow(DataStream<X509Cert> CertStream){
        SingleOutputStreamOperator<X509Cert> CertStreamWithTime = CertStream
                .assignTimestampsAndWatermarks(WatermarkStrategy.forMonotonousTimestamps()).name("系统时间戳水印").setParallelism(PA4)
                .keyBy(new CertKeySelector(4))
                .window(TumblingProcessingTimeWindows.of(Time.milliseconds(500)))
                .process(new ProcessWindowFunction<X509Cert, X509Cert, Integer, TimeWindow>() {
                    @Override
                    public void process(Integer integer, ProcessWindowFunction<X509Cert, X509Cert, Integer, TimeWindow>.Context context, Iterable<X509Cert> iterable, Collector<X509Cert> collector) throws Exception {
                        List<String> UserAsnSha1List = new ArrayList<>();
                        for (X509Cert x509Cert:iterable){
                            String certType = x509Cert.getCertSource();
                            if ("User".equals(certType)) {
                                UserAsnSha1List.add(x509Cert.getASN1SHA1());
                            } else {
                                logger.error("出现了预期外的证书来源类型");
                            }
                        }
                        RestHighLevelClient EsClient = null;
                        try{
                            EsClient = EsUtils.getClient(EsPool);
                            //此处匹配所有的证书index，合并去重
                            HashMap<String, Map> UserResult = new HashMap<>();
                            if (UserAsnSha1List.size()!=0){
                                UserResult =  MultiMatchQuery("cert_user", "ASN1SHA1", UserAsnSha1List, null, null,EsClient);
                            }
                            for (X509Cert x509Cert:iterable){
                                String certType = x509Cert.getCertSource();
                                if ("User".equals(certType)) {
                                    if (!UserResult.containsKey(x509Cert.getASN1SHA1())) {
                                        x509Cert.setIsDedupCert(false);
                                        context.output(Not_Dedup_cert, x509Cert);
                                    } else {
                                        //这一步是从User重复证书中提取所需要的数据，后续可以再加
                                        getDedupCertInfo(x509Cert, UserResult);
                                        getNewUserIDList(x509Cert, UserResult);
                                        context.output(Dedup_cert, x509Cert);
                                    }
                                } else {
                                    logger.error("出现了预期外的证书来源类型");
                                }
                            }
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }finally {
                            if(EsClient != null){
                                EsUtils.returnClient(EsClient, EsPool);
                            }
                        }
                    }
                }).name("处理时间窗口函数去重所有来源证书").setParallelism(PA16);

        return CertStreamWithTime;
    }

    public static void getDedupCertInfo(X509Cert x509Cert, Map<String,Map> Result){
        String SHA1 = x509Cert.getASN1SHA1();
        Map<String,Object> result_cert = Result.get(SHA1);
        x509Cert.setIsDedupCert(true);
        x509Cert.setSubject(new LinkedHashMap<>((HashMap)result_cert.get("Subject")));
        x509Cert.setBlackList((Integer) result_cert.get("BlackList"));
        x509Cert.setWhiteList((Integer) result_cert.get("WhiteList"));
        x509Cert.setTagList((List<String>) result_cert.get("Labels"));
    }

    public static void updateESUserList(GenericObjectPool<RestHighLevelClient> EsPool, String index, List<String> UserIDList, String SHA1) throws Exception {
        RestHighLevelClient EsClient = EsUtils.getClient(EsPool);
        try{
            EsUtils.updateAndSearchDocuments(index,SHA1,UserIDList,EsClient);
        }catch (Exception e){
            LOG.error("获取EsClient失败，error--->{}",e);
        }finally {
            if(EsClient != null){
                EsUtils.returnClient(EsClient,EsPool);
            }
        }
    }

    public static void getNewUserIDList(X509Cert x509Cert,Map<String,Map> Result) throws Exception {
        String SHA1 = x509Cert.getASN1SHA1();
        Map<String,Object> result_cert = Result.get(SHA1);
        List<String> ESUserIDList = (List<String>) result_cert.get("UserIDList");
        List<String> NowUserIDList = x509Cert.getUserIDList();
        Set<String> NewUserIDSet = new HashSet<>();
        if (ESUserIDList!=null){
            NewUserIDSet.addAll(ESUserIDList);
        }
        NewUserIDSet.addAll(NowUserIDList);
        List<String> NewUserIDList = new ArrayList<>(NewUserIDSet);
        if (!NewUserIDList.equals(ESUserIDList)){
            updateESUserList(EsPool,"cert_user",NewUserIDList, x509Cert.getASN1SHA1());
            LOG.info("向SHA1为——{}——的证书更新用户ID",SHA1);
        }else {
            LOG.info("无需更新用户ID");
        }
    }
}

