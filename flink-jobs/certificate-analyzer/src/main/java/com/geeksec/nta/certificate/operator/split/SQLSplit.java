package com.geeksec.nta.certificate.operator.split;

import com.geeksec.analysisFunction.analysisEntity.cert.X509Cert;
import com.geeksec.utils.MysqlUtils;
import com.geeksec.utils.RedisUtils;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/5/6
 */

public class SQLSplit extends ProcessFunction<X509Cert, Row> {
    private static final Logger LOG = LoggerFactory.getLogger(SQLSplit.class);
    public static transient JedisPool jedisPool = null;

    public static final OutputTag<Row> upload_file_cert = new OutputTag<>("upload_file_cert", TypeInformation.of(Row.class));
    public static final OutputTag<Row> not_upload_file_cert = new OutputTag<>("not_upload_file_cert", TypeInformation.of(Row.class));

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        // redis池 初始化.
        jedisPool = RedisUtils.initJedisPool();
        LOG.info("生成jedisPool成功! {}", jedisPool.getNumIdle(),jedisPool.hashCode());
    }

    @Override
    public void close() throws Exception {
        super.close();
        if (jedisPool != null) {
            jedisPool.close();
        }
    }

    @Override
    public void processElement(X509Cert x509Cert, ProcessFunction<X509Cert, Row>.Context context, Collector<Row> collector) throws Exception {
        // 先判断是否是用户导入证书
        String sha1 = x509Cert.getASN1SHA1();
        List<String> task_batch_info = new ArrayList<>();

        String source = x509Cert.getCertSource();
        if(!"User".equals(source)){
            LOG.info("不是从cert_user导入的证书");
            Row not_upload_file_cert_row = new Row(2);
            not_upload_file_cert_row.setField(0,task_batch_info);
            not_upload_file_cert_row.setField(1,x509Cert);
            context.output(not_upload_file_cert,not_upload_file_cert_row);
        }else{
            //获取该证书sha1关联的所有task,batch信息
            Jedis jedis = null;
            try {
                jedis = RedisUtils.getJedis(jedisPool);
                task_batch_info = RedisUtils.getRedisTaskBatchInfo(sha1, jedis);
            } catch (Exception e) {
                LOG.error("redis查询失败，error--->{},SHA1 is--->{}", e, sha1);
            } finally {
                if (jedis != null) {
                    jedis.close();
                }
            }
            if (task_batch_info.size()==0) {
                LOG.info("不是用户导入的任何批次的证书");
                Row not_upload_file_cert_row = new Row(2);
                not_upload_file_cert_row.setField(0,task_batch_info);
                not_upload_file_cert_row.setField(1,x509Cert);
                context.output(not_upload_file_cert,not_upload_file_cert_row);
            }else {
                //判断是否终止导入,过滤task_batch_info列表，并对终止导入的证书进行操作
                String SHA1 = x509Cert.getASN1SHA1();
                List<String> task_batch_info_not_stop = new ArrayList<>();
                for(String task_batch:task_batch_info){
                    boolean stop_import = MysqlUtils.mysql_stop_import(task_batch,SHA1);
                    if (!stop_import){
                        task_batch_info_not_stop.add(task_batch);
                    }else {
                        LOG.info("是用户导入证书,但是终止导入的批次为——{}——",task_batch);
                    }
                }
                LOG.info("是用户导入证书,且未终止导入的批次为——{}——",task_batch_info_not_stop);
                Row upload_file_cert_row = new Row(2);
                upload_file_cert_row.setField(0,task_batch_info_not_stop);
                upload_file_cert_row.setField(1,x509Cert);
                context.output(upload_file_cert,upload_file_cert_row);
            }
        }
    }

}
