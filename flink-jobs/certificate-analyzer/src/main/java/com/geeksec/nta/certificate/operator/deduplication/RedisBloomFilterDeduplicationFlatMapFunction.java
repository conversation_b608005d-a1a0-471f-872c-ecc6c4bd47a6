package com.geeksec.nta.certificate.operator.deduplication;

import com.geeksec.analysisFunction.analysisEntity.cert.X509Cert;
import com.geeksec.utils.RedisBloomFilterUtils;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

/**
 * 使用Redis布隆过滤器进行证书去重的FlatMapFunction
 * 
 * <AUTHOR>
 * @date 2024/10/15
 */
public class RedisBloomFilterDeduplicationFlatMapFunction extends RichFlatMapFunction<X509Cert, X509Cert> {
    protected static final Logger LOG = LoggerFactory.getLogger(RedisBloomFilterDeduplicationFlatMapFunction.class);
    
    private transient JedisPool jedisPool;

    @Override
    public void open(Configuration parameters) throws Exception {
        // 初始化Redis连接池
        jedisPool = RedisBloomFilterUtils.initJedisPool();
        LOG.info("生成jedisPool成功! 活跃连接数——{}——，空闲数——{}——，等待数——{}——", 
                jedisPool.getNumActive(), jedisPool.getNumIdle(), jedisPool.getNumWaiters());
        
        // 初始化布隆过滤器
        try (Jedis jedis = RedisBloomFilterUtils.getJedis(jedisPool)) {
            RedisBloomFilterUtils.initBloomFilter(jedis);
        } catch (Exception e) {
            LOG.error("初始化布隆过滤器失败", e);
        }
        
        super.open(parameters);
    }

    @Override
    public void close() throws Exception {
        if (jedisPool != null) {
            jedisPool.close();
        }
        super.close();
    }

    @Override
    public void flatMap(X509Cert x509Cert, Collector<X509Cert> collector) {
        String sourceType = x509Cert.getCertSource();
        
        // 对于User类型的证书，直接通过（与原有逻辑保持一致）
        if ("User".equals(sourceType)) {
            collector.collect(x509Cert);
            return;
        }
        
        // 对于其他类型的证书，使用布隆过滤器进行去重
        Jedis jedis = null;
        try {
            jedis = RedisBloomFilterUtils.getJedis(jedisPool);
            
            // 使用布隆过滤器和精确键进行去重
            boolean isDuplicate = RedisBloomFilterUtils.checkCertDeduplication(x509Cert, jedis);
            
            if (!isDuplicate) {
                // 不是重复的，继续处理
                collector.collect(x509Cert);
            } else {
                // 是重复的，记录日志
                LOG.debug("检测到重复证书: {}", x509Cert.getASN1SHA1());
            }
        } catch (Exception e) {
            LOG.error("布隆过滤器去重失败，error--->{}, SHA1 is--->{}",
                    e.getMessage(), x509Cert.getASN1SHA1());
            // 出错时，为了安全起见，继续处理证书
            collector.collect(x509Cert);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
    }
}
