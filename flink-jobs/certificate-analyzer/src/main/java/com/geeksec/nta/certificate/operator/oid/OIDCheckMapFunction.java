package com.geeksec.nta.certificate.operator.oid;

import com.geeksec.analysisFunction.analysisEntity.UncommonOID;
import com.geeksec.analysisFunction.analysisEntity.cert.X509Cert;
import com.geeksec.utils.FileUtil;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.configuration.Configuration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

/**
 * <AUTHOR>
 * @Date 2023/12/18
 * @Modified hufengkai
 * @Date 2024/10/15
 */

public class OIDCheckMapFunction extends RichMapFunction<X509Cert, X509Cert> {

    private static final Logger LOG = LoggerFactory.getLogger(OIDCheckMapFunction.class);

    private static Properties properties = FileUtil.getProperties("/config.properties");

    // OID查询服务配置
    // 这里需要实现一个OID查询服务来替代LMDB

    @Override
    public void open(Configuration parameters) throws Exception {
        // 初始化OID查询服务
        LOG.info("OID查询服务初始化");
    }

    @Override
    public void close() throws Exception {
        super.close();
    }

    @Override
    public X509Cert map(X509Cert x509Cert) throws Exception {
        // 不常见oid的识别
        List<UncommonOID> uncommonOids = x509Cert.getUncommonOIDs();
        List<UncommonOID> resultUncommonOids = new ArrayList<>();
        for (UncommonOID uncommonOid : uncommonOids) {
            String oid = uncommonOid.getOID();
            try {
                // 这里需要实现OID查询服务
                // 暂时只保留原始OID
                uncommonOid.setDescription("Unknown OID: " + oid);
            } catch (Exception e) {
                LOG.error("查询OID失败，error--->{}",e.toString());
            }

            resultUncommonOids.add(uncommonOid);
        }
        x509Cert.setUncommonOIDs(resultUncommonOids);
        return x509Cert;
    }
}
