package com.geeksec.nta.certificate.util.db.nebula;

import com.geeksec.utils.FileUtil;
import com.google.common.collect.Lists;
import com.vesoft.nebula.client.graph.NebulaPoolConfig;
import com.vesoft.nebula.client.graph.data.HostAddress;
import com.vesoft.nebula.client.graph.net.NebulaPool;
import com.vesoft.nebula.client.graph.net.Session;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.UnknownHostException;
import java.util.List;
import java.util.Properties;

/**
 * <AUTHOR>
 * @Date 2023/10/12
 */

public class CertNebulaPoolUtils {
    private static final Logger logger = LoggerFactory.getLogger(CertNebulaPoolUtils.class);

    private static Properties properties = FileUtil.getProperties("/config.properties");
    // Nebula Configs
    private static int nebulaPoolMaxConnSize = Integer.parseInt(properties.getProperty("nebula.pool.max.size", "200"));
    private static int nebulaPoolMinConnSize = Integer.parseInt(properties.getProperty("nebula.pool.min.size","50"));
    private static int nebulaPoolIdleTime = Integer.parseInt(properties.getProperty("nebula.pool.idle.time","180000"));
    private static int nebulaPoolTimeout = Integer.parseInt(properties.getProperty("nebula.pool.timeout","300000"));
    private static String nebulaCluster = properties.getProperty("nebula.graph.addr");
    private static String userName = properties.getProperty("nebula.graph.username");
    private static String password = properties.getProperty("nebula.graph.password");
    // 空间表名
    private static String space = properties.getProperty("nebula.space.name");


    public static NebulaPoolConfig nebulaPoolConfig() {
        NebulaPoolConfig nebulaPoolConfig = new NebulaPoolConfig();
        nebulaPoolConfig.setMaxConnSize(nebulaPoolMaxConnSize);
        nebulaPoolConfig.setMinConnSize(nebulaPoolMinConnSize);
        nebulaPoolConfig.setIdleTime(nebulaPoolIdleTime);
        nebulaPoolConfig.setTimeout(nebulaPoolTimeout);
        return nebulaPoolConfig;
    }

    public static NebulaPool nebulaPool(NebulaPoolConfig nebulaPoolConfig)
            throws UnknownHostException {
        List<HostAddress> addresses = null;
        try {
            String[] hostPorts = StringUtils.split(nebulaCluster, ",");
            addresses = Lists.newArrayListWithExpectedSize(hostPorts.length);
            for (String hostPort : hostPorts) {
                String[] linkElements = StringUtils.split(hostPort, ":");
                HostAddress hostAddress = new HostAddress(linkElements[0],
                        Integer.valueOf(linkElements[1]));
                addresses.add(hostAddress);
            }
        } catch (Exception e) {
            throw new RuntimeException("nebula数据库连接信息配置有误，正确格式：ip1:port1,ip2:port2");
        }
        NebulaPool pool = new NebulaPool();
        Boolean initResult = pool.init(addresses, nebulaPoolConfig);
        if (!initResult) {
            logger.error("poll init failed.");
        }
        return pool;
    }

    public static Session getSession(NebulaPool nebulaPool) {
        Session session = null;
        try {
            if (nebulaPool == null) {
                nebulaPool = nebulaPool(nebulaPoolConfig());
            }
            session = nebulaPool.getSession(userName, password, false);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("初始化Nebula Graph Session 失败! error --->{}", e);
        }
        return session;
    }

}
