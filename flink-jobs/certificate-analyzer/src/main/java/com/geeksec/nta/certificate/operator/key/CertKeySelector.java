package com.geeksec.nta.certificate.operator.key;

import com.geeksec.analysisFunction.analysisEntity.cert.X509Cert;
import org.apache.flink.api.java.functions.KeySelector;

/**
 * <AUTHOR>
 * @Date 2023/2/15
 */

public class CertKeySelector implements KeySelector<X509Cert, Integer> {
    private static final long serialVersionUID = 1429326005310979722L;
    private int parallelism;
    private Integer[] reBalanceKeys;

    public CertKeySelector(int parallelism) {
        this.parallelism = parallelism;
        reBalanceKeys = KeyReBalanceUtil.createReBalanceKeys(parallelism);
    }

    @Override
    public Integer getKey(X509Cert x509Cert) throws Exception {
        return reBalanceKeys[(int) (x509Cert.getDuration() % parallelism)];
    }
}
