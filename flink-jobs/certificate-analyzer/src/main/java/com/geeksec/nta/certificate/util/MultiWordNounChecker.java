package com.geeksec.nta.certificate.util;

import com.geeksec.analysisFunction.analysisEntity.cert.X509Cert;
import opennlp.tools.postag.POSTaggerME;
import opennlp.tools.tokenize.TokenizerME;
import opennlp.tools.util.Span;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;

/**
 * <AUTHOR>
 * @Date  2024/8/7
 */
public class MultiWordNounChecker {

    protected static final Logger LOG = LoggerFactory.getLogger(MultiWordNounChecker.class);

    public static void DanaBotChecker(X509Cert x509Cert, TokenizerME tokenizer, POSTaggerME tagger, HashSet<String> wordDictionary) throws IOException {
        HashMap subject = CertFormatUtil.objectToMap(x509Cert.getSubject());

        if(!subject.containsKey("CN") || !subject.containsKey("L") || !subject.containsKey("O")
                || !subject.containsKey("ST") || !subject.containsKey("OU") || !subject.containsKey("C")
                || subject.size()!=6){
            return;
        }

        String subjectCN = String.valueOf(CertFormatUtil.readDataFromKeys(subject, "CN", "")).toLowerCase();
        String subjectL = String.valueOf(CertFormatUtil.readDataFromKeys(subject, "L", "")).toLowerCase();
        String subjectO = String.valueOf(CertFormatUtil.readDataFromKeys(subject, "O", "")).toLowerCase();
        String subjectST = String.valueOf(CertFormatUtil.readDataFromKeys(subject, "ST", "")).toLowerCase();
        String subjectOU = String.valueOf(CertFormatUtil.readDataFromKeys(subject, "OU", "")).toLowerCase();

        if(!checkSentence(subjectCN,tokenizer,tagger,wordDictionary,false)){
            return;
        }
        if(!checkSentence(subjectL,tokenizer,tagger,wordDictionary,false)){
            return;
        }
        if(!checkSentence(subjectO,tokenizer,tagger,wordDictionary,false)){
            return;
        }
        if(!checkSentence(subjectST,tokenizer,tagger,wordDictionary,false)){
            return;
        }
        if(!checkSentence(subjectOU,tokenizer,tagger,wordDictionary,false)){
            return;
        }
        List<String> tagList = x509Cert.getTagList();
        // 序列号长度为8，且是自签名证书，序列号要从IntString转化为hexString为8位
        if(tagList.contains("Self Signed Cert")){
            tagList.add("Botnet DanaBot Cert");
            tagList.add("Random Domain Cert");
            x509Cert.setTagList(tagList);
        }
    }

    public static void QuakbotChecker(X509Cert x509Cert, TokenizerME tokenizer, POSTaggerME tagger, HashSet<String> wordDictionary) throws IOException {
        HashMap subject = CertFormatUtil.objectToMap(x509Cert.getSubject());
        String subjectCN = String.valueOf(CertFormatUtil.readDataFromKeys(subject, "CN", "")).toLowerCase();
        String subjectOU = String.valueOf(CertFormatUtil.readDataFromKeys(subject, "OU", "")).toLowerCase();

        HashMap issuer = CertFormatUtil.objectToMap(x509Cert.getIssuer());
        String issuerCN = String.valueOf(CertFormatUtil.readDataFromKeys(issuer, "CN", "")).toLowerCase();
        String issuerO = String.valueOf(CertFormatUtil.readDataFromKeys(issuer, "O", "")).toLowerCase();
        List<String> tagList = x509Cert.getTagList();

        if(!subject.containsKey("CN") || !subject.containsKey("OU") || !subject.containsKey("C")
                || subject.size()!=3){
            return;
        }

        if(!issuer.containsKey("CN") || !issuer.containsKey("L") || !issuer.containsKey("O")
                || !issuer.containsKey("ST")  || !issuer.containsKey("C")
                || issuer.size()!=5){
            return;
        }

        boolean flagSubjectCN = true;
        boolean flagSubjectOU = true;
        boolean flagIssuerCN = true;
        boolean flagIssuerO = true;
        if(!subjectCN.equals(issuerCN)){
            return;
        }
        if(tagList.contains("Self Signed Cert")){
            return;
        }
        if(!checkSentence(subjectCN,tokenizer,tagger,wordDictionary,true)){
            flagSubjectCN = false;
        }
        if(!checkSentence(subjectOU,tokenizer,tagger,wordDictionary,true)){
            flagSubjectCN = false;
        }
        if(!checkSentence(issuerCN,tokenizer,tagger,wordDictionary,true)){
            flagSubjectCN = false;
        }
        if(!checkSentence(issuerO,tokenizer,tagger,wordDictionary,true)){
            flagSubjectCN = false;
        }
        if(flagSubjectCN || flagSubjectOU || flagIssuerCN || flagIssuerO){
            try{
                String serialNumber = Long.toHexString(Long.valueOf(x509Cert.getSerialNumber()));
                // 序列号长度为8，且是自签名证书
                if(serialNumber.length()==4){
                    tagList.add("Botnet Quakbot Cert");
                    tagList.add("Random Domain Cert");
                    x509Cert.setTagList(tagList);
                }
            }catch (Exception e){
                LOG.error("序列号转化失败，肯定不是Quakbot证书");
            }
        }
    }

    public static boolean checkSentence(String text,TokenizerME tokenizer,POSTaggerME tagger,HashSet<String> wordDictionary,boolean strict){

        // 正则表达式匹配所有非英文字符（非字母且非空格）
        String regex = "[^a-zA-Z\\s]";
        // 替换所有匹配的字符为一个空格，这里使用空格作为替换字符，但根据要求也可以替换为""
        text = text.replaceAll(regex, " ");

        // 过滤掉非英文字符
        if (!text.matches("[a-zA-Z\\s]+")) {
            LOG.info("名称不包含英文字符，无法判断为可读的名词短语。");
            return false;
        }

        // 分词
        String[] tokens;
        try{
            tokens = tokenizer.tokenize(text);
            Span span = new Span(0, tokens.length);
        }catch (Exception e){
            LOG.info("分词失败，取原来值为一个单词");
            tokens = new String[]{text};
        }


        if (tokens.length==1){
            if (!wordDictionary.contains(tokens[0])) {
                LOG.info("'" + tokens[0] + "' 只有一个词且不是可读的单词。");
                return true;
            }else {
                return false;
            }
        }

        // 词性标注
        String[] tags = tagger.tag(tokens);

        if(!strict){
            // 非严格检查，有一个可读，就不是
            for (int i = 0; i < tokens.length; i++) {
                if ("PROPN".equals(tags[i])) {
                    if (wordDictionary.contains(tokens[i])) {
                        LOG.info("'" + tokens[i] + "' 不是单词，整个短语不是可读的单词短语。");
                        return false;
                    }
                }
            }
            return true;
        }else {
            // 严格检查，有一个不可读，就是
            for (int i = 0; i < tokens.length; i++) {
                if ("PROPN".equals(tags[i])) {
                    if (wordDictionary.contains(tokens[i])) {
                        LOG.info("'" + tokens[i] + "' 不是单词，整个短语不是可读的单词短语。");
                        return true;
                    }
                }
            }
            return false;
        }
    }
}