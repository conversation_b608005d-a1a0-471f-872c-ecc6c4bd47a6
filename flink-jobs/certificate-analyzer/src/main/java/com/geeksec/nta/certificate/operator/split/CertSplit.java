package com.geeksec.nta.certificate.operator.split;

import com.geeksec.analysisFunction.analysisEntity.cert.X509Cert;
import com.geeksec.analysisFunction.certInfoAll.CertScoreMapRichFunction;
import com.geeksec.utils.CertFormatUtil;
import com.geeksec.utils.FileUtil;
import com.geeksec.utils.MinioCertificateClient;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2022/10/20
 * 对证书进行分流，系统证书，父证书是系统证书，需要打标的证书
 */

public class CertSplit extends ProcessFunction<X509Cert, X509Cert>{
    protected static final Logger LOG = LoggerFactory.getLogger(CertSplit.class);
    private final Properties properties = FileUtil.getProperties("/config.properties");

    //sys系统证书标签
    public static final OutputTag<X509Cert> SYS_CERT = new OutputTag<>("sys_cert",TypeInformation.of(X509Cert.class));

    //需要打标签的用户证书
    public static final OutputTag<X509Cert> TAG_CERT = new OutputTag<>("tag_cert",TypeInformation.of(X509Cert.class));

    //定义威胁分数名单，label.csv的索引+分数
    public static HashMap<String, Integer> BLACK_SCORE_MAP = new HashMap<>();
    public static HashMap<String, Integer> BLACK_SCORE_MAP_tag = new HashMap<>();

    //定义正常分数名单，label.csv的索引+分数
    public static HashMap<String, Integer> WHITE_SCORE_MAP = new HashMap<>();

    private static final List<String> ANDROID_LIST = Arrays.asList("6ba0b098e171ef5aadfe4815807710f4bd6f0b28"
            ,"b49082dd450cbe8b5bb166d3e2a40826cded42cf","53a2b04bca6bd645e6398a8ec40dd2bf77c3a290"
            ,"58e8abb0361533fb80f79b1b6d29d3ff8d5f00f0","55a6723ecbf2eccdc3237470199d2abe11e381d1",
            "d69b561148f01c77c54578c10926df5b856976ad","d067c11351010caad0c76a65373116264f5371a2"
            ,"093c61f38b8bdc7d55df7538020500e125f5c836","1f24c630cda418ef2069ffad4fdd5f463a1b69aa",
            "ec2c834072af269510ff0ef203ee3170f6789dca");

    private static final List<String> FIREFOX_LIST = Arrays.asList("4464937bad1ac056cae4c172646e1b84077a1952"
            ,"1cf68d2fe7dcae50940428fd340cd8f29a7879ff","3360474313ed55cb3521ec092618bf4550892226",
            "981962b7ef60bb3281bb507cbaeef00cea5a5e55","4bd1505c8e2d7a58b907b1a3c69d404e0a4ce295",
            "f379fdd0198851f2f93237353e2188077a4c718e");

    private static final List<String> WITHDRAW_LIST = Arrays.asList("038c7319e6878d5228ee81d47714b6540eae9929",
            "ae93b49e5897dc337a936634c665ddca33418613","40ee4e8c0a6d30f429a6f3676bb81de78558ed6a",
            "c832f5800b96e93fe996570230fafb1ff5e3ac93","31984f032b97e5303907725627ee61f880d94b65",
            "a45dd09d84aa68175e43185c81764e15b1a37971","6a67b0820c809d51e087117fc24f5b4325cdace9",
            "9da3c52f29a9faffb67277ce9197bea31cb617e2","43226b2688bf40d765ade1ac94c9466163723c8b",
            "76dd1a1045e2e900d9c425973135291fda7842cf");

    // 微软网关加密， Microsoft Server Gated Crypto
    private static final List<String> MICROSOFT_GATED_LIST = Arrays.asList("37b67b371b59654c597d0ea0910c2f04b56e0ca2"
            ,"fb9086b87d54dd91ef3b0d8a449d36367184120b","e5b7e109d7ee5a31cdd027646fb718ff1279f3b4",
            "5e3643ea34d505d6776a4beb60fd9f7e62584385");

    // Netscape 网关加密: Key Agreement
    private static final List<String> NETSCAPE_KEY_AGREEMENT_LIST = Arrays.asList("f69fa405bedee6d9594428dc803cf6bbff9e64ef"
            ,"820cce80ebe1d059a057c51a0ad1b2e3e97544d7","3aa96151e2f500f9a5bf1045fa5d278169e41a07",
            "542f5c654bf176e531a1e8e5710fd91ae04d8e71","b22921678772c513f05723c7ed4293a04bcb70ee",
            "6eae470586bc3d471ff92662d09e18474b527d45","b6f7a80a5dce0d01c53fcdb35bd4de97835a770c",
            "26a204a8bec9722e2d344e5743456119d8767560");

    @Override
    public void open(Configuration parameters) throws Exception {
        HashMap<String, HashMap<String, String>> tagList = FileUtil.loadTagList("/label.csv");
        Set<String> tagKeys = tagList.keySet();
        for (String tagKey : tagKeys) {
            HashMap<String, String> element = tagList.get(tagKey);
            BLACK_SCORE_MAP.put(element.get("Tag_Remark"), Integer.valueOf(element.get("Black_List")));
            WHITE_SCORE_MAP.put(element.get("Tag_Remark"), Integer.valueOf(element.get("White_List")));
            BLACK_SCORE_MAP_tag.put(element.get("Tag_Id"), Integer.valueOf(element.get("Black_List")));
        }

        LOG.info("Certificate analyzer initialized");
    }

    @Override
    public void close() throws Exception {
        super.close();
    }

    @Override
    public void processElement(X509Cert x509Cert, ProcessFunction<X509Cert, X509Cert>.Context context, Collector<X509Cert> collector) throws Exception {
        // 提前打标，避免系统证书过滤
        addTagBeforeSplit(x509Cert);

        try{
            List<String> tags = new ArrayList<>();

            // 校验当前证书是否是系统白名单证书，是系统证书就直接往下写入
            // 使用MinIO检查是否是系统证书
            boolean isSysCert = MinioCertificateClient.isCertExists(x509Cert.getASN1SHA1());

            if (isSysCert) {
                getCaAttr(x509Cert,tags);
                x509Cert.setWhiteCert("Yes");
                tags.add("WhiteCert");
                x509Cert.setTagList(CertScoreMapRichFunction.tagsToTagId(tags));
                x509Cert.setBlackList(getScore(tags, BLACK_SCORE_MAP));
                x509Cert.setWhiteList(getScore(tags, WHITE_SCORE_MAP));
                x509Cert.setMethod(false);
                x509Cert.setImportTime(System.currentTimeMillis()/1000);
                context.output(SYS_CERT,x509Cert);
            }
            else{
                x509Cert.setMethod(false);
                x509Cert.setWhiteCert("No");
                context.output(TAG_CERT,x509Cert);
            }
        }catch (Exception e){
            LOG.error("处理证书失败，error--->{}",e.toString());
        }
    }


    private void addTagBeforeSplit(X509Cert x509Cert) {
        List<String> tags = x509Cert.getTagList();
        String SHA1 = x509Cert.getASN1SHA1();
        if(ANDROID_LIST.contains(SHA1)){
            tags.add("Android Trust");
        }

        if(FIREFOX_LIST.contains(SHA1)){
            tags.add("Firefox Trust");
        }

        if(WITHDRAW_LIST.contains(SHA1)){
            tags.add("Withdraw Cert");
        }

        if(MICROSOFT_GATED_LIST.contains(SHA1)){
            tags.add("Microsoft Server Gated Crypto");
        }

        if(NETSCAPE_KEY_AGREEMENT_LIST.contains(SHA1)){
            tags.add("Key Agreement");
        }
        x509Cert.setTagList(tags);
    }

    public static int getScore(List<String> tags, HashMap<String, Integer> map) {
        int score = 0;

        for (String tag : tags) {
            //根据tag的值反查map中的标签对应的黑白名单对应的值，如果查不到就返回0
            Integer tagNumber = map.getOrDefault(tag, 0);
            score += tagNumber;
        }

        //超过100取100
        return Math.min(score, 100);
    }

    // 打标CA，根CA，公开CA
    private static void getCaAttr(X509Cert x509Cert, List<String> tags){
        HashMap extension = CertFormatUtil.objectToMap(x509Cert.getExtension());
        String basicConstraints = String.valueOf(CertFormatUtil.readDataFromKeys(extension, "basicConstraints", ""));
        String subjectMd5 = x509Cert.getSubjectMD5();
        String issuerMd5 = x509Cert.getIssuerMD5();
        String subjectKeyId = String.valueOf(CertFormatUtil.readDataFromKeys(extension, "subjectKeyIdentifier", ""));
        if ("null".equals(subjectKeyId)) {
            subjectKeyId = "";
        }

        String issuerKeyId = String.valueOf(CertFormatUtil.readDataFromKeys(extension, "authorityKeyIdentifier", ""));
        if ("null".equals(issuerKeyId)) {
            issuerKeyId = "";
        }
        if (basicConstraints.contains("CA:TRUE")) {
            tags.add("Public CA");
            if (subjectMd5.equals(issuerMd5) || (!"".equals(subjectKeyId) && subjectKeyId.equals(issuerKeyId))) {
                tags.add("Root CA");
            } else {
                tags.add("CA");
            }
        }
    }
}
