package com.geeksec.nta.certificate.io.sink.nebula;

import com.geeksec.analysisFunction.analysisEntity.cert.X509Cert;
import com.geeksec.dbConnect.nebula.CertNebulaPoolUtils;
import com.geeksec.flinkTool.sideOutputTag.CertNebulaDedupOutPutTag;
import com.geeksec.utils.FileUtil;
import com.geeksec.utils.RedisUtils;
import com.vesoft.nebula.client.graph.net.NebulaPool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.util.Properties;

/**
 * <AUTHOR>
 * @Date 2023/10/12
 */

public class CertNebulaDedup extends ProcessFunction<Row, Row> {

    private static Properties properties = FileUtil.getProperties("/config.properties");
    public static final String NEBULA_GRAPH_ADDR = properties.getProperty("nebula.graph.addr");
    public static final String NEBULA_META_ADDR = properties.getProperty("nebula.meta.addr");
    public static final String NEBULA_GRAPH_SPACE = properties.getProperty("nebula.space.name");

    private static final Logger logger = LoggerFactory.getLogger(CertNebulaDedup.class);

    private static NebulaPool nebulaPool;
    private static transient JedisPool jedisPool = null;

    @Override
    public void open(Configuration parameters) throws Exception {
        // 初始化nebula连接池
        nebulaPool = CertNebulaPoolUtils.nebulaPool(CertNebulaPoolUtils.nebulaPoolConfig());

        // redis池 初始化.
        jedisPool = RedisUtils.initJedisPool();
        logger.info("生成jedisPool成功! {}", jedisPool.getNumIdle(),jedisPool.hashCode());

        super.open(parameters);
    }

    @Override
    public void close() throws Exception {
        if (nebulaPool!=null){
            nebulaPool.close();
        }
        if (jedisPool != null) {
            jedisPool.close();
        }
        super.close();
    }

    @Override
    public void processElement(Row row, ProcessFunction<Row, Row>.Context context, Collector<Row> collector) throws Exception {
        String dedupType = (String) row.getField(0);
        X509Cert x509Cert = (X509Cert) row.getField(1);
        Jedis jedis = null;
        String key = "";

        try {
            jedis = RedisUtils.getJedis(jedisPool);
            jedis.select(11);

            switch (dedupType){
                // 分类设置sql语句
                case "cert_id":
                    String certId = x509Cert.getCertId();
                    key = certId;
                    break;
                case "issuer_md5":
                    String issuerMd5 = x509Cert.getIssuerMD5();
                    key = issuerMd5;
                    break;
                case "subject_md5":
                    String subjectMd5 = x509Cert.getSubjectMD5();
                    key = subjectMd5;
                    break;
                case "ORG":
                    String org = x509Cert.getCompany();
                    key = org;
                    break;
                case "URL":
                    String url = (String) row.getField(2);
                    key = url;
                    break;
                default:
                    break;
            }

            // 如果是证书需要更新，如果是实体，不需要更新
            if(jedis.exists(key)){
                switch (dedupType){
                    case "cert_id":
                        context.output(CertNebulaDedupOutPutTag.Dedup_Nebula_Cert,row);
                        break;
                    case "issuer_md5":
                        logger.info("issuer 重复，无需更新");
                        break;
                    case "subject_md5":
                        logger.info("subject 重复，无需更新");
                        break;
                    case "URL":
                        logger.info("URL 重复，无需更新");
                        break;
                    case "ORG":
                        logger.info("ORG 重复，无需更新");
                        break;
                    default:
                        break;
                }
            }else {
                switch (dedupType){
                    case "cert_id":
                        jedis.setex(key,86400,"1");
                        context.output(CertNebulaDedupOutPutTag.Not_Dedup_Nebula_Cert,row);
                        break;
                    case "issuer_md5":
                        jedis.setex(key,86400,"1");
                        context.output(CertNebulaDedupOutPutTag.Not_Dedup_Nebula_Issuer,row);
                        break;
                    case "subject_md5":
                        jedis.setex(key,86400,"1");
                        context.output(CertNebulaDedupOutPutTag.Not_Dedup_Nebula_Subject,row);
                        break;
                    case "URL":
                        jedis.setex(key,86400,"1");
                        context.output(CertNebulaDedupOutPutTag.Not_Dedup_Nebula_URL,row);
                        break;
                    case "ORG":
                        jedis.setex(key,86400,"1");
                        context.output(CertNebulaDedupOutPutTag.Not_Dedup_Nebula_ORG,row);
                        break;
                    default:
                        break;
                }
            }
        }catch (Exception e){
            logger.error("redis连接获取失败");
        }finally {
            if (jedis != null){
                jedis.close();
            }
        }
    }
}
