package com.geeksec.nta.certificate.operator.functiontag;

import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

/**
 * <AUTHOR>
 * @Date 2023/10/12
 */

public class CertNebulaDedupOutPutTag {
    public static final OutputTag<Row> Dedup_Nebula_Cert = new OutputTag<>("Dedup_Nebula_Cert", TypeInformation.of(Row.class));
    public static final OutputTag<Row> Not_Dedup_Nebula_Cert = new OutputTag<>("Not_Dedup_Nebula_Cert",TypeInformation.of(Row.class));
    public static final OutputTag<Row> Not_Dedup_Nebula_Issuer = new OutputTag<>("Not_Dedup_Nebula_Issuer",TypeInformation.of(Row.class));
    public static final OutputTag<Row> Not_Dedup_Nebula_Subject = new OutputTag<>("Not_Dedup_Nebula_Subject",TypeInformation.of(Row.class));
    public static final OutputTag<Row> Not_Dedup_Nebula_URL = new OutputTag<>("Not_Dedup_Nebula_URL",TypeInformation.of(Row.class));
    public static final OutputTag<Row> Not_Dedup_Nebula_ORG = new OutputTag<>("Not_Dedup_Nebula_ORG",TypeInformation.of(Row.class));
}
