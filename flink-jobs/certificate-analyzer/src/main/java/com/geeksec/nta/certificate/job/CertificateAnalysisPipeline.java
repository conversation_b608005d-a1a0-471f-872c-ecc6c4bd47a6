package com.geeksec.nta.certificate.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.geeksec.analysisFunction.analysisEntity.cert.X509Cert;
import com.geeksec.analysisFunction.certInfoAll.*;
import com.geeksec.analysisFunction.certSign.*;
import com.geeksec.analysisFunction.certSplitAll.CertSplit;
import com.geeksec.analysisFunction.deduplication.CertDeduplicationWindow;
import com.geeksec.analysisFunction.deduplication.RedisBloomFilterDeduplicationFlatMapFunction;
import com.geeksec.analysisFunction.deduplication.SystemCertDeduplicationWindow;
import com.geeksec.analysisFunction.infoSink.es.ESIndexSink;
import com.geeksec.analysisFunction.infoSink.minio.MinioFileSinkFactory;
import com.geeksec.flinkTool.deserializer.CertDeserializationSchema;
import com.geeksec.flinkTool.sideOutputTag.ErrorCorrectingOutPutTag;
import com.geeksec.flinkTool.sideOutputTag.SinkOutPutTag;
import com.geeksec.flinkTool.sideOutputTag.signOutPutTag;
import com.geeksec.nta.certificate.io.source.kafka.KafkaConsumerConfig;
import com.geeksec.utils.CertFormatUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.geeksec.analysisFunction.certSplitAll.CertSplit.SYS_CERT;
import static com.geeksec.analysisFunction.certSplitAll.CertSplit.TAG_CERT;
import static com.geeksec.config.globalConfig.initGlobalJobParameters;
import static com.geeksec.flinkTool.sideOutputTag.DeduplicationOutPutTag.Not_Dedup_cert;

/**
 * 证书分析流水线
 * 从Kafka读取证书数据，进行分析处理，并将结果写入Elasticsearch和MinIO
 */
@Slf4j
public class CertificateAnalysisPipeline {

        /**
         * Parallelism 全局变量设置
         * PA 是 Parallelism 的缩写，后面的数字代表全局变量
         */
        public static final Integer PA1 = 1;
        public static final Integer PA2 = 2;
        public static final Integer PA4 = 4;
        public static final Integer PA8 = 8;
        public static final Integer PA12 = 12;
        public static final Integer PA16 = 16;
        public static final Integer PA32 = 32;

        public static void main(String[] args) throws Exception {
                log.info("Creating flink context from kafka.");

                // 创建用户证书的env，消息推送的kafka，得到 kafkaStream
                StreamExecutionEnvironment env = createFlinkEnv();
                Configuration globalConf = initGlobalJobParameters(env);
                // 获取kafka配置
                Properties kafkaProperty = KafkaConsumerConfig.getKafkaConsumerConfig(globalConf);
                List<String> topicList = Arrays.asList(
                                globalConf.getString("kafka.topic", ""));
                KafkaSource<X509Cert> kafkaSource = KafkaSource.<X509Cert>builder()
                                .setBootstrapServers(kafkaProperty.getProperty("bootstrap.servers"))
                                .setTopics(topicList)
                                // 从最新的地方开始取
                                .setStartingOffsets(OffsetsInitializer.latest())
                                // 读取出来的数据通过CertSerializationSchema方法进行证书的序列化。
                                .setDeserializer(new CertDeserializationSchema())
                                .setGroupId(kafkaProperty.getProperty("group.id"))
                                .setProperties(kafkaProperty)
                                .build();

                SingleOutputStreamOperator<X509Cert> certStream = env
                                .fromSource(kafkaSource, WatermarkStrategy.noWatermarks(), "Kafka-Source",
                                                TypeInformation.of(X509Cert.class))
                                .setParallelism(PA4).name("获取来自topic为：user的证书");

                certStream.sinkTo(MinioFileSinkFactory.createCertificateSink()).name("证书写入MinIO").setParallelism(PA1);

                SingleOutputStreamOperator<X509Cert> kafkaStream = certStream
                                .flatMap(new RedisBloomFilterDeduplicationFlatMapFunction())
                                .name("Redis布隆过滤器去重").setParallelism(PA1)
                                .process(new ProcessFunction<X509Cert, X509Cert>() {
                                        @Override
                                        public void processElement(X509Cert x509Cert,
                                                        ProcessFunction<X509Cert, X509Cert>.Context context,
                                                        Collector<X509Cert> collector) throws Exception {
                                                String certSource = x509Cert.getCertSource();
                                                if (x509Cert.getASN1MD5() == null) {
                                                        x509Cert.setIsError(true);
                                                        if ("User".equals(certSource)) {
                                                                context.output(ErrorCorrectingOutPutTag.errorUserCert,
                                                                                x509Cert);
                                                        } else {
                                                                log.error("出现了意料之外的证书来源类型");
                                                        }
                                                } else {
                                                        x509Cert.setIsError(false);
                                                        if ("User".equals(certSource)) {
                                                                context.output(ErrorCorrectingOutPutTag.normalUserCert,
                                                                                x509Cert);
                                                        } else if ("System".equals(certSource)) {
                                                                context.output(ErrorCorrectingOutPutTag.normalSystemCert,
                                                                                x509Cert);
                                                        } else {
                                                                log.error("出现了意料之外的证书来源类型");
                                                        }
                                                }
                                        }
                                }).name("区分UserCert和SystemCert是否存在错误").setParallelism(PA2);

                certStream.sinkTo(MinioFileSinkFactory.createCertificateSink()).name("证书写入MinIO").setParallelism(PA1);

                // 处理系统证书
                DataStream<X509Cert> SystemCertStream = kafkaStream
                                .getSideOutput(ErrorCorrectingOutPutTag.normalSystemCert)
                                .filter(ObjectUtils::anyNotNull).name("过滤系统证书中为空的证书").setParallelism(PA2);

                // 以下是系统证书的操作, 去重, 转Json, 写入, 给系统证书初始化一个userIDList为"0"
                SingleOutputStreamOperator<X509Cert> systemCertDedup = SystemCertDeduplicationWindow
                                .systemCertDeduplicationWindow(SystemCertStream);

                // 将系统证书写入MinIO
                systemCertDedup.sinkTo(MinioFileSinkFactory.createCertificateSink())
                                .name("系统证书写入MinIO").setParallelism(PA1);

                // 转换为JSON并写入ES
                SingleOutputStreamOperator<JSONObject> systemCertDedupStream = systemCertDedup
                                .map(new MapFunction<X509Cert, JSONObject>() {
                                        @Override
                                        public JSONObject map(X509Cert x509Cert) throws Exception {
                                                x509Cert.setUserIDList(Collections.singletonList("0"));
                                                x509Cert.setTagList(new ArrayList<>());
                                                x509Cert.setImportTime(System.currentTimeMillis() / 1000);
                                                JSONObject certJson = JSONObject
                                                                .parseObject(JSON.toJSONString(x509Cert));
                                                certJson.put("UncommonOIDs",
                                                                CertFormatUtil.getString(x509Cert.getUncommonOIDs()));
                                                return certJson;
                                        }
                                }).name("系统证书转json").setParallelism(PA4);

                ESIndexSink.SystemCertSink(systemCertDedupStream);

                // Filtering parse error`s cert. DataStream.filter过滤出不为空的证书
                SingleOutputStreamOperator<X509Cert> filterStream = kafkaStream
                                .getSideOutput(ErrorCorrectingOutPutTag.normalUserCert)
                                .filter(ObjectUtils::anyNotNull)
                                .name("过滤出为空的证书").setParallelism(PA2);

                // 使用窗口函数对需要打标的证书进行过滤，在这一步还可以加process进行纠错之类的
                SingleOutputStreamOperator<X509Cert> DeduplicationTagStream = CertDeduplicationWindow
                                .certDeduplicationWindow(filterStream);

                // 对上一步过滤出的不是重复的证书进行分流
                SingleOutputStreamOperator<X509Cert> splitStream = DeduplicationTagStream.getSideOutput(Not_Dedup_cert)
                                .process(new CertSplit()).name("根据证书属性分流操作").setParallelism(PA16);

                // 得到sys函数分出的流,分别是系统证书，白名单证书，需要打标签的证书
                DataStream<X509Cert> SysCertStream = splitStream.getSideOutput(SYS_CERT);
                DataStream<X509Cert> TagStream = splitStream.getSideOutput(TAG_CERT);

                SingleOutputStreamOperator<X509Cert> x509RelatedInfoStream = TagStream
                                .map(new CertRelatedInfoMapFunction())
                                .name("对证书进行关联信息的提取").setParallelism(PA4);
                SingleOutputStreamOperator<X509Cert> SysRelatedInfoStream = SysCertStream
                                .map(new CertRelatedInfoMapFunction())
                                .name("对证书进行关联信息的提取").setParallelism(PA4);

                // Parsing cert tag. 对证书进行map操作，通过map的富函数进行操作
                SingleOutputStreamOperator<X509Cert> x509SimpleTagStream = x509RelatedInfoStream
                                .map(new CertSimpleTagMapRichFunction())
                                .name("对证书进行简单的打标").setParallelism(PA16);
                SingleOutputStreamOperator<Row> x509IfSignStream = x509SimpleTagStream.process(new CertIfSign())
                                .name("过滤自签名证书")
                                .setParallelism(PA4);

                SingleOutputStreamOperator<Row> x509CertSign1 = x509IfSignStream.getSideOutput(signOutPutTag.goto_sign)
                                .process(new Cert1SignSplit()).name("一级验签").setParallelism(PA16);
                SingleOutputStreamOperator<Row> x509CertSign2 = x509CertSign1
                                .getSideOutput(signOutPutTag.continue_sign1)
                                .process(new Cert2SignSplit()).name("二级验签").setParallelism(PA8);
                SingleOutputStreamOperator<Row> x509CertSign3 = x509CertSign2
                                .getSideOutput(signOutPutTag.continue_sign2)
                                .process(new Cert3SignSplit()).name("三级验签").setParallelism(PA4);
                SingleOutputStreamOperator<Row> x509CertSign4 = x509CertSign3
                                .getSideOutput(signOutPutTag.continue_sign3)
                                .process(new Cert4SignSplit()).name("四级验签").setParallelism(PA4);
                SingleOutputStreamOperator<Row> x509CertSign5 = x509CertSign4
                                .getSideOutput(signOutPutTag.continue_sign4)
                                .process(new Cert5SignSplit()).name("五级验签（过长）").setParallelism(PA2);

                // 将所有待打分的Stream合并
                DataStream<Row> x509Cert2ScoreRow = x509IfSignStream.getSideOutput(signOutPutTag.goto_score)
                                .union(x509CertSign1.getSideOutput(signOutPutTag.stop_sign1))
                                .union(x509CertSign2.getSideOutput(signOutPutTag.stop_sign2))
                                .union(x509CertSign3.getSideOutput(signOutPutTag.stop_sign3))
                                .union(x509CertSign4.getSideOutput(signOutPutTag.stop_sign4))
                                .union(x509CertSign5.getSideOutput(signOutPutTag.stop_sign5));
                SingleOutputStreamOperator<X509Cert> x509Cert2ScoreX509 = x509Cert2ScoreRow
                                .map(new MapFunction<Row, X509Cert>() {
                                        @Override
                                        public X509Cert map(Row row) throws Exception {
                                                return (X509Cert) row.getField(0);
                                        }
                                }).name("Row转X509").setParallelism(PA4);

                // 进行验签后的打标
                SingleOutputStreamOperator<X509Cert> x509TagAfterSign = x509Cert2ScoreX509
                                .map(new CertTag1AfterSignMapFunction()).name("验签后的打标步骤1").setParallelism(PA4)
                                .map(new CertTag2AfterSignMapFunction()).name("验签后的打标步骤2").setParallelism(PA4);

                // 对所有的打标完成的证书进行转化标签以及打分
                SingleOutputStreamOperator<X509Cert> x509TagAllToScore = x509TagAfterSign
                                .map(new CertScoreMapRichFunction())
                                .name("所有打标证书进行打分以及标签转化").setParallelism(PA4);

                // 对所有系统证书和父证书是系统证书的系统证书进行转化json,并分来源分流
                SingleOutputStreamOperator<JSONObject> AllSinkJsonStream = SysRelatedInfoStream.union(x509TagAllToScore)
                                .process(new ProcessFunction<X509Cert, JSONObject>() {
                                        @Override
                                        public void processElement(X509Cert x509Cert,
                                                        ProcessFunction<X509Cert, JSONObject>.Context context,
                                                        Collector<JSONObject> collector) throws Exception {
                                                String cert_source = x509Cert.getCertSource();
                                                CertFormatUtil.setCertTreatLevel(x509Cert);
                                                JSONObject cert_json = JSONObject
                                                                .parseObject(JSON.toJSONString(x509Cert));
                                                cert_json.put("UncommonOIDs",
                                                                CertFormatUtil.getString(x509Cert.getUncommonOIDs()));
                                                if (cert_source.equals("User")) {
                                                        context.output(SinkOutPutTag.User, cert_json);
                                                } else {
                                                        log.error("证书来源出现问题");
                                                }
                                        }
                                }).name("证书转Json写入").setParallelism(PA2);

                // 对所有不重复证书进行ES不同来源入库
                ESIndexSink.UserCertSink(AllSinkJsonStream.getSideOutput(SinkOutPutTag.User));

                env.execute("GSF03-证书分析打标与入库");
        }

        /**
         * 创建Flink上下文
         *
         * @return
         */
        private static StreamExecutionEnvironment createFlinkEnv() {
                log.info("Flink ExecutionEnvironment create now, type:[ stream ].");

                // 创建Flink上下文环境
                StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
                // 设计检查点
                env.enableCheckpointing(5000L);
                env.disableOperatorChaining();
                // 错误处理机制
                env.setRestartStrategy(RestartStrategies.failureRateRestart(
                                // 最大失败次数
                                3,
                                // 衡量失败次数的是时间段
                                Time.of(2, TimeUnit.SECONDS),
                                // 间隔
                                Time.of(5, TimeUnit.SECONDS)));

                return env;
        }
}
