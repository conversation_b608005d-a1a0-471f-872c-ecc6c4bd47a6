package com.geeksec.nta.certificate.util;

import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.FSDataInputStream;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Properties;

/**
 * 证书检索工具类，用于从MinIO中检索证书
 *
 * <AUTHOR>
 */
public class CertificateRetriever {
    private static final Logger logger = LoggerFactory.getLogger(CertificateRetriever.class);

    /**
     * S3协议前缀
     */
    private static final String S3_PROTOCOL = "s3://";

    /**
     * 证书文件名前缀
     */
    private static final String PREFIX = "cert_";

    /**
     * SHA1哈希值的前缀长度，用于创建子目录
     */
    private static final int SHA1_PREFIX_LENGTH = 2;

    /**
     * 配置属性
     */
    private static final Properties properties = FileUtil.getProperties("/config.properties");

    /**
     * MinIO配置
     */
    private static final String MINIO_ENDPOINT = properties.getProperty("minio.endpoint", "http://minio:9000");
    private static final String MINIO_ACCESS_KEY = properties.getProperty("minio.access.key", "minioadmin");
    private static final String MINIO_SECRET_KEY = properties.getProperty("minio.secret.key", "minioadmin");
    private static final String MINIO_BUCKET = properties.getProperty("minio.bucket.name", "certificates");

    /**
     * Hadoop配置
     */
    private static final Configuration hadoopConf = new Configuration();

    static {
        // 设置S3配置
        hadoopConf.set("fs.s3a.endpoint", MINIO_ENDPOINT);
        hadoopConf.set("fs.s3a.access.key", MINIO_ACCESS_KEY);
        hadoopConf.set("fs.s3a.secret.key", MINIO_SECRET_KEY);
        hadoopConf.set("fs.s3a.path.style.access", "true");
        hadoopConf.set("fs.s3a.impl", "org.apache.hadoop.fs.s3a.S3AFileSystem");
    }

    /**
     * 从MinIO获取证书
     *
     * @param sha1 证书的SHA1哈希值
     * @return 证书数据，如果不存在则返回null
     */
    public static byte[] getCertificate(String sha1) {
        if (sha1 == null || sha1.isEmpty()) {
            return null;
        }

        // 构建S3路径
        String s3Path = buildCertificatePath(sha1);

        try {
            // 创建文件系统
            FileSystem fs = FileSystem.get(hadoopConf);
            Path path = new Path(s3Path);

            // 检查文件是否存在
            if (!fs.exists(path)) {
                logger.debug("Certificate not found: {}", s3Path);
                return null;
            }

            // 读取文件内容
            try (FSDataInputStream inputStream = fs.open(path);
                 ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }

                return outputStream.toByteArray();
            }
        } catch (IOException e) {
            logger.error("Error retrieving certificate from MinIO: {}", s3Path, e);
            return null;
        }
    }

    /**
     * 检查证书是否存在
     *
     * @param sha1 证书的SHA1哈希值
     * @return 如果存在则返回true，否则返回false
     */
    public static boolean certificateExists(String sha1) {
        if (sha1 == null || sha1.isEmpty()) {
            return false;
        }

        // 构建S3路径
        String s3Path = buildCertificatePath(sha1);

        try {
            // 创建文件系统
            FileSystem fs = FileSystem.get(hadoopConf);
            Path path = new Path(s3Path);

            // 检查文件是否存在
            return fs.exists(path);
        } catch (IOException e) {
            logger.error("Error checking if certificate exists in MinIO: {}", s3Path, e);
            return false;
        }
    }

    /**
     * 构建证书在MinIO中的路径
     *
     * @param sha1 证书的SHA1哈希值
     * @return 完整的S3路径
     */
    private static String buildCertificatePath(String sha1) {
        // 使用SHA1的前两个字符作为子目录，避免单个目录下文件过多
        String subDir = sha1.length() >= SHA1_PREFIX_LENGTH ? sha1.substring(0, SHA1_PREFIX_LENGTH) : "unknown";
        return S3_PROTOCOL + MINIO_BUCKET + "/certificates/" + subDir + "/" + PREFIX + sha1;
    }
}
