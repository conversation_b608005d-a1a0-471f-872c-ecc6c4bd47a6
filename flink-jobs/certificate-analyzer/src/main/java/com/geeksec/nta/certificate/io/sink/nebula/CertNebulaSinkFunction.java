package com.geeksec.nta.certificate.io.sink.nebula;

import com.geeksec.analysisFunction.analysisEntity.cert.X509Cert;
import com.geeksec.utils.CertFormatUtil;
import com.geeksec.utils.FileUtil;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.connector.nebula.connection.NebulaClientOptions;
import org.apache.flink.connector.nebula.connection.NebulaGraphConnectionProvider;
import org.apache.flink.connector.nebula.connection.NebulaMetaConnectionProvider;
import org.apache.flink.connector.nebula.sink.NebulaEdgeBatchOutputFormat;
import org.apache.flink.connector.nebula.sink.NebulaSinkFunction;
import org.apache.flink.connector.nebula.sink.NebulaVertexBatchOutputFormat;
import org.apache.flink.connector.nebula.statement.EdgeExecutionOptions;
import org.apache.flink.connector.nebula.statement.VertexExecutionOptions;
import org.apache.flink.connector.nebula.utils.WriteModeEnum;
import org.apache.flink.shaded.netty4.io.netty.util.internal.StringUtil;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

import java.util.*;

import static com.geeksec.nta.pipeline.CertificateAnalysisPipeline.PA1;
import static com.geeksec.nta.pipeline.CertificateAnalysisPipeline.PA4;

/**
 * <AUTHOR>
 * @Date 2023/10/16
 */

public class CertNebulaSinkFunction {

    private static Properties properties = FileUtil.getProperties("/config.properties");
    public static final String NEBULA_GRAPH_ADDR = properties.getProperty("nebula.graph.addr");
    public static final String NEBULA_META_ADDR = properties.getProperty("nebula.meta.addr");
    public static final String NEBULA_GRAPH_SPACE = properties.getProperty("nebula.space.name");

    // Nebula Conn通用配置
    private static NebulaClientOptions nebulaClientOptions = new NebulaClientOptions.NebulaClientOptionsBuilder()
            .setGraphAddress(NEBULA_GRAPH_ADDR)
            .setMetaAddress(NEBULA_META_ADDR)
            .build();
    private static NebulaGraphConnectionProvider graphConnectionProvider = new NebulaGraphConnectionProvider(
            nebulaClientOptions);
    private static NebulaMetaConnectionProvider metaConnectionProvider = new NebulaMetaConnectionProvider(
            nebulaClientOptions);

    /**
     * 不重复证书插入操作
     */
    public static void insertVertexData(DataStream<Row> certNotDedupStream) {
        VertexExecutionOptions executionOptions = new VertexExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setTag("CERT")
                .setIdIndex(0)
                .setFields(Arrays.asList("cert_id", "first_time", "last_time", "black_list", "white_list",
                        "source_type", "cert_md5", "remark"))
                .setPositions(Arrays.asList(0, 1, 2, 3, 4, 5, 6, 7))
                .setBatchSize(20)
                .setBatchIntervalMs(2000)
                .build();

        NebulaVertexBatchOutputFormat outputFormat = new NebulaVertexBatchOutputFormat(
                graphConnectionProvider, metaConnectionProvider, executionOptions);
        NebulaSinkFunction<Row> nebulaSinkFunction = new NebulaSinkFunction<>(outputFormat);
        DataStream<Row> certRowStream = certNotDedupStream.map((MapFunction<Row, Row>) row -> {
            X509Cert x509Cert = (X509Cert) row.getField(1);
            Row certSinkRow = new Row(8);
            certSinkRow.setField(0, x509Cert.getCertId());
            certSinkRow.setField(1, x509Cert.getImportTime());
            certSinkRow.setField(2, x509Cert.getImportTime());
            certSinkRow.setField(3, x509Cert.getBlackList());
            certSinkRow.setField(4, x509Cert.getWhiteList());
            certSinkRow.setField(5, sourceToInt(x509Cert.getCertSource()));
            certSinkRow.setField(6, sourceToInt(x509Cert.getCertSource()));
            certSinkRow.setField(7, StringUtil.EMPTY_STRING);
            return certSinkRow;
        }).name("X509Cert转化为Row").setParallelism(PA4);
        certRowStream.addSink(nebulaSinkFunction).name("未重复证书直接Sink").setParallelism(PA4);
    }

    private static Integer sourceToInt(String certSource) {
        switch (certSource) {
            case "User":
                return 0;
            case "System":
                return 3;
            default:
                return null;
        }
    }

    /**
     * 重复证书更新操作
     */
    public static void updateVertexData(DataStream<Row> certDedupStream) {
        VertexExecutionOptions executionOptions = new VertexExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setTag("CERT")
                .setIdIndex(0)
                .setFields(Arrays.asList("last_time", "black_list", "white_list", "cert_md5"))
                .setPositions(Arrays.asList(1, 2, 3, 4))
                .setWriteMode(WriteModeEnum.UPDATE)
                .setBatchSize(20)
                .setBatchIntervalMs(2000)
                .build();

        NebulaVertexBatchOutputFormat outputFormat = new NebulaVertexBatchOutputFormat(
                graphConnectionProvider, metaConnectionProvider, executionOptions);
        NebulaSinkFunction<Row> nebulaSinkFunction = new NebulaSinkFunction<>(outputFormat);
        DataStream<Row> certRowStream = certDedupStream.map((MapFunction<Row, Row>) row -> {
            X509Cert x509Cert = (X509Cert) row.getField(1);

            Row certSinkRow = new Row(5);
            certSinkRow.setField(0, x509Cert.getCertId());
            certSinkRow.setField(1, x509Cert.getImportTime());
            certSinkRow.setField(2, x509Cert.getBlackList());
            certSinkRow.setField(3, x509Cert.getWhiteList());
            certSinkRow.setField(4, x509Cert.getASN1MD5());
            return certSinkRow;
        }).name("X509Cert转化为Row").setParallelism(PA4);
        certRowStream.addSink(nebulaSinkFunction).name("重复证书直接update").setParallelism(PA4);
    }

    /**
     * 不重复Issuer插入操作
     */
    public static void insertIssuerVertexData(DataStream<Row> certNotDedupStream) {
        VertexExecutionOptions executionOptions = new VertexExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setTag("ISSUER")
                .setIdIndex(0)
                .setFields(Arrays.asList("issuer_md5", "common_name", "country", "object_name"))
                .setPositions(Arrays.asList(0, 1, 2, 3))
                .setBatchSize(20)
                .setBatchIntervalMs(2000)
                .build();

        NebulaVertexBatchOutputFormat outputFormat = new NebulaVertexBatchOutputFormat(
                graphConnectionProvider, metaConnectionProvider, executionOptions);
        NebulaSinkFunction<Row> nebulaSinkFunction = new NebulaSinkFunction<>(outputFormat);
        DataStream<Row> certRowStream = certNotDedupStream.map((MapFunction<Row, Row>) row -> {
            X509Cert x509Cert = (X509Cert) row.getField(1);

            Row certSinkRow = new Row(4);
            HashMap issuer = CertFormatUtil.objectToMap(x509Cert.getIssuer());
            String issuerCN = String.valueOf(CertFormatUtil.readDataFromKeys(issuer, "CN", StringUtil.EMPTY_STRING));// 取出issuer的CN值
            if ("null".equals(issuerCN)) {
                issuerCN = StringUtil.EMPTY_STRING;
            }
            String issuerC = String.valueOf(CertFormatUtil.readDataFromKeys(issuer, "C", StringUtil.EMPTY_STRING));// 取出issuer的C值
            if ("null".equals(issuerC)) {
                issuerC = StringUtil.EMPTY_STRING;
            }
            String issuerO = String.valueOf(CertFormatUtil.readDataFromKeys(issuer, "O", StringUtil.EMPTY_STRING));// 取出issuer的O值
            if ("null".equals(issuerO)) {
                issuerO = StringUtil.EMPTY_STRING;
            }
            certSinkRow.setField(0, "issuer_" + x509Cert.getIssuerMD5());
            certSinkRow.setField(1, issuerCN);
            certSinkRow.setField(2, issuerC);
            certSinkRow.setField(3, issuerO);
            return certSinkRow;
        }).name("X509Cert转化为Row").setParallelism(PA4);
        certRowStream.addSink(nebulaSinkFunction).name("未重复issuer直接Sink").setParallelism(PA4);
    }

    /**
     * 不重复Subject插入操作
     */
    public static void insertSubjectVertexData(DataStream<Row> certNotDedupStream) {
        VertexExecutionOptions executionOptions = new VertexExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setTag("SUBJECT")
                .setIdIndex(0)
                .setFields(Arrays.asList("subject_md5", "common_name", "country", "object_name"))
                .setPositions(Arrays.asList(0, 1, 2, 3))
                .setBatchSize(20)
                .setBatchIntervalMs(2000)
                .build();

        NebulaVertexBatchOutputFormat outputFormat = new NebulaVertexBatchOutputFormat(
                graphConnectionProvider, metaConnectionProvider, executionOptions);
        NebulaSinkFunction<Row> nebulaSinkFunction = new NebulaSinkFunction<>(outputFormat);
        DataStream<Row> certRowStream = certNotDedupStream.map((MapFunction<Row, Row>) row -> {
            X509Cert x509Cert = (X509Cert) row.getField(1);

            Row certSinkRow = new Row(4);
            HashMap subject = CertFormatUtil.objectToMap(x509Cert.getIssuer());
            String subjectCN = String.valueOf(CertFormatUtil.readDataFromKeys(subject, "CN", StringUtil.EMPTY_STRING));// 取出issuer的CN值
            if ("null".equals(subjectCN)) {
                subjectCN = StringUtil.EMPTY_STRING;
            }
            String subjectC = String.valueOf(CertFormatUtil.readDataFromKeys(subject, "C", StringUtil.EMPTY_STRING));// 取出issuer的C值
            if ("null".equals(subjectC)) {
                subjectC = StringUtil.EMPTY_STRING;
            }
            String subjectO = String.valueOf(CertFormatUtil.readDataFromKeys(subject, "O", StringUtil.EMPTY_STRING));// 取出issuer的O值
            if ("null".equals(subjectO)) {
                subjectO = StringUtil.EMPTY_STRING;
            }
            certSinkRow.setField(0, "subject_" + x509Cert.getSubjectMD5());
            certSinkRow.setField(1, subjectCN);
            certSinkRow.setField(2, subjectC);
            certSinkRow.setField(3, subjectO);
            return certSinkRow;
        }).name("X509Cert转化为Row").setParallelism(PA4);
        certRowStream.addSink(nebulaSinkFunction).name("未重复subject直接Sink").setParallelism(PA4);
    }

    /**
     * 不重复 URL 插入操作
     */
    public static void insertURLVertexData(DataStream<Row> certDedupStream) {
        VertexExecutionOptions executionOptions = new VertexExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setTag("URL")
                .setIdIndex(0)
                .setFields(Arrays.asList("url_key", "black_list", "white_list"))
                .setPositions(Arrays.asList(0, 1, 2))
                .setBatchSize(20)
                .setBatchIntervalMs(2000)
                .build();

        NebulaVertexBatchOutputFormat outputFormat = new NebulaVertexBatchOutputFormat(
                graphConnectionProvider, metaConnectionProvider, executionOptions);
        NebulaSinkFunction<Row> nebulaSinkFunction = new NebulaSinkFunction<>(outputFormat);
        DataStream<Row> certRowStream = certDedupStream.map((MapFunction<Row, Row>) row -> {
            X509Cert x509Cert = (X509Cert) row.getField(1);
            String URL = (String) row.getField(2);

            Row certSinkRow = new Row(3);
            certSinkRow.setField(0, URL);
            certSinkRow.setField(1, 0);
            certSinkRow.setField(2, 0);
            return certSinkRow;
        }).name("X509Cert转化为Row").setParallelism(PA4);
        certRowStream.addSink(nebulaSinkFunction).name("不重复URl直接写入").setParallelism(PA4);
    }

    /**
     * 不重复 ORG 插入操作
     */
    public static void insertORGVertexData(DataStream<Row> certDedupStream) {
        VertexExecutionOptions executionOptions = new VertexExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setTag("ORG")
                .setIdIndex(0)
                .setFields(Arrays.asList("org_name", "org_desc", "black_list", "white_list", "remark"))
                .setPositions(Arrays.asList(1, 2, 3, 4, 5))
                .setBatchSize(20)
                .setBatchIntervalMs(2000)
                .build();

        NebulaVertexBatchOutputFormat outputFormat = new NebulaVertexBatchOutputFormat(
                graphConnectionProvider, metaConnectionProvider, executionOptions);
        NebulaSinkFunction<Row> nebulaSinkFunction = new NebulaSinkFunction<>(outputFormat);
        DataStream<Row> certRowStream = certDedupStream.map((MapFunction<Row, Row>) row -> {
            X509Cert x509Cert = (X509Cert) row.getField(1);
            String company = x509Cert.getCompany();
            String company_md5 = DigestUtils.md5Hex(company);

            Row certSinkRow = new Row(6);
            certSinkRow.setField(0, company_md5);
            certSinkRow.setField(1, company);
            certSinkRow.setField(2, company);
            certSinkRow.setField(3, 0);
            certSinkRow.setField(4, 0);
            certSinkRow.setField(5, 0);
            return certSinkRow;
        }).name("X509Cert转化为Row").setParallelism(PA4);
        certRowStream.addSink(nebulaSinkFunction).name("不重复 ORG 直接写入").setParallelism(PA4);
    }

    /**
     * Subject 关联 Cert
     */
    public static void insertSubjectRelatedCert(SingleOutputStreamOperator<X509Cert> writeCertModelStream) {
        EdgeExecutionOptions executionOptions = new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("subject_related")
                .setSrcIndex(0)
                .setDstIndex(1)
                .setRankIndex(2)
                .setFields(Collections.emptyList())
                .setPositions(Collections.emptyList())
                .setBatchSize(100)
                .setBatchIntervalMs(2000)
                .build();

        NebulaEdgeBatchOutputFormat outputFormat = new NebulaEdgeBatchOutputFormat(
                graphConnectionProvider, metaConnectionProvider, executionOptions);
        NebulaSinkFunction<Row> nebulaSinkFunction = new NebulaSinkFunction<>(outputFormat);
        DataStream<Row> dataStream = writeCertModelStream.map(new MapFunction<X509Cert, Row>() {
            @Override
            public Row map(X509Cert x509Cert) throws Exception {
                Row SubjectRelatedCertEdgeRow = new Row(3);
                SubjectRelatedCertEdgeRow.setField(0, x509Cert.getCertId());
                SubjectRelatedCertEdgeRow.setField(1, "subject_" + x509Cert.getSubjectMD5());
                SubjectRelatedCertEdgeRow.setField(2, 0);
                return SubjectRelatedCertEdgeRow;
            }
        }).name("Subject 关联 Cert 边生成").setParallelism(PA1);
        dataStream.addSink(nebulaSinkFunction).name("Subject 关联 Cert 边写入").setParallelism(PA1);
    }

    /**
     * Issuer 关联 Cert
     */
    public static void insertIssuerRelatedCert(SingleOutputStreamOperator<X509Cert> writeCertModelStream) {
        EdgeExecutionOptions executionOptions = new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("issuer_related")
                .setSrcIndex(0)
                .setDstIndex(1)
                .setRankIndex(2)
                .setFields(Collections.emptyList())
                .setPositions(Collections.emptyList())
                .setBatchSize(100)
                .setBatchIntervalMs(2000)
                .build();

        NebulaEdgeBatchOutputFormat outputFormat = new NebulaEdgeBatchOutputFormat(
                graphConnectionProvider, metaConnectionProvider, executionOptions);
        NebulaSinkFunction<Row> nebulaSinkFunction = new NebulaSinkFunction<>(outputFormat);
        DataStream<Row> dataStream = writeCertModelStream.map(new MapFunction<X509Cert, Row>() {
            @Override
            public Row map(X509Cert x509Cert) throws Exception {
                Row IssuerRelatedCertEdgeRow = new Row(3);
                IssuerRelatedCertEdgeRow.setField(0, x509Cert.getCertId());
                IssuerRelatedCertEdgeRow.setField(1, "issuer_" + x509Cert.getIssuerMD5());
                IssuerRelatedCertEdgeRow.setField(2, 0);
                return IssuerRelatedCertEdgeRow;
            }
        }).name("Issuer 关联 Cert 边生成").setParallelism(PA1);
        dataStream.addSink(nebulaSinkFunction).name("Issuer 关联 Cert 边写入").setParallelism(PA1);
    }

    /**
     * Domain 关联 Org（company）
     * domain_belong_to_org
     */
    public static void insertDomainBelongToOrg(SingleOutputStreamOperator<X509Cert> writeCertModelStream) {
        EdgeExecutionOptions executionOptions = new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("domain_belong_to_org")
                .setSrcIndex(0)
                .setDstIndex(1)
                .setRankIndex(2)
                .setFields(Collections.emptyList())
                .setPositions(Collections.emptyList())
                .setBatchSize(100)
                .setBatchIntervalMs(2000)
                .build();

        NebulaEdgeBatchOutputFormat outputFormat = new NebulaEdgeBatchOutputFormat(
                graphConnectionProvider, metaConnectionProvider, executionOptions);
        NebulaSinkFunction<Row> nebulaSinkFunction = new NebulaSinkFunction<>(outputFormat);
        DataStream<Row> dataStream = writeCertModelStream.flatMap(new FlatMapFunction<X509Cert, Row>() {
            @Override
            public void flatMap(X509Cert x509Cert, Collector<Row> collector) throws Exception {

                List<String> relatedDomain = x509Cert.getAssociateDomain();
                String company = x509Cert.getCompany();
                if (!"".equals(company) && relatedDomain.size() > 0) {
                    for (String domain : relatedDomain) {
                        Row DomainBelongToOrgRow = new Row(3);
                        DomainBelongToOrgRow.setField(0, domain);
                        DomainBelongToOrgRow.setField(1, DigestUtils.md5Hex(company));
                        DomainBelongToOrgRow.setField(2, 0);
                        collector.collect(DomainBelongToOrgRow);
                    }
                }
            }
        }).name("domain_belong_to_org 边生成").setParallelism(PA1);
        dataStream.addSink(nebulaSinkFunction).name("domain_belong_to_org 边写入").setParallelism(PA1);
    }

    /**
     * IP 关联 Org（company）
     * ip_belong_to_org
     */
    public static void insertIpBelongToOrg(SingleOutputStreamOperator<X509Cert> writeCertModelStream) {
        EdgeExecutionOptions executionOptions = new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("ip_belong_to_org")
                .setSrcIndex(0)
                .setDstIndex(1)
                .setRankIndex(2)
                .setFields(Collections.emptyList())
                .setPositions(Collections.emptyList())
                .setBatchSize(100)
                .setBatchIntervalMs(2000)
                .build();

        NebulaEdgeBatchOutputFormat outputFormat = new NebulaEdgeBatchOutputFormat(
                graphConnectionProvider, metaConnectionProvider, executionOptions);
        NebulaSinkFunction<Row> nebulaSinkFunction = new NebulaSinkFunction<>(outputFormat);
        DataStream<Row> dataStream = writeCertModelStream.flatMap(new FlatMapFunction<X509Cert, Row>() {
            @Override
            public void flatMap(X509Cert x509Cert, Collector<Row> collector) throws Exception {

                List<String> relatedIp = x509Cert.getAssociateIP();
                String company = x509Cert.getCompany();
                if (!"".equals(company) && relatedIp.size() > 0) {
                    for (String Ip : relatedIp) {
                        Row IpBelongToOrgRow = new Row(3);
                        IpBelongToOrgRow.setField(0, Ip);
                        IpBelongToOrgRow.setField(1, DigestUtils.md5Hex(company));
                        IpBelongToOrgRow.setField(2, 0);
                        collector.collect(IpBelongToOrgRow);
                    }
                }
            }
        }).name("ip_belong_to_org 边生成").setParallelism(PA1);
        dataStream.addSink(nebulaSinkFunction).name("ip_belong_to_org 边写入").setParallelism(PA1);
    }

    /**
     * Cert 关联 Org（company）
     * cert_belong_to_org
     */
    public static void insertCertBelongToOrg(SingleOutputStreamOperator<X509Cert> writeCertModelStream) {
        EdgeExecutionOptions executionOptions = new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("cert_belong_to_org")
                .setSrcIndex(0)
                .setDstIndex(1)
                .setRankIndex(2)
                .setFields(Collections.emptyList())
                .setPositions(Collections.emptyList())
                .setBatchSize(100)
                .setBatchIntervalMs(2000)
                .build();

        NebulaEdgeBatchOutputFormat outputFormat = new NebulaEdgeBatchOutputFormat(
                graphConnectionProvider, metaConnectionProvider, executionOptions);
        NebulaSinkFunction<Row> nebulaSinkFunction = new NebulaSinkFunction<>(outputFormat);
        DataStream<Row> dataStream = writeCertModelStream.flatMap(new FlatMapFunction<X509Cert, Row>() {
            @Override
            public void flatMap(X509Cert x509Cert, Collector<Row> collector) throws Exception {
                String company = x509Cert.getCompany();
                if (!"".equals(company)) {
                    Row CertBelongToOrgRow = new Row(3);
                    CertBelongToOrgRow.setField(0, x509Cert.getCertId());
                    CertBelongToOrgRow.setField(1, DigestUtils.md5Hex(company));
                    CertBelongToOrgRow.setField(2, 0);
                    collector.collect(CertBelongToOrgRow);
                }
            }
        }).name("cert_belong_to_org 边生成").setParallelism(PA1);
        dataStream.addSink(nebulaSinkFunction).name("cert_belong_to_org 边写入").setParallelism(PA1);
    }

    /**
     * Domain 关联 Url
     * domain_url_related
     */
    public static void insertDomainUrlRelated(SingleOutputStreamOperator<X509Cert> writeCertModelStream) {
        EdgeExecutionOptions executionOptions = new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("domain_url_related")
                .setSrcIndex(0)
                .setDstIndex(1)
                .setRankIndex(2)
                .setFields(Collections.emptyList())
                .setPositions(Collections.emptyList())
                .setBatchSize(100)
                .setBatchIntervalMs(2000)
                .build();

        NebulaEdgeBatchOutputFormat outputFormat = new NebulaEdgeBatchOutputFormat(
                graphConnectionProvider, metaConnectionProvider, executionOptions);
        NebulaSinkFunction<Row> nebulaSinkFunction = new NebulaSinkFunction<>(outputFormat);
        DataStream<Row> dataStream = writeCertModelStream.flatMap(new FlatMapFunction<X509Cert, Row>() {
            @Override
            public void flatMap(X509Cert x509Cert, Collector<Row> collector) throws Exception {

                List<String> relatedDomain = x509Cert.getAssociateDomain();
                List<String> relatedURL = x509Cert.getAssociateURL();
                if (relatedURL.size() > 0 && relatedDomain.size() > 0) {
                    for (String Domain : relatedDomain) {
                        for (String URL : relatedURL) {
                            Row DomainUrlRelatedRow = new Row(3);
                            DomainUrlRelatedRow.setField(0, Domain);
                            DomainUrlRelatedRow.setField(1, URL);
                            DomainUrlRelatedRow.setField(2, 0);
                            collector.collect(DomainUrlRelatedRow);
                        }
                    }
                }
            }
        }).name("domain_url_related 边生成").setParallelism(PA1);
        dataStream.addSink(nebulaSinkFunction).name("domain_url_related 边写入").setParallelism(PA1);
    }

    /**
     * IP 关联 Url
     * ip_url_related
     */
    public static void insertIpUrlRelated(SingleOutputStreamOperator<X509Cert> writeCertModelStream) {
        EdgeExecutionOptions executionOptions = new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("ip_url_related")
                .setSrcIndex(0)
                .setDstIndex(1)
                .setRankIndex(2)
                .setFields(Collections.emptyList())
                .setPositions(Collections.emptyList())
                .setBatchSize(100)
                .setBatchIntervalMs(2000)
                .build();

        NebulaEdgeBatchOutputFormat outputFormat = new NebulaEdgeBatchOutputFormat(
                graphConnectionProvider, metaConnectionProvider, executionOptions);
        NebulaSinkFunction<Row> nebulaSinkFunction = new NebulaSinkFunction<>(outputFormat);
        DataStream<Row> dataStream = writeCertModelStream.flatMap(new FlatMapFunction<X509Cert, Row>() {
            @Override
            public void flatMap(X509Cert x509Cert, Collector<Row> collector) throws Exception {

                List<String> relatedIp = x509Cert.getAssociateIP();
                List<String> relatedURL = x509Cert.getAssociateURL();
                if (relatedURL.size() > 0 && relatedIp.size() > 0) {
                    for (String Ip : relatedIp) {
                        for (String URL : relatedURL) {
                            Row IpUrlRelatedRow = new Row(3);
                            IpUrlRelatedRow.setField(0, Ip);
                            IpUrlRelatedRow.setField(1, URL);
                            IpUrlRelatedRow.setField(2, 0);
                            collector.collect(IpUrlRelatedRow);
                        }
                    }
                }
            }
        }).name("ip_url_related 边生成").setParallelism(PA1);
        dataStream.addSink(nebulaSinkFunction).name("ip_url_related 边写入").setParallelism(PA1);
    }

    /**
     * Cert 关联 Url
     * cert_url_related
     */
    public static void insertCertUrlRelated(SingleOutputStreamOperator<X509Cert> writeCertModelStream) {
        EdgeExecutionOptions executionOptions = new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("ip_url_related")
                .setSrcIndex(0)
                .setDstIndex(1)
                .setRankIndex(2)
                .setFields(Collections.emptyList())
                .setPositions(Collections.emptyList())
                .setBatchSize(100)
                .setBatchIntervalMs(2000)
                .build();

        NebulaEdgeBatchOutputFormat outputFormat = new NebulaEdgeBatchOutputFormat(
                graphConnectionProvider, metaConnectionProvider, executionOptions);
        NebulaSinkFunction<Row> nebulaSinkFunction = new NebulaSinkFunction<>(outputFormat);
        DataStream<Row> dataStream = writeCertModelStream.flatMap(new FlatMapFunction<X509Cert, Row>() {
            @Override
            public void flatMap(X509Cert x509Cert, Collector<Row> collector) throws Exception {

                List<String> relatedURL = x509Cert.getAssociateURL();
                if (relatedURL.size() > 0) {
                    for (String URL : relatedURL) {
                        Row CertUrlRelatedRow = new Row(3);
                        CertUrlRelatedRow.setField(0, x509Cert.getCertId());
                        CertUrlRelatedRow.setField(1, URL);
                        CertUrlRelatedRow.setField(2, 0);
                        collector.collect(CertUrlRelatedRow);
                    }
                }
            }
        }).name("cert_url_related 边生成").setParallelism(PA1);
        dataStream.addSink(nebulaSinkFunction).name("cert_url_related 边写入").setParallelism(PA1);
    }
}
