package com.geeksec.nta.certificate.operator.error;

import com.geeksec.analysisFunction.analysisEntity.cert.X509Cert;
import com.geeksec.flinkTool.sideOutputTag.ErrorCorrectingOutPutTag;
import com.geeksec.utils.*;
import com.geeksec.utils.MinioCertificateClient;
import org.apache.commons.pool.impl.GenericObjectPool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.elasticsearch.client.RestHighLevelClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * 处理证书字节正向错误的处理函数
 * 使用MinIO替代LMDB存储证书数据
 *
 * <AUTHOR>
 * @date 2023/7/7
 * @modified hufengkai
 * @date 2024/10/15
 */
public class ByteNumPositiveProcess extends ProcessFunction<X509Cert, X509Cert> {
    protected static final Logger LOG = LoggerFactory.getLogger(ByteNumPositiveProcess.class);
    private static transient JedisPool jedisPool = null;
    private static transient GenericObjectPool<RestHighLevelClient> EsPool = null;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        // redis池初始化
        jedisPool = TimeRedisUtils.initJedisPool();
        LOG.info("生成jedisPool成功! 活跃连接数——{}——，空闲数——{}——，等待数——{}——",
                jedisPool.getNumActive(), jedisPool.getNumIdle(), jedisPool.getNumWaiters());

        // ES初始化
        EsPool = EsUtils.initEsPool();
        LOG.info("生成EsPool成功! {}", EsPool.getNumIdle(), EsPool.hashCode());
    }

    @Override
    public void close() throws Exception {
        if (jedisPool != null) {
            jedisPool.close();
        }
        if (EsPool != null) {
            EsPool.close();
        }
        super.close();
    }

    @Override
    public void processElement(X509Cert x509Cert, ProcessFunction<X509Cert, X509Cert>.Context context,
            Collector<X509Cert> collector) throws Exception {
        // 提取证书的正向哈希列表
        LinkedList<String> positiveHash = x509Cert.getPositiveHash();
        int length = positiveHash.size();
        Jedis redisClient = null;
        RestHighLevelClient esClient = null;
        x509Cert.setParseStatus(false);

        try {
            redisClient = TimeRedisUtils.getJedis(jedisPool);
            redisClient.select(3);
            esClient = EsUtils.getClient(EsPool);

            // 先查Redis中是否存在
            String redisSha1 = TimeRedisUtils.scanRedisNum(length, positiveHash, redisClient, "PositiveHash");

            // Redis中存在，直接使用SHA1从MinIO获取证书数据
            if (redisSha1 != null) {
                LOG.info("Redis中查询到了纠错信息");
                List<String> sha1List = Arrays.asList(redisSha1.split("_")[0]);
                Map<String, byte[]> resultMap = MinioCertificateClient.queryDataList(sha1List);

                if (!resultMap.isEmpty()) {
                    processCertificateData(x509Cert, resultMap, redisSha1, context, "Redis");
                } else {
                    LOG.error("MinIO中无法查询到Redis中SHA1为{}的证书", redisSha1);
                    context.output(ErrorCorrectingOutPutTag.FailNumPositiveCert, x509Cert);
                }
            } else {
                // Redis中不存在，查询ES
                String esSha1 = EsUtils.scanESHashNum(length, "PositiveHash", positiveHash, esClient);

                if (esSha1 != null) {
                    LOG.info("ES中查询到了纠错信息");
                    List<String> sha1List = Arrays.asList(esSha1);
                    Map<String, byte[]> resultMap = MinioCertificateClient.queryDataList(sha1List);

                    if (!resultMap.isEmpty()) {
                        processCertificateData(x509Cert, resultMap, esSha1, context, "ES");
                    } else {
                        LOG.error("MinIO中无法查询到ES中SHA1为{}的证书", esSha1);
                        context.output(ErrorCorrectingOutPutTag.FailNumPositiveCert, x509Cert);
                    }
                } else {
                    // ES也不存在
                    LOG.error("ES和Redis中都无法查询到该证书的纠错信息！！！");
                    context.output(ErrorCorrectingOutPutTag.FailNumPositiveCert, x509Cert);
                }
            }
        } catch (Exception e) {
            LOG.error("处理失败，error--->{}, 错误证书的SHA1 is--->{}",
                    e.getMessage(), x509Cert.getASN1SHA1());
            context.output(ErrorCorrectingOutPutTag.FailNumPositiveCert, x509Cert);
        } finally {
            if (redisClient != null) {
                redisClient.close();
            }
            if (esClient != null) {
                EsUtils.returnClient(esClient, EsPool);
            }
        }
    }

    /**
     * 处理证书数据
     *
     * @param x509Cert 原始证书
     * @param resultMap 查询结果
     * @param sha1 证书SHA1
     * @param context 处理上下文
     * @param source 数据来源（Redis或ES）
     */
    private void processCertificateData(X509Cert x509Cert, Map<String, byte[]> resultMap,
            String sha1, ProcessFunction<X509Cert, X509Cert>.Context context, String source) {

        for (Map.Entry<String, byte[]> entry : resultMap.entrySet()) {
            byte[] certByte = entry.getValue();
            String errorName = EsUtils.getErrorName(x509Cert.getCert(), certByte);
            X509Cert cert = new X509Cert(certByte);

            if ("ES".equals(source)) {
                LOG.info("{}", certByte);
                LOG.info("{}", x509Cert.getCert());
            }

            if (LevenshteinDistance.check_distance(certByte, x509Cert.getCert(), errorName)) {
                CertFormatUtil.parseContent(cert);
                x509Cert.setCorrectASN1SHA1(cert.getASN1SHA1());
                x509Cert.setTagList(Arrays.asList(errorName));
                x509Cert.setParseStatus(true);
                context.output(ErrorCorrectingOutPutTag.SuccessNumPositiveCert, x509Cert);
            } else {
                LOG.error("{}SHA1——{}——的证书纠错失败，LevenshteinDistance的距离过长", source, sha1);
                context.output(ErrorCorrectingOutPutTag.FailNumPositiveCert, x509Cert);
            }
            break;
        }
    }
}
