package com.geeksec.nta.certificate.operator.cert;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.utils.MysqlUtils;
import com.geeksec.utils.RedisUtils;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.configuration.Configuration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/10/17
 */

public class MysqlBlackListMapFunction extends RichMapFunction<JSONObject, JSONObject> {
    private static final Logger LOG = LoggerFactory.getLogger(MysqlBlackListMapFunction.class);
    public static transient JedisPool jedisPool = null;

    @Override
    public void open(Configuration parameters) throws Exception {
        // redis池 初始化.
        jedisPool = RedisUtils.initJedisPool();
        LOG.info("生成jedisPool成功! {}", jedisPool.getNumIdle(),jedisPool.hashCode());
        super.open(parameters);
    }

    @Override
    public void close() throws Exception {
        if(jedisPool != null){
            jedisPool.close();
        }
        super.close();
    }

    @Override
    public JSONObject map(JSONObject jsonObject) throws Exception {
        String SHA1 = jsonObject.getString("ASN1SHA1");
        List<String> task_batch_info = new ArrayList<>();//获取该证书sha1关联的所有task,batch信息
        Jedis jedis = null;
        try {
            jedis = RedisUtils.getJedis(jedisPool);
            task_batch_info = RedisUtils.getRedisTaskBatchInfoBlackList(SHA1, jedis);
        } catch (Exception e) {
            LOG.error("redis查询失败，error--->{},SHA1 is--->{}", e, SHA1);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
        if (task_batch_info.size()==0) {
            LOG.info("{}——不是用户导入的任何批次的证书,无需更新black_list",SHA1);
        }else {
            // 查询mysql中是否设置初始黑名单权值
            int blackList = jsonObject.getIntValue("BlackList");
            int mysqlBlackList = MysqlUtils.getBlackListFromMysql(task_batch_info,SHA1);
            if(mysqlBlackList>blackList){
                blackList = mysqlBlackList;
            }
            LOG.info("是用户导入证书,成功更新blackList为{}",blackList);
            jsonObject.put("BlackList",blackList);
        }

        return jsonObject;
    }
}
