package com.geeksec.nta.certificate.util.db.nebula;

import org.apache.flink.connector.nebula.connection.NebulaClientOptions;
import org.apache.flink.connector.nebula.connection.NebulaGraphConnectionProvider;
import org.apache.flink.connector.nebula.connection.NebulaMetaConnectionProvider;
import org.apache.flink.connector.nebula.statement.EdgeExecutionOptions;
import org.apache.flink.connector.nebula.statement.VertexExecutionOptions;
import org.apache.flink.connector.nebula.utils.WriteModeEnum;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.*;

/**
 * NebulaGraph连接器测试
 * 使用Mockito模拟外部依赖
 *
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class NebulaGraphConnectorTest {

    private static final Logger LOG = LoggerFactory.getLogger(NebulaGraphConnectorTest.class);

    @Mock
    private NebulaClientOptions nebulaClientOptions;

    @Mock
    private NebulaGraphConnectionProvider graphConnectionProvider;

    @Mock
    private NebulaMetaConnectionProvider metaConnectionProvider;

    @Before
    public void setUp() {
        // 模拟NebulaClientOptions行为
        when(nebulaClientOptions.getGraphAddress()).thenReturn("127.0.0.1:9669");
        when(nebulaClientOptions.getMetaAddress()).thenReturn("127.0.0.1:9559");
    }

    /**
     * 测试NebulaGraph连接提供者
     */
    @Test
    public void testNebulaGraphConnectionProvider() {
        // 使用静态方法模拟
        try (org.mockito.MockedConstruction<NebulaGraphConnectionProvider> mockedGraphProvider =
                     mockConstruction(NebulaGraphConnectionProvider.class);
             org.mockito.MockedConstruction<NebulaMetaConnectionProvider> mockedMetaProvider =
                     mockConstruction(NebulaMetaConnectionProvider.class)) {

            // 创建连接提供者
            NebulaGraphConnectionProvider graphProvider = new NebulaGraphConnectionProvider(nebulaClientOptions);
            NebulaMetaConnectionProvider metaProvider = new NebulaMetaConnectionProvider(nebulaClientOptions);

            // 验证结果
            assertNotNull("图连接提供者不应为空", graphProvider);
            assertNotNull("元数据连接提供者不应为空", metaProvider);

            // 验证交互
            assertEquals(1, mockedGraphProvider.constructed().size());
            assertEquals(1, mockedMetaProvider.constructed().size());
        }
    }

    /**
     * 测试顶点执行选项构建
     */
    @Test
    public void testVertexExecutionOptions() {
        // 测试INSERT模式
        VertexExecutionOptions insertOptions = new VertexExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace("testSpace")
                .setTag("person")
                .setIdIndex(0)
                .setFields(Arrays.asList("name", "age"))
                .setPositions(Arrays.asList(1, 2))
                .setBatch(2)
                .builder();

        assertEquals("testSpace", insertOptions.getGraphSpace());
        assertEquals("person", insertOptions.getTag());
        assertEquals(0, insertOptions.getIdIndex());
        assertEquals(2, insertOptions.getFields().size());
        assertEquals(WriteModeEnum.INSERT, insertOptions.getWriteMode());

        // 测试UPDATE模式
        VertexExecutionOptions updateOptions = new VertexExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace("testSpace")
                .setTag("person")
                .setIdIndex(0)
                .setFields(Arrays.asList("name", "age"))
                .setPositions(Arrays.asList(1, 2))
                .setWriteMode(WriteModeEnum.UPDATE)
                .setBatch(2)
                .builder();

        assertEquals(WriteModeEnum.UPDATE, updateOptions.getWriteMode());

        // 测试DELETE模式
        VertexExecutionOptions deleteOptions = new VertexExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace("testSpace")
                .setTag("person")
                .setIdIndex(0)
                .setFields(Arrays.asList("name", "age"))
                .setPositions(Arrays.asList(1, 2))
                .setWriteMode(WriteModeEnum.DELETE)
                .setBatch(2)
                .builder();

        assertEquals(WriteModeEnum.DELETE, deleteOptions.getWriteMode());
    }

    /**
     * 测试边执行选项构建
     */
    @Test
    public void testEdgeExecutionOptions() {
        // 测试INSERT模式
        EdgeExecutionOptions insertOptions = new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace("testSpace")
                .setEdge("friend")
                .setSrcIndex(0)
                .setDstIndex(1)
                .setRankIndex(2)
                .setFields(Arrays.asList("since", "weight"))
                .setPositions(Arrays.asList(3, 4))
                .setBatch(2)
                .builder();

        assertEquals("testSpace", insertOptions.getGraphSpace());
        assertEquals("friend", insertOptions.getEdge());
        assertEquals(0, insertOptions.getSrcIndex());
        assertEquals(1, insertOptions.getDstIndex());
        assertEquals(2, insertOptions.getRankIndex());
        assertEquals(2, insertOptions.getFields().size());
        assertEquals(WriteModeEnum.INSERT, insertOptions.getWriteMode());

        // 测试UPDATE模式
        EdgeExecutionOptions updateOptions = new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace("testSpace")
                .setEdge("friend")
                .setSrcIndex(0)
                .setDstIndex(1)
                .setRankIndex(2)
                .setFields(Arrays.asList("since", "weight"))
                .setPositions(Arrays.asList(3, 4))
                .setWriteMode(WriteModeEnum.UPDATE)
                .setBatch(2)
                .builder();

        assertEquals(WriteModeEnum.UPDATE, updateOptions.getWriteMode());

        // 测试DELETE模式
        EdgeExecutionOptions deleteOptions = new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace("testSpace")
                .setEdge("friend")
                .setSrcIndex(0)
                .setDstIndex(1)
                .setRankIndex(2)
                .setFields(Arrays.asList("since", "weight"))
                .setPositions(Arrays.asList(3, 4))
                .setWriteMode(WriteModeEnum.DELETE)
                .setBatch(2)
                .builder();

        assertEquals(WriteModeEnum.DELETE, deleteOptions.getWriteMode());
    }
}
