package com.geeksec.nta.certificate.util.db.mongo;

import com.geeksec.nta.certificate.util.MongodbUtils;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import org.apache.commons.pool.impl.GenericObjectPool;
import org.bson.Document;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * MongoDB工具类测试
 * 使用Mockito模拟外部依赖
 *
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class MongoDbUtilsTest {

    private static final Logger logger = LoggerFactory.getLogger(MongoDbUtilsTest.class);
    private static final String TEST_OID = "1.3.6.1.1.1";

    @Mock
    private GenericObjectPool<MongoClient> mongoPool;

    @Mock
    private MongoClient mongoClient;

    @Mock
    private MongoDatabase database;

    @Mock
    private MongoCollection<Document> collection;

    @Mock
    private FindIterable<Document> findIterable;

    @Mock
    private Document document;

    @Before
    public void setUp() throws Exception {
        // 模拟MongoDB连接池行为
        when(mongoPool.borrowObject()).thenReturn(mongoClient);
        when(mongoPool.getNumIdle()).thenReturn(5);

        // 模拟MongoDB客户端行为
        when(mongoClient.getDatabase(anyString())).thenReturn(database);
        when(database.getCollection(anyString())).thenReturn(collection);
        when(collection.find(any(Document.class))).thenReturn(findIterable);
        when(findIterable.first()).thenReturn(document);
        when(document.getString(anyString())).thenReturn("Test OID Description");
    }

    /**
     * 测试初始化MongoDB连接池
     */
    @Test
    public void testInitMongoPool() {
        // 使用静态方法模拟
        try (MockedStatic<MongodbUtils> mockedStatic = mockStatic(MongodbUtils.class)) {
            // 模拟静态方法行为
            mockedStatic.when(MongodbUtils::initMongoPool).thenReturn(mongoPool);

            // 执行测试
            GenericObjectPool<MongoClient> result = MongodbUtils.initMongoPool();

            // 验证结果
            assertNotNull("MongoDB连接池不应为空", result);
            assertEquals("应该返回模拟的连接池", mongoPool, result);
            assertEquals("MongoDB连接池应该有空闲连接", 5, result.getNumIdle());
        }
    }

    /**
     * 测试获取MongoDB客户端连接
     */
    @Test
    public void testGetClient() throws Exception {
        // 使用静态方法模拟
        try (MockedStatic<MongodbUtils> mockedStatic = mockStatic(MongodbUtils.class)) {
            // 模拟静态方法行为，但允许真实调用
            mockedStatic.when(() -> MongodbUtils.getClient(any(GenericObjectPool.class)))
                    .thenCallRealMethod();

            // 执行测试
            MongoClient result = MongodbUtils.getClient(mongoPool);

            // 验证结果
            assertNotNull("MongoDB客户端不应为空", result);
            assertEquals("应该返回模拟的客户端", mongoClient, result);

            // 验证交互
            verify(mongoPool).borrowObject();
        }
    }

    /**
     * 测试获取OID描述
     */
    @Test
    public void testGetOIDDescription() throws Exception {
        // 使用静态方法模拟
        try (MockedStatic<MongodbUtils> mockedStatic = mockStatic(MongodbUtils.class)) {
            // 模拟静态方法行为，但允许真实调用
            mockedStatic.when(() -> MongodbUtils.getOIDDescription(anyString(), any(MongoClient.class)))
                    .thenCallRealMethod();

            // 执行测试
            String description = MongodbUtils.getOIDDescription(TEST_OID, mongoClient);

            // 验证结果
            assertNotNull("OID描述不应为空", description);
            assertEquals("应该返回模拟的描述", "Test OID Description", description);

            // 验证交互
            verify(mongoClient).getDatabase(anyString());
            verify(database).getCollection(anyString());
            verify(collection).find(any(Document.class));
            verify(findIterable).first();
            verify(document).getString(anyString());
        }
    }

    /**
     * 测试归还MongoDB客户端连接
     */
    @Test
    public void testReturnClient() throws Exception {
        // 使用静态方法模拟
        try (MockedStatic<MongodbUtils> mockedStatic = mockStatic(MongodbUtils.class)) {
            // 模拟静态方法行为，但允许真实调用
            mockedStatic.when(() -> MongodbUtils.returnClient(any(MongoClient.class), any(GenericObjectPool.class)))
                    .thenCallRealMethod();

            // 执行测试
            MongodbUtils.returnClient(mongoClient, mongoPool);

            // 验证交互
            verify(mongoPool).returnObject(mongoClient);
        }
    }
}
