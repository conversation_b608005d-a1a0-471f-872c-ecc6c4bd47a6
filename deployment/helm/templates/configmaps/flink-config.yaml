{{- define "nta.flinkConfigMap" -}}
{{- $jobName := .jobName -}}
{{- $jobConfig := .jobConfig -}}
{{- $namespace := .namespace -}}
{{- $root := .root -}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: flink-{{ $jobName }}-config
  namespace: {{ $namespace }}
  labels:
    {{- include "nta.labels" $root | nindent 4 }}
    app: flink-{{ $jobName }}
data:
  config.properties: |
    # Kafka配置
    kafka.bootstrap.servers={{ $root.Values.infrastructure.kafka.host }}:{{ $root.Values.infrastructure.kafka.port }}
    {{- if $jobConfig.kafka.groupId }}
    kafka.group.id={{ $jobConfig.kafka.groupId }}
    {{- end }}

    {{- if $jobConfig.kafka.topicMeta }}
    kafka.topic.meta={{ $jobConfig.kafka.topicMeta }}
    {{- end }}
    {{- if $jobConfig.kafka.topicJsonConnect }}
    kafka.topic.json.connect={{ $jobConfig.kafka.topicJsonConnect }}
    {{- end }}
    {{- if $jobConfig.kafka.topicJsonHttp }}
    kafka.topic.json.http={{ $jobConfig.kafka.topicJsonHttp }}
    {{- end }}
    {{- if $jobConfig.kafka.topicJsonDns }}
    kafka.topic.json.dns={{ $jobConfig.kafka.topicJsonDns }}
    {{- end }}
    {{- if $jobConfig.kafka.topicJsonSsl }}
    kafka.topic.json.ssl={{ $jobConfig.kafka.topicJsonSsl }}
    {{- end }}
    {{- if $jobConfig.kafka.topicJsonSsh }}
    kafka.topic.json.ssh={{ $jobConfig.kafka.topicJsonSsh }}
    {{- end }}
    {{- if $jobConfig.kafka.topicJsonRlogin }}
    kafka.topic.json.rlogin={{ $jobConfig.kafka.topicJsonRlogin }}
    {{- end }}
    {{- if $jobConfig.kafka.topicJsonTelnet }}
    kafka.topic.json.telnet={{ $jobConfig.kafka.topicJsonTelnet }}
    {{- end }}
    {{- if $jobConfig.kafka.topicJsonRdp }}
    kafka.topic.json.rdp={{ $jobConfig.kafka.topicJsonRdp }}
    {{- end }}
    {{- if $jobConfig.kafka.topicJsonVnc }}
    kafka.topic.json.vnc={{ $jobConfig.kafka.topicJsonVnc }}
    {{- end }}
    {{- if $jobConfig.kafka.topicJsonXdmcp }}
    kafka.topic.json.xdmcp={{ $jobConfig.kafka.topicJsonXdmcp }}
    {{- end }}
    {{- if $jobConfig.kafka.topicJsonNtp }}
    kafka.topic.json.ntp={{ $jobConfig.kafka.topicJsonNtp }}
    {{- end }}
    {{- if $jobConfig.kafka.topicJsonIcmp }}
    kafka.topic.json.icmp={{ $jobConfig.kafka.topicJsonIcmp }}
    {{- end }}
    {{- if $jobConfig.kafka.modelToggleTopic }}
    kafka.modelToggle.topic={{ $jobConfig.kafka.modelToggleTopic }}
    {{- end }}
    {{- if $jobConfig.kafka.modelToggleGroupId }}
    kafka.modelToggle.group.id={{ $jobConfig.kafka.modelToggleGroupId }}
    {{- end }}
    {{- if $jobConfig.kafka.outputTopic }}
    kafka.output.topic={{ $jobConfig.kafka.outputTopic }}
    {{- end }}
    {{- if $jobConfig.kafka.certTopic }}
    cert.kafka.topic.name={{ $jobConfig.kafka.certTopic }}
    {{- end }}
    {{- if $jobConfig.kafka.certGroupId }}
    cert.kafka.group.id={{ $jobConfig.kafka.certGroupId }}
    {{- end }}
    kafka.security.protocol={{ $root.Values.infrastructure.kafka.security.protocol }}
    kafka.sasl.mechanism={{ $root.Values.infrastructure.kafka.security.mechanism }}

    # Elasticsearch配置
    elasticsearch.host={{ $root.Values.infrastructure.elasticsearch.host }}
    elasticsearch.port={{ $root.Values.infrastructure.elasticsearch.port }}

    # Doris配置
    doris.host={{ $root.Values.infrastructure.doris.cluster.name }}-fe.{{ $root.Values.global.namespace }}.svc:{{ $root.Values.infrastructure.doris.fe.queryPort | default "9030" }}
    doris.database=nta
    doris.sink.parallelism={{ $jobConfig.parallelism.dorisSink | default "4" }}

    # MinIO配置 - 用于存储和读取数据
    {{- if $jobConfig.minio }}
    {{- if $jobConfig.minio.enabled }}
    minio.endpoint={{ $jobConfig.minio.endpoint }}
    # 凭据通过环境变量传入，这里不需要配置
    minio.bucket.name={{ $jobConfig.minio.bucket }}
    {{- if $jobConfig.minio.pathPrefix }}
    minio.path.prefix={{ $jobConfig.minio.pathPrefix }}
    {{- end }}
    {{- end }}
    {{- end }}

    # MySQL配置
    mysql.host={{ $root.Values.infrastructure.mysql.host }}
    mysql.port={{ $root.Values.infrastructure.mysql.port }}
    mysql.database=th_analysis
    mysql.url=jdbc:mysql://{{ $root.Values.infrastructure.mysql.host }}:{{ $root.Values.infrastructure.mysql.port }}/th_analysis?useUnicode=true&characterEncoding=UTF-8&userSSL=false&serverTimezone=GMT-8

    # 并行度配置
    {{- if $jobConfig.parallelism.kafkaSource }}
    parallelism.kafka.source={{ $jobConfig.parallelism.kafkaSource }}
    {{- end }}
    {{- if $jobConfig.parallelism.parsing }}
    parallelism.parsing={{ $jobConfig.parallelism.parsing }}
    {{- end }}
    {{- if $jobConfig.parallelism.processing }}
    parallelism.processing={{ $jobConfig.parallelism.processing }}
    {{- end }}
    # Redis配置
    redis.host={{ $root.Values.infrastructure.redis.host }}
    redis.port={{ $root.Values.infrastructure.redis.port }}
    redis.timeout=10000
    redis.key.ttl=604800
    # Redis凭据通过环境变量传入

    # Nebula配置
    nebula.graph.addr={{ $root.Values.infrastructure.nebula.graphd.host }}:{{ $root.Values.infrastructure.nebula.graphd.port }}
    nebula.meta.addr={{ $root.Values.infrastructure.nebula.metad.host }}:{{ $root.Values.infrastructure.nebula.metad.port }}
    nebula.space.name={{ $root.Values.infrastructure.nebula.space.name }}
    nebula.pool.max.conn.size={{ $root.Values.infrastructure.nebula.pool.maxConnSize }}
    nebula.pool.min.conn.size={{ $root.Values.infrastructure.nebula.pool.minConnSize }}
    nebula.pool.idle.time={{ $root.Values.infrastructure.nebula.pool.idleTime }}
    nebula.pool.timeout={{ $root.Values.infrastructure.nebula.pool.timeout }}
    nebula.session.size={{ $root.Values.infrastructure.nebula.sessionSize }}

    # Nebula批处理配置
    batch.size={{ $root.Values.infrastructure.nebula.batch.size | default "100" }}
    batch.interval={{ $root.Values.infrastructure.nebula.batch.interval | default "1000" }}
    {{- if $jobConfig.parallelism.elasticsearchSink }}
    parallelism.elasticsearch.sink={{ $jobConfig.parallelism.elasticsearchSink }}
    {{- end }}
    {{- if $jobConfig.parallelism.kafkaSink }}
    parallelism.kafka.sink={{ $jobConfig.parallelism.kafkaSink }}
    {{- end }}
    {{- if $jobConfig.parallelism.kafkaJsonSink }}
    parallelism.kafka.json-sink={{ $jobConfig.parallelism.kafkaJsonSink }}
    {{- end }}
    {{- if $jobConfig.parallelism.connectInfoUpdate }}
    parallelism.connect-info.update={{ $jobConfig.parallelism.connectInfoUpdate }}
    {{- end }}
{{- end -}}

{{- if .Values.infrastructure.flink.enabled }}
{{- $namespace := .Values.global.namespace }}

{{- if .Values.features.graph.enabled }}
{{- $configData := dict "jobName" "flink-graph-builder" "jobConfig" .Values.infrastructure.flink.jobs.graph-builder.config "namespace" $namespace "root" . -}}
{{- include "nta.flinkConfigMap" $configData }}
---
{{- end }}

{{- if .Values.infrastructure.flink.jobs.threat-detector.enabled }}
{{- $configData := dict "jobName" "flink-threat-detector" "jobConfig" .Values.infrastructure.flink.jobs.threat-detector.config "namespace" $namespace "root" . -}}
{{- include "nta.flinkConfigMap" $configData }}
---
{{- end }}

{{- if .Values.infrastructure.flink.jobs.certificate-analyzer.enabled }}
{{- $configData := dict "jobName" "flink-certificate-analyzer" "jobConfig" .Values.infrastructure.flink.jobs.certificate-analyzer.config "namespace" $namespace "root" . -}}
{{- include "nta.flinkConfigMap" $configData }}
---
{{- end }}

{{- if .Values.infrastructure.flink.jobs.data-warehouse-processor.enabled }}
{{- $configData := dict "jobName" "flink-data-warehouse-processor" "jobConfig" .Values.infrastructure.flink.jobs.data-warehouse-processor.config "namespace" $namespace "root" . -}}
{{- include "nta.flinkConfigMap" $configData }}
---
{{- end }}

{{- end }}
