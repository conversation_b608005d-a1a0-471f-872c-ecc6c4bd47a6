{{- if .Values.features.graph.enabled -}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: nebula-init-config
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
data:
  nebula_init.ngql: |
    # 使用指定的图空间
    USE {{ .Values.infrastructure.nebula.space.name }};

    # 创建标签（Tags）
    CREATE TAG IF NOT EXISTS IP(ip STRING, version FIXED_STRING(5), city FIXED_STRING(20), country FIXED_STRING(20), threat_score INT8, trust_score INT8, remark STRING);
    CREATE TAG IF NOT EXISTS MAC(mac STRING, vlan_info STRING, threat_score INT8, trust_score INT8, remark STRING);
    CREATE TAG IF NOT EXISTS APPSERVICE(ip STRING, app_name STRING, dport INT32, ip_protocol STRING, threat_score INT8, trust_score INT8);
    CREATE TAG IF NOT EXISTS DOMAIN(domain STRING, alexa_rank INT64, threat_score INT8, trust_score INT8, remark STRING, whois STRING);
    CREATE TAG IF NOT EXISTS REGISTRABLE_DOMAIN(registrable_domain STRING, threat_score INT8, trust_score INT8) COMMENT "可注册域 (Registrable Domain，从完整域名中提取的用户可直接注册的部分)";
    CREATE TAG IF NOT EXISTS CERT(cert_md5 STRING, not_before STRING, not_after STRING, subject_alt_names STRING, issuer_alt_names STRING, key_usage STRING, extended_key_usage STRING, is_ca BOOL, path_length INT32, crl_distribution_points STRING, ocsp_url STRING, issuer_cert_url STRING, cert_policies STRING, signature_algorithm STRING, hash_algorithm STRING, authority_key_identifier STRING, subject_key_identifier STRING, threat_score INT8, trust_score INT8, source_type INT32, remark STRING);
    CREATE TAG IF NOT EXISTS ISSUER(country STRING NULL COMMENT "国家C", common_name STRING NULL COMMENT "通用名称CN", organization STRING NULL COMMENT "组织名称O", org_unit STRING NULL COMMENT "组织单位OU", state STRING NULL COMMENT "州/省ST", locality STRING NULL COMMENT "地区L", email STRING NULL COMMENT "邮箱地址E", serial_number STRING NULL COMMENT "序列号", street_address STRING NULL COMMENT "街道地址", postal_code STRING NULL COMMENT "邮政编码", business_category STRING NULL COMMENT "业务类别", description STRING NULL COMMENT "描述", threat_score INT8, trust_score INT8);
    CREATE TAG IF NOT EXISTS SUBJECT(country STRING NULL COMMENT "国家C", common_name STRING NULL COMMENT "通用名称CN", organization STRING NULL COMMENT "组织名称O", org_unit STRING NULL COMMENT "组织单位OU", state STRING NULL COMMENT "州/省ST", locality STRING NULL COMMENT "地区L", email STRING NULL COMMENT "邮箱地址E", serial_number STRING NULL COMMENT "序列号", street_address STRING NULL COMMENT "街道地址", postal_code STRING NULL COMMENT "邮政编码", business_category STRING NULL COMMENT "业务类别", description STRING NULL COMMENT "描述", threat_score INT8, trust_score INT8);
    CREATE TAG IF NOT EXISTS ORG(org_name STRING, org_desc STRING, threat_score INT8, trust_score INT8, remark STRING);
    CREATE TAG IF NOT EXISTS SSLFINGERPRINT(ja3_hash STRING, finger_desc STRING, type INT8, threat_score INT8, trust_score INT8);
    CREATE TAG IF NOT EXISTS UA(ua STRING, ua_desc STRING, browser_engine STRING, browser_version STRING, is_bot BOOL, bot_name STRING, bot_category STRING, layout_engine STRING, layout_engine_version STRING, agent_class STRING, threat_score INT8, trust_score INT8);
    CREATE TAG IF NOT EXISTS DEVICE(device_name STRING, device_brand STRING, device_model STRING, device_category STRING, threat_score INT8, trust_score INT8);
    CREATE TAG IF NOT EXISTS OS(os_name STRING, os_version STRING, os_major_version STRING, os_minor_version STRING, os_class STRING, threat_score INT8, trust_score INT8);
    CREATE TAG IF NOT EXISTS APP(app_name STRING, app_version STRING, threat_score INT8, trust_score INT8);
    CREATE TAG IF NOT EXISTS URL(url STRING, trust_score INT8, threat_score INT8);
    CREATE TAG IF NOT EXISTS ATTACKER() COMMENT "攻击者角色标记";
    CREATE TAG IF NOT EXISTS VICTIM() COMMENT "受害者角色标记";

    # 创建边类型（Edges）

    # 网络层关系（IP、MAC）
    CREATE EDGE IF NOT EXISTS ip_maps_to_mac() COMMENT "IP与MAC地址映射关系";
    CREATE EDGE IF NOT EXISTS mac_connects_to_mac() COMMENT "链路层连接";
    CREATE EDGE IF NOT EXISTS ip_connects_to_ip(dport INT32) COMMENT "网络层连接";
    CREATE EDGE IF NOT EXISTS attacks() COMMENT "攻击者攻击目标";

    # 域名结构关系
    CREATE EDGE IF NOT EXISTS domain_derives_from_registrable_domain(is_sni BOOL DEFAULT false) COMMENT "域名派生自可注册域，is_sni表示是否为TLS会话中的SNI域名";
    CREATE EDGE IF NOT EXISTS cname_points_to_domain() COMMENT "CNAME指向目标域名";

    # DNS解析关系
    CREATE EDGE IF NOT EXISTS client_queries_domain(dns_type INT32, answer_type INT32) COMMENT "DNS查询域名";
    CREATE EDGE IF NOT EXISTS client_queries_dns_server(dns_type INT32, answer_type INT32) COMMENT "向DNS服务器查询";
    CREATE EDGE IF NOT EXISTS dns_server_resolves_domain(dns_type STRING, answer_type INT32) COMMENT "DNS服务器解析域名";
    CREATE EDGE IF NOT EXISTS domain_resolves_to_ip(record_type STRING DEFAULT "A", final_parse BOOL DEFAULT true, max_ttl INT32 DEFAULT 0, min_ttl INT32 DEFAULT 0) COMMENT "域名解析到IP，record_type可为'A'或'AAAA'等";

    # HTTP/TLS会话关系
    CREATE EDGE IF NOT EXISTS client_http_requests_domain() COMMENT "HTTP客户端访问域名";
    CREATE EDGE IF NOT EXISTS server_http_serves_domain() COMMENT "HTTP服务端提供域名";
    CREATE EDGE IF NOT EXISTS client_tls_requests_domain() COMMENT "TLS客户端访问域名";
    CREATE EDGE IF NOT EXISTS server_tls_hosts_domain() COMMENT "TLS服务端托管域名";

    # 证书相关关系
    CREATE EDGE IF NOT EXISTS ip_provides_cert(role STRING DEFAULT "server", sni STRING DEFAULT "") COMMENT "IP提供证书，role可为'client'或'server'，表示在网络会话中观察到的IP与证书的关系";
    CREATE EDGE IF NOT EXISTS client_receives_cert(sni STRING) COMMENT "客户端在TLS会话中接收/验证服务端的证书";
    CREATE EDGE IF NOT EXISTS cert_has_issuer() COMMENT "证书包含颁发者信息";
    CREATE EDGE IF NOT EXISTS cert_has_subject() COMMENT "证书包含主体信息";
    CREATE EDGE IF NOT EXISTS cert_signs_cert() COMMENT "CA证书签发其他证书";
    CREATE EDGE IF NOT EXISTS cert_covers_domain() COMMENT "证书覆盖特定域名";
    CREATE EDGE IF NOT EXISTS cert_covers_registrable_domain() COMMENT "证书覆盖可注册域";
    CREATE EDGE IF NOT EXISTS cert_serves_sni() COMMENT "证书用于服务特定SNI域名";
    CREATE EDGE IF NOT EXISTS cert_secures_connection_to_url() COMMENT "证书为URL连接提供安全保障";

    # TLS指纹关系
    CREATE EDGE IF NOT EXISTS ip_presents_fingerprint(role STRING) COMMENT "IP呈现特定TLS指纹特征，role可为client或server";
    CREATE EDGE IF NOT EXISTS fingerprint_appears_with_cert(sni STRING) COMMENT "TLS指纹与特定证书在同一TLS会话中出现";
    CREATE EDGE IF NOT EXISTS fingerprint_appears_with_domain() COMMENT "TLS指纹与特定域名在同一TLS会话中出现";
    CREATE EDGE IF NOT EXISTS fingerprint_identifies_app() COMMENT "TLS指纹识别特定应用程序";

    # 应用服务关系
    CREATE EDGE IF NOT EXISTS client_accesses_app() COMMENT "客户端访问应用服务";
    CREATE EDGE IF NOT EXISTS app_deploys_on_server() COMMENT "应用部署在服务器";
    CREATE EDGE IF NOT EXISTS app_uses_cert() COMMENT "应用程序使用证书";
    CREATE EDGE IF NOT EXISTS app_uses_domain() COMMENT "应用程序使用域名";
    CREATE EDGE IF NOT EXISTS app_uses_ip() COMMENT "应用程序使用IP地址";

    # UA/设备/OS关系
    CREATE EDGE IF NOT EXISTS client_uses_ua() COMMENT "客户端UA标识";
    CREATE EDGE IF NOT EXISTS ua_requests_domain() COMMENT "UA请求域名";
    CREATE EDGE IF NOT EXISTS ua_has_device() COMMENT "UA包含设备信息";
    CREATE EDGE IF NOT EXISTS ua_has_os() COMMENT "UA包含系统信息";
    CREATE EDGE IF NOT EXISTS ua_has_app() COMMENT "UA包含应用信息";

    # 组织所有权关系
    CREATE EDGE IF NOT EXISTS org_owns_domain() COMMENT "组织拥有域名";
    CREATE EDGE IF NOT EXISTS org_owns_registrable_domain() COMMENT "组织拥有可注册域";
    CREATE EDGE IF NOT EXISTS org_owns_ip() COMMENT "组织拥有IP";
    CREATE EDGE IF NOT EXISTS org_owns_cert() COMMENT "组织拥有证书";

    # URL相关关系
    CREATE EDGE IF NOT EXISTS url_contains_domain() COMMENT "URL包含域名";
    CREATE EDGE IF NOT EXISTS ip_provides_url_service() COMMENT "IP提供URL服务";

    # 为非VID属性创建索引
    CREATE TAG INDEX IF NOT EXISTS cert_sha1_index on CERT(cert_sha1(40));
    CREATE TAG INDEX IF NOT EXISTS domain_index on DOMAIN(domain(255));
    CREATE TAG INDEX IF NOT EXISTS issuer_id_index on ISSUER(issuer_id(32));
    CREATE TAG INDEX IF NOT EXISTS subject_id_index on SUBJECT(subject_id(32));
    CREATE TAG INDEX IF NOT EXISTS issuer_common_name_index on ISSUER(common_name(255));
    CREATE TAG INDEX IF NOT EXISTS subject_common_name_index on SUBJECT(common_name(255));
    CREATE TAG INDEX IF NOT EXISTS org_org_name_index on ORG(org_name(255));
    CREATE TAG INDEX IF NOT EXISTS issuer_country_index on ISSUER(country(50));
    CREATE TAG INDEX IF NOT EXISTS issuer_organization_index on ISSUER(organization(100));
    CREATE TAG INDEX IF NOT EXISTS issuer_org_unit_index on ISSUER(org_unit(100));
    CREATE TAG INDEX IF NOT EXISTS issuer_email_index on ISSUER(email(100));
    CREATE TAG INDEX IF NOT EXISTS subject_country_index on SUBJECT(country(50));
    CREATE TAG INDEX IF NOT EXISTS subject_organization_index on SUBJECT(organization(100));
    CREATE TAG INDEX IF NOT EXISTS subject_org_unit_index on SUBJECT(org_unit(100));
    CREATE TAG INDEX IF NOT EXISTS subject_email_index on SUBJECT(email(100));
    CREATE TAG INDEX IF NOT EXISTS cert_signature_algorithm_index on CERT(signature_algorithm(50));
    CREATE TAG INDEX IF NOT EXISTS cert_is_ca_index on CERT(is_ca);
    CREATE TAG INDEX IF NOT EXISTS ip_index on IP(ip(50));
    CREATE TAG INDEX IF NOT EXISTS appservice_appname_index on APPSERVICE(app_name(50));
    CREATE TAG INDEX IF NOT EXISTS sslfingerprint_ja3_hash_index on SSLFINGERPRINT(ja3_hash(64));
    CREATE TAG INDEX IF NOT EXISTS ua_index on UA(ua(255));
    CREATE TAG INDEX IF NOT EXISTS ua_browser_engine_index on UA(browser_engine(50));
    CREATE TAG INDEX IF NOT EXISTS ua_browser_version_index on UA(browser_version(50));
    CREATE TAG INDEX IF NOT EXISTS ua_bot_name_index on UA(bot_name(100));
    CREATE TAG INDEX IF NOT EXISTS ua_agent_class_index on UA(agent_class(50));
    CREATE TAG INDEX IF NOT EXISTS device_brand_index on DEVICE(device_brand(100));
    CREATE TAG INDEX IF NOT EXISTS device_model_index on DEVICE(device_model(100));
    CREATE TAG INDEX IF NOT EXISTS device_category_index on DEVICE(device_category(50));
    CREATE TAG INDEX IF NOT EXISTS os_version_index on OS(os_version(50));
    CREATE TAG INDEX IF NOT EXISTS os_class_index on OS(os_class(50));
    CREATE TAG INDEX IF NOT EXISTS app_app_name_index on APP(app_name(50));
    CREATE TAG INDEX IF NOT EXISTS url_index on URL(url(255));
{{- end -}}
