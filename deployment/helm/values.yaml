# NTA 3.0 Helm Chart Values
# 主配置文件 - 导入所有子配置文件

# 导入说明：
# 此文件为主配置文件，仅包含最基本的配置和组件开关
# 详细配置已拆分到 values/ 目录下的多个文件中
# 使用 helm install/upgrade 时，可以通过 -f values/values-xxx.yaml 指定额外的配置文件

# 全局配置
# 详细配置请参考 values/values-global.yaml
# 此处仅包含最基本的配置，更多配置请参考 values-global.yaml
global:
  # Docker registry where images are stored
  registry: hb.gs.lan
  # Image tag to use for all services
  tag: 3.0.0

# 功能特性开关
# 详细配置请参考 values/values-global.yaml
features:
  # 图谱功能
  graph:
    enabled: true
  # 监控功能
  monitoring:
    enabled: true

# 服务配置
# 详细配置请参考 values/values-services.yaml
services:
  # 服务列表
  frontend:
    enabled: true
  auth-service:
    enabled: true
  analysis-service:
    enabled: true
  graph-service:
    enabled: true
  search-service:
    enabled: true
  notification-service:
    enabled: true
  task-service:
    enabled: true
  config-service:
    enabled: true
  system-service:
    enabled: true
  security-service:
    enabled: true

# 基础设施配置
# 详细配置请参考 values/values-infrastructure.yaml
infrastructure:
  # 基础设施组件列表
  mysql:
    enabled: true
  redis:
    enabled: true
  elasticsearch:
    enabled: true
  nebula:
    enabled: true
  doris:
    enabled: true
  kafka:
    enabled: true
  minio:
    enabled: true
  flink:
    enabled: true

# Istio配置
# 详细配置请参考 values/values-istio.yaml
istio:
  enabled: true

# 监控配置
# 详细配置请参考 values/values-monitoring.yaml
monitoring:
  enabled: true

# 初始化配置
# 详细配置请参考 values/values-global.yaml
initialization:
  enabled: true

# 元数据配置
# 详细配置请参考 values/values-metadata.yaml
metadata:
  # 元数据版本管理
  version: "1.0.0"
  # 自动更新配置
  autoUpdate:
    enabled: true
    schedule: "0 2 * * *"
  # 外部数据源配置
  dataSources:
    tranco:
      enabled: true
    geoip:
      enabled: true
    publicSuffix:
      enabled: true
    maliciousDomains:
      enabled: true

# Operator配置
# 详细配置请参考 values/values-operators.yaml
eck-operator:
  enabled: true
flink-kubernetes-operator:
  enabled: true
strimzi-kafka-operator:
  enabled: true
nebula-operator:
  enabled: true
doris-operator:
  enabled: true
minio-operator:
  enabled: true
