#!/bin/bash
# update-knowledge-base.sh
# 统一的知识库管理脚本
# 用于自动下载、部署、验证各种知识库数据文件
#
# 使用方法:
#   ./update-knowledge-base.sh                    # 完整更新流程
#   ./update-knowledge-base.sh --verify          # 只验证系统状态
#   UPDATE_SOURCES=ip-whois ./update-knowledge-base.sh  # 更新特定数据源
#
# 功能:
#   1. 下载各种知识库数据 (Tranco, GeoIP, IP WHOIS, 恶意域名等)
#   2. 保存到 files/knowledge-base/ 目录
#   3. 部署到 PersistentVolume
#   4. 重启 Flink 作业
#   5. 备份到 MinIO
#   6. 验证系统状态

set -euo pipefail

# 配置变量
NAMESPACE="${NAMESPACE:-nta-3.0}"
CONFIGMAP_PREFIX="${CONFIGMAP_PREFIX:-nta-3.0}"
UPDATE_SOURCES="${UPDATE_SOURCES:-tranco,geoip,public-suffix,malicious-domains,ip-whois}"
TEMP_DIR="${TEMP_DIR:-/tmp/metadata}"
MINIO_ENDPOINT="${MINIO_ENDPOINT:-http://minio.nta-3.0.svc.cluster.local:9000}"
MINIO_BUCKET="${MINIO_BUCKET:-nta-data}"

# PersistentVolume部署配置
KNOWLEDGE_BASE_SOURCE_DIR="files/knowledge-base"
KNOWLEDGE_BASE_TARGET_BASE_DIR="knowledge-base"

# 固定文件映射配置
declare -A FIXED_FILE_MAPPINGS=(
    # GeoIP数据
    ["GeoLite2-City.mmdb"]="geoip/"
    ["GeoLite2-ASN.mmdb"]="geoip/"

    # Tranco数据
    ["tranco_NNJPW.csv"]="tranco/"

    # Public Suffix List
    ["public_suffix_list.dat"]="public-suffix/"

    # 恶意域名数据
    ["full-domains-aa.txt"]="malicious-domains/"
    ["full-domains-ab.txt"]="malicious-domains/"
    ["full-domains-ac.txt"]="malicious-domains/"

    # 域名WHOIS数据
    ["sample_whois_db_download.csv"]="domain-whois/"
)

# 动态文件模式配置
declare -A DYNAMIC_FILE_PATTERNS=(
    # IP WHOIS数据（文件名包含日期，需要动态查找）
    ["ip_whois_data_*.jsonl"]="ip-whois/"
    ["all_objects_pytricia_inetnum_*.jsonl"]="ip-whois/"
)

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $*"
}

error() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $*" >&2
}

success() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] ✅ $*"
}

warning() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] ⚠️  $*"
}

# 创建临时目录
mkdir -p "$TEMP_DIR"
cd "$TEMP_DIR"

# 更新Tranco域名排名
update_tranco() {
    log "开始更新Tranco域名排名数据..."

    local list_id="${TRANCO_LIST_ID:-NNJPW}"
    local url="https://tranco-list.eu/download/${list_id}/1000000"

    if curl -L -o "tranco_${list_id}.csv" "$url"; then
        log "Tranco数据下载成功"

        # 保存到标准位置
        local target_dir="$KNOWLEDGE_BASE_SOURCE_DIR"
        mkdir -p "$target_dir"
        cp "tranco_${list_id}.csv" "$target_dir/"

        log "Tranco数据已保存到: $target_dir/tranco_${list_id}.csv"
    else
        error "Tranco数据下载失败"
        return 1
    fi
}

# 更新GeoIP数据库
update_geoip() {
    log "开始更新GeoIP数据库..."

    local city_url="https://git.io/GeoLite2-City.mmdb"
    local asn_url="https://git.io/GeoLite2-ASN.mmdb"
    local download_success=0

    # 下载City数据库
    log "正在下载GeoLite2-City数据库从 ${city_url}..."
    if curl -L -f -o "GeoLite2-City.mmdb" "${city_url}"; then
        log "GeoLite2-City数据库下载成功"

        # 验证文件是否为有效的MMDB文件
        if file "GeoLite2-City.mmdb" | grep -q "data"; then
            local file_size=$(stat -f%z "GeoLite2-City.mmdb" 2>/dev/null || stat -c%s "GeoLite2-City.mmdb" 2>/dev/null)
            log "GeoLite2-City数据库大小: ${file_size} bytes"
            download_success=$((download_success + 1))

            # 保存到标准位置
            local target_dir="$KNOWLEDGE_BASE_SOURCE_DIR"
            mkdir -p "$target_dir"
            cp "GeoLite2-City.mmdb" "$target_dir/"
        else
            error "GeoLite2-City数据库文件格式无效"
            return 1
        fi
    else
        error "GeoLite2-City数据库下载失败"
        return 1
    fi

    # 下载ASN数据库
    log "正在下载GeoLite2-ASN数据库从 ${asn_url}..."
    if curl -L -f -o "GeoLite2-ASN.mmdb" "${asn_url}"; then
        log "GeoLite2-ASN数据库下载成功"

        # 验证文件是否为有效的MMDB文件
        if file "GeoLite2-ASN.mmdb" | grep -q "data"; then
            local file_size=$(stat -f%z "GeoLite2-ASN.mmdb" 2>/dev/null || stat -c%s "GeoLite2-ASN.mmdb" 2>/dev/null)
            log "GeoLite2-ASN数据库大小: ${file_size} bytes"
            download_success=$((download_success + 1))

            # 保存到标准位置
            cp "GeoLite2-ASN.mmdb" "$target_dir/"
        else
            error "GeoLite2-ASN数据库文件格式无效"
            return 1
        fi
    else
        error "GeoLite2-ASN数据库下载失败"
        return 1
    fi

    # 验证所有数据库都下载成功
    if [[ $download_success -eq 2 ]]; then
        log "GeoIP数据库更新成功"
    else
        error "GeoIP数据库更新失败"
        return 1
    fi
}

# 更新Public Suffix List
update_public_suffix() {
    log "开始更新Public Suffix List..."

    local url="https://publicsuffix.org/list/public_suffix_list.dat"

    if curl -L -o "public_suffix_list.dat" "$url"; then
        log "Public Suffix List下载成功"

        # 保存到标准位置
        local target_dir="$KNOWLEDGE_BASE_SOURCE_DIR"
        mkdir -p "$target_dir"
        cp "public_suffix_list.dat" "$target_dir/"

        log "Public Suffix List已保存到: $target_dir/public_suffix_list.dat"
    else
        error "Public Suffix List下载失败"
        return 1
    fi
}

# 更新恶意域名列表
update_malicious_domains() {
    log "开始更新恶意域名列表..."

    # GitHub恶意域名数据源
    local base_url="https://raw.githubusercontent.com/romainmarcoux/malicious-domains/main"
    local files=("full-domains-aa.txt" "full-domains-ab.txt" "full-domains-ac.txt")
    local download_success=0

    # 下载各个分片文件
    for file in "${files[@]}"; do
        local url="${base_url}/${file}"
        log "正在下载 ${file} 从 ${url}..."

        if curl -L -f -o "${file}" "${url}"; then
            log "${file} 下载成功"
            download_success=$((download_success + 1))

            # 验证文件内容（检查是否为空或格式错误）
            if [[ -s "${file}" ]]; then
                local line_count=$(wc -l < "${file}")
                log "${file} 包含 ${line_count} 行域名"
            else
                error "${file} 文件为空"
                return 1
            fi
        else
            error "${file} 下载失败"
            return 1
        fi
    done

    # 验证所有文件都下载成功
    if [[ $download_success -eq ${#files[@]} ]]; then
        log "所有恶意域名文件下载成功"

        # 保存到标准位置
        local target_dir="$KNOWLEDGE_BASE_SOURCE_DIR"
        mkdir -p "$target_dir"

        for file in "${files[@]}"; do
            cp "$file" "$target_dir/"
            log "恶意域名文件已保存: $target_dir/$file"
        done
    else
        error "部分恶意域名文件下载失败"
        return 1
    fi
}

# 更新IP WHOIS数据
update_ip_whois() {
    log "开始更新IP WHOIS数据..."

    # OpenINTEL数据源配置
    local base_url="https://object.openintel.nl/rir-data/whois-formatted/type=enriched"

    # 获取当天日期
    local current_date=$(date +%Y-%m-%d)
    local current_year=$(date +%Y)
    local current_month=$(date +%m)
    local current_day=$(date +%d)

    log "尝试下载 ${current_date} 的IP WHOIS数据..."

    # 构建文件名和URL
    local start_time="${current_year}${current_month}${current_day}00"
    local end_time="${current_year}${current_month}${current_day}23"
    local filename="all_objects_pytricia_inetnum_${start_time}_${end_time}.jsonl.bz2"
    local url="${base_url}/year=${current_year}/month=${current_month}/day=${current_day}/hour=20/${filename}"

    log "尝试下载: ${url}"

    # 检查URL是否可访问
    if curl -I -L -f --connect-timeout 10 --max-time 30 "${url}" >/dev/null 2>&1; then
        log "找到当天的数据文件，开始下载..."
        if curl -L -f --connect-timeout 30 --max-time 600 -o "${filename}" "${url}"; then
            log "IP WHOIS压缩文件下载成功: ${filename}"
            # 保存到标准位置
            local target_dir="$KNOWLEDGE_BASE_SOURCE_DIR"
            mkdir -p "${target_dir}"
            local target_bz2="${target_dir}/${filename}"
            mv "${filename}" "${target_bz2}"
            log "IP WHOIS压缩文件已保存到: ${target_bz2}"
            log "IP WHOIS数据下载完成"
            return 0
        else
            error "IP WHOIS数据下载失败"
            return 1
        fi
    else
        log "当天IP WHOIS数据不存在，跳过更新"
        return 1
    fi
}

# 触发Flink作业重启
restart_flink_jobs() {
    log "开始重启相关的Flink作业..."

    # 更新FlinkDeployment的注解以触发重启
    local timestamp=$(date +%s)

    kubectl patch flinkdeployment "${CONFIGMAP_PREFIX}-data-warehouse-processor" \
        --patch "{\"metadata\":{\"annotations\":{\"metadata.updated\":\"${timestamp}\"}}}" \
        --type=merge

    log "Flink作业重启触发成功"
}

# 备份到MinIO
backup_to_minio() {
    log "开始备份元数据到MinIO..."

    if command -v mc >/dev/null 2>&1; then
        # 配置MinIO客户端
        mc alias set minio "$MINIO_ENDPOINT" "$MINIO_ACCESS_KEY" "$MINIO_SECRET_KEY"

        # 创建备份目录
        local backup_dir="knowledge-base-backup/$(date +%Y%m%d-%H%M%S)"

        log "开始备份知识库文件到MinIO..."

        # 备份所有相关文件（包括压缩文件）
        local backup_count=0
        find "$TEMP_DIR" -type f \( \
            -name "*.csv" -o \
            -name "*.txt" -o \
            -name "*.dat" -o \
            -name "*.mmdb" -o \
            -name "*.bz2" \
        \) | while read -r file; do
            if [[ -f "$file" ]]; then
                local relative_path="${file#$TEMP_DIR/}"
                local file_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file" 2>/dev/null)
                local file_size_mb=$((file_size / 1024 / 1024))

                log "备份文件: $relative_path (${file_size_mb}MB)"

                if mc cp "$file" "minio/$MINIO_BUCKET/$backup_dir/$relative_path"; then
                    ((backup_count++))
                    log "✅ $relative_path 备份成功"
                else
                    error "❌ $relative_path 备份失败"
                fi
            fi
        done

        log "知识库备份到MinIO完成: $backup_dir (共备份 $backup_count 个文件)"
    else
        log "MinIO客户端未安装，跳过备份"
    fi
}

# PersistentVolume操作函数

# 检查PVC是否存在
check_pvc() {
    if ! kubectl get pvc flink-data-pvc -n "$NAMESPACE" >/dev/null 2>&1; then
        error "PersistentVolumeClaim 'flink-data-pvc' 不存在于命名空间 '$NAMESPACE'"
        return 1
    fi
    log "PVC检查通过"
}

# 创建临时Pod
create_metadata_copy_pod() {
    log "创建临时Pod..."
    kubectl run metadata-copy-pod \
        --image=busybox:latest \
        --restart=Never \
        --overrides="{
            \"spec\": {
                \"containers\": [{
                    \"name\": \"copy-container\",
                    \"image\": \"busybox:latest\",
                    \"command\": [\"sh\", \"-c\", \"mkdir -p /data/$KNOWLEDGE_BASE_TARGET_BASE_DIR && sleep 3600\"],
                    \"volumeMounts\": [{
                        \"name\": \"flink-data\",
                        \"mountPath\": \"/data\"
                    }]
                }],
                \"volumes\": [{
                    \"name\": \"flink-data\",
                    \"persistentVolumeClaim\": {
                        \"claimName\": \"flink-data-pvc\"
                    }
                }]
            }
        }" \
        --namespace="$NAMESPACE"

    # 等待Pod启动
    log "等待Pod启动..."
    kubectl wait --for=condition=Ready pod/metadata-copy-pod -n "$NAMESPACE" --timeout=60s
}

# 复制单个文件到PersistentVolume
copy_file_to_pv() {
    local source_file="$1"
    local target_subdir="$2"
    local full_source_path="$KNOWLEDGE_BASE_SOURCE_DIR/$source_file"

    if [[ ! -f "$full_source_path" ]]; then
        error "源文件不存在: $full_source_path"
        return 1
    fi

    local file_size=$(stat -f%z "$full_source_path" 2>/dev/null || stat -c%s "$full_source_path" 2>/dev/null)
    log "复制文件: $source_file (大小: $file_size bytes) -> $target_subdir"

    # 创建目标目录
    kubectl exec metadata-copy-pod -n "$NAMESPACE" -- \
        mkdir -p "/data/$KNOWLEDGE_BASE_TARGET_BASE_DIR/$target_subdir"

    # 复制文件
    kubectl cp "$full_source_path" "$NAMESPACE/metadata-copy-pod:/data/$KNOWLEDGE_BASE_TARGET_BASE_DIR/$target_subdir/"

    # 如果是bz2文件，自动解压
    if [[ "$source_file" == *.bz2 ]]; then
        local compressed_filename=$(basename "$source_file")
        local decompressed_filename="${compressed_filename%.bz2}"

        log "解压文件: $compressed_filename -> $decompressed_filename"
        kubectl exec metadata-copy-pod -n "$NAMESPACE" -- \
            sh -c "bzip2 -dc '/data/$KNOWLEDGE_BASE_TARGET_BASE_DIR/$target_subdir/$compressed_filename' > '/data/$KNOWLEDGE_BASE_TARGET_BASE_DIR/$target_subdir/$decompressed_filename'"

        # 删除压缩文件（PersistentVolume中只保留解压后的文件）
        kubectl exec metadata-copy-pod -n "$NAMESPACE" -- \
            rm -f "/data/$KNOWLEDGE_BASE_TARGET_BASE_DIR/$target_subdir/$compressed_filename"

        log "文件解压完成: $decompressed_filename"
    fi

    return 0
}

# 验证复制结果
verify_pv_files() {
    log "验证复制结果..."
    kubectl exec metadata-copy-pod -n "$NAMESPACE" -- \
        find "/data/$METADATA_TARGET_BASE_DIR" -type f -exec ls -lh {} \;
}

# 清理临时Pod
cleanup_metadata_copy_pod() {
    log "清理临时Pod..."
    kubectl delete pod metadata-copy-pod --namespace="$NAMESPACE" --ignore-not-found=true
}

# 部署元数据到PersistentVolume
deploy_metadata_to_pv() {
    log "开始部署元数据文件到PersistentVolume..."

    # 检查前置条件
    if ! check_pvc; then
        return 1
    fi

    # 创建临时Pod
    create_metadata_copy_pod

    local success_count=0
    local total_count=$((${#FIXED_FILE_MAPPINGS[@]} + ${#DYNAMIC_FILE_PATTERNS[@]}))

    # 复制固定文件
    for source_file in "${!FIXED_FILE_MAPPINGS[@]}"; do
        target_subdir="${FIXED_FILE_MAPPINGS[$source_file]}"

        if copy_file_to_pv "$source_file" "$target_subdir"; then
            ((success_count++))
            log "✅ $source_file 复制成功"
        else
            log "⚠️  $source_file 复制失败或文件不存在，跳过"
        fi
    done

    # 复制动态文件（按模式查找）
    for pattern in "${!DYNAMIC_FILE_PATTERNS[@]}"; do
        target_subdir="${DYNAMIC_FILE_PATTERNS[$pattern]}"

        # 查找匹配的文件
        local found_files=()
        while IFS= read -r -d '' file; do
            found_files+=("$(basename "$file")")
        done < <(find "$KNOWLEDGE_BASE_SOURCE_DIR" -name "$pattern" -type f -print0 2>/dev/null)

        if [[ ${#found_files[@]} -eq 0 ]]; then
            log "⚠️  未找到匹配模式 $pattern 的文件，跳过"
            continue
        fi

        # 复制找到的文件
        for source_file in "${found_files[@]}"; do
            if copy_file_to_pv "$source_file" "$target_subdir"; then
                ((success_count++))
                log "✅ $source_file 复制成功"
            else
                error "❌ $source_file 复制失败"
            fi
        done
    done

    # 验证结果
    verify_pv_files

    # 清理
    cleanup_metadata_copy_pod

    # 总结
    log "部署完成: $success_count 个文件成功"

    if [[ $success_count -gt 0 ]]; then
        log "🎉 元数据文件部署到PersistentVolume成功！"
        return 0
    else
        error "没有文件成功部署到PersistentVolume"
        return 1
    fi
}

# 验证功能

# 检查ConfigMap是否存在
verify_configmaps() {
    log "检查ConfigMap是否存在..."

    local configmap="${CONFIGMAP_PREFIX}-metadata-config"

    if kubectl get configmap "$configmap" -n "$NAMESPACE" >/dev/null 2>&1; then
        success "ConfigMap $configmap 存在"
        return 0
    else
        error "ConfigMap $configmap 不存在"
        return 1
    fi
}

# 检查ConfigMap内容
verify_configmap_content() {
    log "检查ConfigMap内容..."

    local configmap="${CONFIGMAP_PREFIX}-metadata-config"

    # 检查小文件
    local small_files=(
        "public_suffix_list.dat"
    )

    for file in "${small_files[@]}"; do
        if kubectl get configmap "$configmap" -n "$NAMESPACE" -o jsonpath="{.data['$file']}" >/dev/null 2>&1; then
            local size=$(kubectl get configmap "$configmap" -n "$NAMESPACE" -o jsonpath="{.data['$file']}" | wc -c)
            success "文件 $file 存在，大小: $size bytes"
        else
            warning "文件 $file 在ConfigMap中不存在"
        fi
    done

    # 检查配置文件
    local config_files=(
        "geoip.config"
        "ip.whois.config"
        "tranco.config"
        "malicious.domains.config"
    )

    for file in "${config_files[@]}"; do
        if kubectl get configmap "$configmap" -n "$NAMESPACE" -o jsonpath="{.data['$file']}" >/dev/null 2>&1; then
            success "配置文件 $file 存在"
        else
            warning "配置文件 $file 在ConfigMap中不存在"
        fi
    done
}

# 检查PersistentVolume中的文件
verify_pv_files() {
    log "检查PersistentVolume中的文件..."

    # 创建临时Pod进行检查
    kubectl run metadata-verify-pod \
        --image=busybox:latest \
        --restart=Never \
        --rm -i \
        --overrides="{
            \"spec\": {
                \"containers\": [{
                    \"name\": \"verify-container\",
                    \"image\": \"busybox:latest\",
                    \"command\": [\"sh\", \"-c\", \"sleep 300\"],
                    \"volumeMounts\": [{
                        \"name\": \"flink-data\",
                        \"mountPath\": \"/data\"
                    }]
                }],
                \"volumes\": [{
                    \"name\": \"flink-data\",
                    \"persistentVolumeClaim\": {
                        \"claimName\": \"flink-data-pvc\"
                    }
                }]
            }
        }" \
        --namespace="$NAMESPACE" &

    # 等待Pod启动
    sleep 10

    # 检查各个目录
    local directories=(
        "geoip"
        "tranco"
        "public-suffix"
        "malicious-domains"
        "ip-whois"
        "domain-whois"
    )

    for dir in "${directories[@]}"; do
        local path="/data/$KNOWLEDGE_BASE_TARGET_BASE_DIR/$dir"
        if kubectl exec metadata-verify-pod -n "$NAMESPACE" -- test -d "$path" 2>/dev/null; then
            local file_count=$(kubectl exec metadata-verify-pod -n "$NAMESPACE" -- find "$path" -type f | wc -l)
            success "目录 $dir 存在，包含 $file_count 个文件"

            # 显示文件列表
            kubectl exec metadata-verify-pod -n "$NAMESPACE" -- find "$path" -type f -exec ls -lh {} \; | while read -r line; do
                log "  $line"
            done
        else
            warning "目录 $dir 不存在"
        fi
    done

    # 清理临时Pod
    kubectl delete pod metadata-verify-pod --namespace="$NAMESPACE" --ignore-not-found=true
}

# 检查Flink作业挂载
verify_flink_mounts() {
    log "检查Flink作业挂载情况..."

    # 获取Flink作业Pod
    local pods=$(kubectl get pods -n "$NAMESPACE" -l "app=${CONFIGMAP_PREFIX}-data-warehouse-processor" -o jsonpath='{.items[*].metadata.name}')

    if [[ -z "$pods" ]]; then
        warning "未找到Flink作业Pod，可能作业未运行"
        return 0
    fi

    local pod_name=$(echo $pods | awk '{print $1}')
    log "使用Pod: $pod_name"

    # 检查挂载路径
    local mount_paths=(
        "/opt/flink/data/knowledge-base"
    )

    for path in "${mount_paths[@]}"; do
        if kubectl exec -n "$NAMESPACE" "$pod_name" -- ls "$path" >/dev/null 2>&1; then
            local file_count=$(kubectl exec -n "$NAMESPACE" "$pod_name" -- find "$path" -type f | wc -l)
            success "挂载路径 $path 存在，包含 $file_count 个文件"
        else
            error "挂载路径 $path 不存在或不可访问"
        fi
    done
}

# 检查环境变量
verify_environment_variables() {
    log "检查环境变量..."

    local pods=$(kubectl get pods -n "$NAMESPACE" -l "app=${CONFIGMAP_PREFIX}-data-warehouse-processor" -o jsonpath='{.items[*].metadata.name}')

    if [[ -z "$pods" ]]; then
        warning "未找到Flink作业Pod，跳过环境变量检查"
        return 0
    fi

    local pod_name=$(echo $pods | awk '{print $1}')

    local env_vars=(
        "KNOWLEDGE_BASE_DATA_PATH"
        "TRANCO_PATH"
        "GEOIP_PATH"
        "MALICIOUS_DOMAINS_PATH"
        "IP_WHOIS_PATH"
        "DOMAIN_WHOIS_PATH"
    )

    for var in "${env_vars[@]}"; do
        local value=$(kubectl exec -n "$NAMESPACE" "$pod_name" -- printenv "$var" 2>/dev/null || echo "")
        if [[ -n "$value" ]]; then
            success "环境变量 $var = $value"
        else
            warning "环境变量 $var 未设置"
        fi
    done
}

# 检查应用日志
verify_application_logs() {
    log "检查应用日志中的加载信息..."

    local pods=$(kubectl get pods -n "$NAMESPACE" -l "app=${CONFIGMAP_PREFIX}-data-warehouse-processor" -o jsonpath='{.items[*].metadata.name}')

    if [[ -z "$pods" ]]; then
        warning "未找到Flink作业Pod，跳过日志检查"
        return 0
    fi

    local pod_name=$(echo $pods | awk '{print $1}')

    # 查找加载日志
    local load_patterns=(
        "知识库"
        "Public Suffix List"
        "IP WHOIS"
        "GeoIP"
        "Tranco"
    )

    for pattern in "${load_patterns[@]}"; do
        local count=$(kubectl logs -n "$NAMESPACE" "$pod_name" --tail=1000 | grep -c "$pattern" || echo "0")
        if [[ $count -gt 0 ]]; then
            success "找到 $count 条包含 '$pattern' 的日志"
        else
            warning "未找到包含 '$pattern' 的日志"
        fi
    done

    # 显示最近的相关日志
    log "最近的知识库加载日志："
    kubectl logs -n "$NAMESPACE" "$pod_name" --tail=100 | grep -E "(知识库|Public Suffix|WHOIS|GeoIP|Tranco)" | tail -10 || true
}

# 执行完整验证
verify_knowledge_base_system() {
    log "开始验证知识库系统状态..."

    local failed_checks=()

    # 执行各项检查
    if ! verify_configmaps; then
        failed_checks+=("ConfigMap检查")
    fi

    verify_configmap_content || failed_checks+=("ConfigMap内容检查")
    verify_pv_files || failed_checks+=("PersistentVolume文件检查")
    verify_flink_mounts || failed_checks+=("Flink挂载检查")
    verify_environment_variables || failed_checks+=("环境变量检查")
    verify_application_logs || failed_checks+=("应用日志检查")

    # 报告结果
    echo ""
    log "验证完成！"

    if [[ ${#failed_checks[@]} -eq 0 ]]; then
        success "所有检查都通过，元数据资源系统运行正常！"
        echo ""
        log "系统状态总结："
        log "✅ ConfigMap已创建并包含正确的配置"
        log "✅ PersistentVolume包含所有大型数据文件"
        log "✅ Flink作业已正确挂载资源文件"
        log "✅ 环境变量已正确设置"
        log "✅ 应用程序能够正常加载元数据"
        return 0
    else
        error "以下检查失败: ${failed_checks[*]}"
        echo ""
        log "故障排除建议："
        log "1. 检查Helm部署是否成功: helm status $NAMESPACE"
        log "2. 检查ConfigMap: kubectl get configmaps -n $NAMESPACE"
        log "3. 检查PersistentVolume: kubectl get pvc -n $NAMESPACE"
        log "4. 检查Flink作业状态: kubectl get flinkdeployments -n $NAMESPACE"
        log "5. 查看Pod日志: kubectl logs -n $NAMESPACE <pod-name>"
        return 1
    fi
}

# 主函数
main() {
    # 检查是否是验证模式
    if [[ "${1:-}" == "--verify" || "${1:-}" == "-v" ]]; then
        verify_metadata_system
        exit $?
    fi

    log "开始元数据更新任务..."
    log "更新源: $UPDATE_SOURCES"

    local sources
    IFS=',' read -ra sources <<< "$UPDATE_SOURCES"

    local failed_sources=()

    for source in "${sources[@]}"; do
        case "$source" in
            "tranco")
                if ! update_tranco; then
                    failed_sources+=("tranco")
                fi
                ;;
            "geoip")
                if ! update_geoip; then
                    failed_sources+=("geoip")
                fi
                ;;
            "public-suffix")
                if ! update_public_suffix; then
                    failed_sources+=("public-suffix")
                fi
                ;;
            "malicious-domains")
                if ! update_malicious_domains; then
                    failed_sources+=("malicious-domains")
                fi
                ;;
            "ip-whois")
                if ! update_ip_whois; then
                    failed_sources+=("ip-whois")
                fi
                ;;
            *)
                error "未知的更新源: $source"
                failed_sources+=("$source")
                ;;
        esac
    done

    # 如果有成功的更新，则部署到PersistentVolume并重启Flink作业
    if [[ ${#failed_sources[@]} -lt ${#sources[@]} ]]; then
        if deploy_metadata_to_pv; then
            log "元数据部署到PersistentVolume成功"
            restart_flink_jobs
            backup_to_minio
        else
            error "元数据部署到PersistentVolume失败"
            exit 1
        fi
    fi

    # 清理临时文件
    rm -rf "$TEMP_DIR"

    if [[ ${#failed_sources[@]} -eq 0 ]]; then
        log "所有元数据更新成功完成"

        # 自动验证系统状态
        log "开始自动验证系统状态..."
        if verify_metadata_system; then
            log "🎉 元数据更新和验证全部成功！"
            exit 0
        else
            warning "元数据更新成功，但验证发现问题，请检查系统状态"
            exit 1
        fi
    else
        error "以下更新源失败: ${failed_sources[*]}"
        exit 1
    fi
}

# 执行主函数
main "$@"
